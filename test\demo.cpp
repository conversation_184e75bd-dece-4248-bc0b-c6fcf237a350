#include "DCGrayscaleRoute.h"
#include <stdio.h>
#include <iostream>
#include <stdlib.h>
#include <string>
#include <map>
#include <vector>
#include "DCLogMacro.h"

using namespace std;

/**
 * @brief 灰度路由系统测试示例
 *
 * 本示例展示了如何使用灰度路由系统的主要功能：
 * 1. 初始化灰度路由系统
 * 2. 使用GetRouteServiceName方法获取服务名称
 * 3. 使用GetRouteServiceNameDCFClient方法获取服务名称和DCF客户端
 * 4. 使用GetRouteEnvs方法获取路由环境配置
 *
 * 使用方法：
 * ./demo sql配置文件 日志地址:端口 订阅者编码 路由环节
 */
int main(int argc, char **argv)
{
	// 检查命令行参数
	if (argc < 5)
	{
		printf("用法: %s sql.xml 日志地址:端口 订阅者编码 路由环节\n", argv[0]);
		printf("示例: %s ../cfg/GrayscaleRoute.sql.xml 127.0.0.1:9081 SUBSCRIBER ToRate\n", argv[0]);
		return -1;
	}

	// 参数解析
	char *sqlConfigFile = argv[1];
	char *logAddrPort = argv[2];
	char *subscriber = argv[3];
	char *routeProcess = argv[4];

	// 初始化日志系统
	int ret = DCLOGINITNEW("CLOUD", "DCGRAYSCALEROUTETEST", "", "11", "1", 7, routeProcess);
	if (ret < 0)
	{
		printf("日志初始化失败\n");
		return ret;
	}
	DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "灰度路由测试开始, 订阅者: %s, 路由环节: %s", subscriber, routeProcess);

	// 初始化数据库管理器
	DCDBManer *dbm = new DCDBManer();
	ret = dbm->Init(sqlConfigFile);
	if (ret < 0)
	{
		printf("SQL配置文件初始化失败: %s\n", sqlConfigFile);
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "SQL配置文件初始化失败: %s\n", sqlConfigFile);
		delete dbm;
		return ret;
	}

	// 设置路由参数
	map<string, string> routeParams;
	// 基础参数
	routeParams["BillingNum"] = "18900000001"; // 计费号码
	routeParams["SectionNum"] = "18900000001"; // 号段
	routeParams["LatnID"] = "910";			   // 本地网
	routeParams["OperType"] = "DA4G";		   // 业务类型

	// 附加参数
	routeParams["ACCTID"] = "123456";	  // 账户ID
	routeParams["CUSTID"] = "67890";	  // 客户ID
	routeParams["OFFERID"] = "10001";	  // 销售品ID
	routeParams["OFFERINSTID"] = "20001"; // 销售品实例ID

	// 获取灰度路由实例
	DCGrayRoute *pGrayRoute = DCGrayscaleRoute::instance();

	// 初始化灰度路由（加载数据）
	ret = pGrayRoute->init(dbm, subscriber);
	if (ret < 0)
	{
		printf("灰度路由初始化失败, 错误码: %d\n", ret);
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "灰度路由初始化失败, 错误码: %d\n", ret);
		delete dbm;
		return ret;
	}
	DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "灰度路由初始化成功");

	// 示例1: 使用GetRouteServiceName接口获取服务名称
	string serviceName = "NoServer";
	ret = pGrayRoute->GetRouteServiceName(routeProcess, routeParams, serviceName);
	if (ret < 0)
	{
		printf("获取服务名称失败, 错误码: %d\n", ret);
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "获取服务名称失败, 错误码: %d\n", ret);
	}
	else
	{
		printf("获取服务名称成功: %s\n", serviceName.c_str());
		DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "获取服务名称成功: %s\n", serviceName.c_str());
	}

	// 示例2: 使用GetRouteServiceNameDCFClient接口获取服务名称和DCF客户端
	dcf_new::DCFLocalClient *pDCFClient = NULL;
	ret = pGrayRoute->GetRouteServiceNameDCFClient(routeProcess, routeParams, serviceName, pDCFClient);
	if (ret < 0)
	{
		printf("获取DCF客户端失败, 错误码: %d\n", ret);
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "获取DCF客户端失败, 错误码: %d\n", ret);
	}
	else
	{
		if (pDCFClient != NULL)
		{
			printf("获取DCF客户端成功, 服务名称: %s\n", serviceName.c_str());
			DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "获取DCF客户端成功, 服务名称: %s\n", serviceName.c_str());

			// DCF客户端使用示例
			string messageId = "msg_" + to_string(time(NULL));
			string messageContent = "{\"request\":\"test_request\",\"param\":\"test_value\"}";

			// 发送消息（注释掉以避免实际发送）
			// int sendRet = pDCFClient->sendMsg(serviceName, messageId, messageContent);
			// if (sendRet == 0) {
			//     printf("消息发送成功, ID: %s\n", messageId.c_str());
			//     DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "消息发送成功, ID: %s\n", messageId.c_str());
			// } else {
			//     printf("消息发送失败, 错误码: %d\n", sendRet);
			//     DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "消息发送失败, 错误码: %d\n", sendRet);
			// }
		}
		else
		{
			printf("DCF客户端为空, 服务名称: %s\n", serviceName.c_str());
			DCBIZLOG(DCLOG_LEVEL_WARN, 0, "", "DCF客户端为空, 服务名称: %s\n", serviceName.c_str());
		}
	}

	// 示例3: 获取路由环境配置
	vector<St_route_env> routeEnvs;
	ret = pGrayRoute->GetRouteEnvs(routeProcess, routeEnvs);
	if (ret < 0)
	{
		printf("获取路由环境配置失败, 错误码: %d\n", ret);
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "获取路由环境配置失败, 错误码: %d\n", ret);
	}
	else
	{
		printf("获取到 %d 个路由环境配置:\n", (int)routeEnvs.size());
		DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "获取到 %d 个路由环境配置\n", (int)routeEnvs.size());

		for (size_t i = 0; i < routeEnvs.size(); i++)
		{
			printf("环境 %d: 编码=%s, 地址=%s\n",
				   (int)i + 1,
				   routeEnvs[i].env_code.c_str(),
				   routeEnvs[i].env_addr.c_str());
			DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "环境 %d: 编码=%s, 地址=%s\n",
					 (int)i + 1,
					 routeEnvs[i].env_code.c_str(),
					 routeEnvs[i].env_addr.c_str());
		}
	}

	// 清理资源
	if (dbm != NULL)
	{
		delete dbm;
		dbm = NULL;
	}

	DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "灰度路由测试结束");
	return 0;
}

#include "DCSendCallBack.h"
#include "DCLogMacro.h"
#include "DCTaskService.h"
#include "DCTaskServiceSum.h"
#include "DCPerfLogStatistic.h"

DCSendCallBack::DCSendCallBack(DCPairList *pairList) 
{
	m_pairList = pairList;	
}

DCSendCallBack::~DCSendCallBack()
{

}

void DCSendCallBack::callback(const std::string& uuid, const std::string& serviceName, const char *pParams, int len)
{
	DCPerfTimeVCollect collet(DCPerfLogStatistic::instance()->GetPerfTimeStats("APP", "AnsTask"), true);

	DCBIZLOG(DCLOG_LEVEL_INFO, 0, "","recv ans msg: uuid=[%s],pParams[%s]",uuid.c_str(),pParams);
	string ipErrorcode = "";
	//m_pairList->push(uuid);
	if(strlen(pParams) > 0)
	{
		ipErrorcode =  pParams;
	}
	int ret = m_pairList->enQueue(uuid,ipErrorcode);
	if(ret == 1)
	{
		DCTaskService::state(2);
	}
	else if(ret == 2)
	{
		DCTaskServiceSum::state(2);
	}

	collet.stop();
}

/*void DCSendCallBack::setList(vector<DBInDex> db)
{
	m_dblist.swap(db);		
}*/


/*int DCSendCallBack::getHashIndex(int ttid)
{
	for(int i=0;i<m_threadnum;i++)
	{
		if(ttid%m_threadnum==i)
		{	
			lock();
			if(m_dblist[i].threadid==ttid)
			{
				unlock();
				return i;
			}
			else
			{ 
				unlock();
				return i+1;
			}  
		}
	}
	return 0;
}*/



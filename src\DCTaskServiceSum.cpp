﻿
#include <ace/Guard_T.h>
#include <string>
#include <utility>
#include <string.h>
#include <unistd.h>
#include <time.h>
#include <sys/time.h>
#include <sched.h>
#include <iostream>
#include <fstream>
#include <netdb.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>

#include "DCTaskServiceSum.h"
#include "DCLogMacro.h"
#include "DCParseXml.h"
#include "UHead.h"
#include "DCPerfLogStatistic.h"

using namespace std;

int DCTaskServiceSum::sm_nDone = 0;
int DCTaskServiceSum::sm_nQueueState = 0;
string DCTaskServiceSum::m_strHostIp = "";
DCTQueue<SumTask> DCTaskServiceSum::sm_qTask;
ACE_Thread_Mutex DCTaskServiceSum::sm_aceMutex;
time_t DCTaskServiceSum::sm_checktime = time(NULL);

DCTaskServiceSum::DCTaskServiceSum():m_en(ESeriaBinString)
{
	m_bexit = true;
	m_task = NULL;
	m_pairList = NULL;
	m_exchdb = NULL;
	m_client = NULL;
}

DCTaskServiceSum::~DCTaskServiceSum()
{
	//SAFE_DELETE_PTR(m_dmdb);
	//SAFE_DELETE_PTR(m_pairList);
	SAFE_DELETE_PTR(m_exchdb);
}

/* void getCurrentTime(char* v_date)
{
tm *p;
time_t timep;
time(&timep);
p=localtime(&timep);
sprintf(v_date,"%d%02d%02d%02d%02d",(1900+p->tm_year) , (1+p->tm_mon) ,p->tm_mday,p->tm_hour,p->tm_min);

return;
}	 */

void DCTaskServiceSum::state(int done)
{
	sm_nDone = done;
}

void DCTaskServiceSum::queueState(int flag)
{
	sm_nQueueState = flag;
}

bool DCTaskServiceSum::exit()
{
	return m_bexit;
}

int DCTaskServiceSum::run()
{
	int ret = 0;
	ret = activate(THR_NEW_LWP | THR_JOINABLE | THR_INHERIT_SCHED | THR_SCOPE_SYSTEM, 1);
	if(ret < 0)
	{
		return -1;
	}
	return 0;
}

int DCTaskServiceSum::init(DCDBManer* dmdb,dcf_new::DCFLocalClient *client,DCPairList *pairlist,int latnId, int iThreadNo)
{
	m_dmdb = dmdb;
	m_exchdb = new DCExchDB(dmdb);
	if(!m_exchdb)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","new DCExchDB() faild.");
		return -1;
	}
	m_client = client;
	m_pairList = pairlist;
	SExchCfg* pcfg = DCExchCfg::instance()->GetConfig();
	m_cfg = pcfg;
	m_compress = Compressor::Instance();
	m_latnId = latnId;
	m_iThreadNo = iThreadNo;

    // 获取ip地址
    char szHostName[128] = { 0 };
    struct hostent *hent = NULL;
    gethostname(szHostName, sizeof(szHostName));
    hent = gethostbyname(szHostName);
    m_strHostIp = inet_ntoa(*(struct in_addr*)(hent->h_addr_list[0]));
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "hostIp[%s]", m_strHostIp.c_str());
	m_bInterceptFlag = false;
	return 0;
}

int DCTaskServiceSum::CheckIntercept(DCDBManer *dmdb, int iLatnId, const SumTask& task)
{
	int iRet = 0;
	string strBatchTime = GetBatchTime(task.batchId);
	if (strBatchTime.empty())
	{
		iRet = m_exchdb->MarkFileStateSum(task.chargeFilesId, task.batchId.c_str(), "M0E", task.latnId, "M0D");
		if (iRet <= 0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "charge file id[%ld], batchId[%s], mark file state M0D -> M0E failed", 
				task.chargeFilesId, task.batchId.c_str());
		}

		return 1;
	}

	string strInterceptTime = "";
	iRet = GetBatchInterceptTime(dmdb, iLatnId, strInterceptTime);
	if (iRet < 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "query intercept time failed");
		return -1;
	}

	if (strInterceptTime.empty())
	{
		if (m_bInterceptFlag)
		{
			UpdateTaskStatus(dmdb, iLatnId);
			m_bInterceptFlag = false;
		}

		return 0;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "intercept time[%s], batchTime[%s]", strInterceptTime.c_str(), strBatchTime.c_str());
	if (strncmp(strInterceptTime.c_str(), strBatchTime.c_str(), 12) > 0)
	{
		return 0;
	}

	iRet = m_exchdb->MarkFileStateSum(task.chargeFilesId, task.batchId.c_str(), "M0I", task.latnId, "M0D");
	if (iRet <= 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "charge file id[%ld], batchId[%s], mark file state M0D -> M0I failed", 
			task.chargeFilesId, task.batchId.c_str());
	}

	m_bInterceptFlag = true;
	return 1;
}

int DCTaskServiceSum::svc()
{
	int ret = 0;
	SumTask tasks;
	ACE_Time_Value expired;
	m_bexit = false;
	m_checktime = time(NULL);
	map<int, long>::iterator iter;

	if (m_iThreadNo == 0)
	{
		UpdateTaskStatus(m_dmdb, m_latnId);
	}
	
	while(1)	// 可运行
	{
		SExchCfg* pcfg = DCExchCfg::instance()->GetConfig();
		if(m_cfg!=pcfg)
		{
			m_cfg = pcfg;
		}

		DCKpiMon *ptrBPMon = DCKpiSender::instance()->GetKPIHandle("BILLING", "REC", "MSGEX");
		char monGroup[50] = {0};
		DCKpiSender::instance()->GetFullGroup("TraceKpi", m_latnId, monGroup);

		if (DCExchDB::CheckDBState(m_dmdb, m_checktime) < 0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "connected to database error!");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "kpi code[MXEc] latnid[%d] err[2005] msg[connect to db failed]", m_latnId);
			DCKpiSender::instance()->cycle_array_inc(ptrBPMon, monGroup, "", "MXEc", "2005", 1);
			continue;
		}

		ret = sm_qTask.try_dequeue(tasks);	//优先从任务队列获取
		if (ret < 0)
		{
			ret = GetTask(m_dmdb,tasks,m_latnId);	//从数据库获取
			if(ret<=0)
			{
				expired.set(time(NULL)+5, 0);
				if(sm_qTask.dequeue(tasks, &expired) < 0)	//超时等待获取
				{
					if(sm_nDone == 2)		// 退出执行
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","thread exit");
						break;
					}

					string strInterceptTime = "";
					int iRet = GetBatchInterceptTime(m_dmdb, m_latnId, strInterceptTime);
					if (iRet < 0)
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "query intercept time failed");
						continue;
					}

					if (strInterceptTime.empty() && m_bInterceptFlag)
					{
						UpdateTaskStatus(m_dmdb, m_latnId);
						m_bInterceptFlag = false;
					}

					DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","task continue");
					continue;
				}
			}
		}

		DCKpiSender::instance()->cycle_array_set(ptrBPMon, monGroup, "", "MXBl", NULL, sm_qTask.size());
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "kpi code[MXBl] latnid[%d] value[%d]", m_latnId, sm_qTask.size());

		if(sm_nDone == 2)		// 退出执行
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","task exit");
			break;
		}

		while (true)
		{
			ret = CheckIntercept(m_dmdb, m_latnId, tasks);
			if (ret < 0)
			{
				sleep(5);
				continue;
			}

			break;
		}

		if (ret == 1)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "intercept task, chargeFileId[%ld], latnid[%d]", tasks.chargeFilesId, m_latnId);
			continue;
		}

		m_task = &tasks;
		ret = m_exchdb->MarkFileStateSum(tasks.chargeFilesId, tasks.batchId.c_str(), "M0B",m_latnId,"M0D");
		//尝试一次重连
		if(ret <= 0)
		{
			DCKpiSender::instance()->cycle_array_inc(ptrBPMon, monGroup, "", "MXEc", "2006", 1);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "kpi code[MXEc] latnid[%d] err[2006] msg[mark file state failed M0D->M0B]", m_latnId);
			continue;
		}

		ret = DoTask(tasks);
		if(ret < 0)
		{
			// 处理失败，异常状态
			DCKpiSender::instance()->cycle_array_inc(ptrBPMon, monGroup, "", "MXFEr", NULL, 1);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "kpi code[MXFEr] latnid[%d] inc", m_latnId);

			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "chargeFilesId[%ld], deal failed, mark to exception", tasks.chargeFilesId);
			ret = m_exchdb->MarkFileStateSum(tasks.chargeFilesId, tasks.batchId.c_str(), "M0E", m_latnId, "M0B");
			if(ret < 0)
			{
				DCKpiSender::instance()->cycle_array_inc(ptrBPMon, monGroup, "", "MXEc", "2006", 1);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "kpi code[MXEc] latnid[%d] err[2006] msg[mark file state failed M0B->M0E]", m_latnId);
			}
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_INFO,0,""," chargeFilesId[%ld], fileName[%s],deal success.",tasks.chargeFilesId, tasks.fileName.c_str());
			ret = m_exchdb->MarkFileStateSum(tasks.chargeFilesId, tasks.batchId.c_str(), "M0A", m_latnId, "M0B");
			if(ret < 0)
			{
				DCKpiSender::instance()->cycle_array_inc(ptrBPMon, monGroup, "", "MXEc", "2006", 1);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "kpi code[MXEc] latnid[%d] err[2006] msg[mark file state failed M0B->M0A]", m_latnId);
			}
		}
		DCBIZLOG(DCLOG_LEVEL_INFO,0,""," chargeFilesId[%ld], deal done",tasks.chargeFilesId);
		m_task = NULL;
	}
	m_pairList->SetQuit();
	m_bexit = true;
	return 0;
}

string DCTaskServiceSum::GetBatchTime(string strBatchId)
{
	string strBatchTime = "";
	if (strBatchId.size() > 12) // 年月日时分
	{
		int iTimePos = strBatchId.size() - 12;
		strBatchTime = strBatchId.substr(iTimePos);
	}

	return strBatchTime;
}

int DCTaskServiceSum::DoTask(const SumTask& task)
{
	DCPerfTimeVCollect collet(DCPerfLogStatistic::instance()->GetPerfTimeStats("APP", "DoTask", m_dmdb), true);
	DCBIZLOG(DCLOG_LEVEL_INFO,0,"","start to deal file[%ld]",task.chargeFilesId);
	ocs::UHead uhd;
	ocs::IAcctBodyReq pRollBody;
	ocs::IAcctHead head;
	ocs::UFmtHead uFmtHead;
	ocs::RatingFee stRatingFee;
	uFmtHead.latnid = task.latnId;
	uFmtHead.msgtype = "trail";
	uFmtHead.opertype = "acct";
	head.latn_id = task.latnId;
	char cBillingCycleId[20] = {0};
	sprintf(cBillingCycleId,"%d",task.billingCycleId);
	head.billing_cycle = cBillingCycleId;
	head.BatchNo = atol(task.batchId.c_str());
	string filename = task.fileName;
	if(task.fileName.find("common_detail") != string::npos)
	{
		head.messagesource = 7;
		head.big_acct_flag = 0;
	}
	else if(task.fileName.find("bigacct_detail") != string::npos)
	{
		head.messagesource = 7;
		head.big_acct_flag = 1;
	}
	else if(task.fileName.find("crossacct_detail") != string::npos)
	{
		head.messagesource = 8;
		head.big_acct_flag = 2;
	}
	else if(task.fileName.find("superacct_detail") != string::npos)
	{
		head.messagesource = 7;
		head.big_acct_flag = 3;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","file name error[%s]",task.fileName.c_str());
		return -1;
	}
	if(task.fileName.find(".roll") != string::npos)
	{
		head.messagesource += 2;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","is roll file");
	}
	std::string strLocalFile= task.filePath + '/' + task.fileName;
	DCBIZLOG(DCLOG_LEVEL_INFO,0,"","parse local file[%s]",strLocalFile.c_str());
	ifstream ifs(strLocalFile.c_str());
	string sline;
	int line = 0;
	if(!ifs)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","parse file error");
		return -1;
	}
	uhd.ext["IMPORT_P"] = "File";
	uhd.car = "10";
	long lsendMsgCount = m_cfg->lsendMsgCount;
	int  sleeptime = m_cfg->sleeptime;
	string sendmsg="";
	SMsgPair sMsg;
	int count = 0;
	ofstream fBreakPoint;
	char bakPath[128] = {0};
	int breakLine = 0;
	sprintf(bakPath,"%s/%ld",m_cfg->sum_brk_dir.c_str(),task.chargeFilesId);
	if(access(bakPath,F_OK) == 0)
	{
		ifstream ifBreakPoint(bakPath);
		if(ifBreakPoint.is_open())
		{
			ifBreakPoint>>breakLine;
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","read break file[%s],line:%d",bakPath,breakLine);
		}
		ifBreakPoint.close();
	}
	TAccRecord recordBuf;
	int bSend = 0;
	int firstSend = 0;
	int freeFlag = 0;
	long lnSendLine = 0;
	long lnRouteAcctId = 0;

	DCKpiMon *ptrBPMon = DCKpiSender::instance()->GetKPIHandle("BILLING", "REC", "MSGEX");
	char monGroup[50] = {0};
	DCKpiSender::instance()->GetFullGroup("TraceKpi", m_latnId, monGroup);

	while(ifs)
	{
		DCPerfTimeVCollect collet_cdr(DCPerfLogStatistic::instance()->GetPerfTimeStats("APP", "ToMsg"), true);

		if(getline(ifs, sline))
		{
			line++;
			if(breakLine > 0)
			{
				breakLine--;
				continue;
			}
			lnSendLine++;
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","read file[%s],line:%d",sline.c_str(),line);
			GetBuf(recordBuf,sline);
			if(firstSend == 0)
			{
				char uuid[50] = {0};
				if(head.messagesource % 2)
				{
					head.acct_id = recordBuf.nAcctID;
				}
				else
				{
					head.acct_id = recordBuf.nModGroupId;
				}
				lnRouteAcctId = recordBuf.nAcctID;
				head.mtype = recordBuf.if_realtime_disct;
				if(recordBuf.uid.length() == 0)
				{
					sprintf(uuid,"%02d%s%014ld%014ld",head.mtype,task.batchId.c_str(),task.chargeFilesId,head.acct_id);
					uhd.uid = uuid;
				}
				else
				{
					uhd.uid = recordBuf.uid;
				}
				SetRecord(stRatingFee,recordBuf);
				if((stRatingFee.IfCloud && !DCExchCfg::instance()->bDoubleEqualZero(stRatingFee.HprecisionCharge)) ||
				   (!stRatingFee.IfCloud && (stRatingFee.OriCharge + stRatingFee.DisctCharge != 0)))
				//if(stRatingFee.OriCharge + stRatingFee.DisctCharge != 0)
				{
					freeFlag = 1;
				}

				pRollBody.VRealRatingFee.push_back(stRatingFee);
				head.platform = recordBuf.nPlatform;
                head.service_nbr = recordBuf.cServiceNbr;
				head.serv_id.push_back(recordBuf.nServId);
				head.cust_id.push_back(atol(recordBuf.custId.c_str()));
				firstSend = 1;

				continue;
			}
			// (A && (B || C)) || (!A && (D || C))
			if(((head.messagesource % 2)&&( head.acct_id != recordBuf.nAcctID || head.mtype
				!= recordBuf.if_realtime_disct)) ||((head.messagesource % 2 == 0) && (head.acct_id != recordBuf.nModGroupId ||head.mtype != recordBuf.if_realtime_disct)))
			{
				bSend = 1;
			}
			if(bSend == 0)
			{
				SetRecord(stRatingFee,recordBuf);
				if((stRatingFee.IfCloud && !DCExchCfg::instance()->bDoubleEqualZero(stRatingFee.HprecisionCharge)) ||
				   (!stRatingFee.IfCloud && (stRatingFee.OriCharge + stRatingFee.DisctCharge != 0)))
				//if(stRatingFee.OriCharge + stRatingFee.DisctCharge != 0)
				{
					freeFlag = 1;
				}
				if(uhd.uid < recordBuf.uid)
				{
					uhd.uid = recordBuf.uid;
				}
				pRollBody.VRealRatingFee.push_back(stRatingFee);
				head.platform = recordBuf.nPlatform;
                head.service_nbr = recordBuf.cServiceNbr;
				head.serv_id.push_back(recordBuf.nServId);
				head.cust_id.push_back(atol(recordBuf.custId.c_str()));
				continue;
			}
		}
		int tempMessageSource = head.messagesource;
		if(freeFlag == 0)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "free message");
			head.messagesource = 5;
		}
		if(uhd.uid.length() == 0)
		{
			char uuid[50] = {0};
			sprintf(uuid,"%02d%s%014ld%014ld",head.mtype,task.batchId.c_str(),task.chargeFilesId,head.acct_id);
			uhd.uid = uuid;
		}
		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(uFmtHead);
		m_en.encode(head);
		m_en.encode(pRollBody);
		if(DCLOG_GETLEVEL(DCLOG_CLASS_BIZ) >= DCLOG_LEVEL_DEBUG  && head.serv_id.size() < 20)
		{
			//打印head消息
			m_print.clear();
			m_print.print(uhd);
			m_print.print(uFmtHead);
			m_print.print(head);
			m_print.print(pRollBody);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "msg[%s]", m_print.data());
		}
		sendmsg = HexEncode(m_en.data(),m_en.size());
		compress(sendmsg);
		if(sendmsg.size()> 64*1024)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "msgSize[%d] > 64k", sendmsg.size());
		}
		char premsg[25] = {0};
		sprintf(premsg,"######%016ld%02d",head.acct_id,head.big_acct_flag<=1?head.big_acct_flag+11:head.big_acct_flag+12);
		string spremsg = premsg;
		sendmsg = premsg + sendmsg;
		char buf[20]={0};
		struct timeval tv;
		gettimeofday(&tv, NULL);
		sprintf(buf, "%016ld", tv.tv_sec*1000000 + tv.tv_usec);
		sendmsg.insert(0, buf);
		sMsg.begin = tv;
		sMsg.uuid = uhd.uid;
		sMsg.latnid = task.latnId;
        sMsg.batchId = task.batchId;
		sMsg.sendMsg = sendmsg;
		sMsg.sourceid = task.chargeFilesId;
		sMsg.servName = m_cfg->serviceBILL;
        sMsg.procId[0] = 'C';
        sMsg.procId[1] = '\0';
		while(m_pairList->size()>=m_cfg->FlowControlSize)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","system overload[%d]",m_pairList->size());
			sleep(1);
			continue;
		}
		string serviceBILL = m_cfg->serviceBILL;
		dcf_new::DCFLocalClient *pTempClient = m_client;
		GetGrayServName(head.latn_id, lnRouteAcctId, serviceBILL, head.service_nbr, pTempClient);
		sMsg.servName = serviceBILL;
		m_pairList->in(sMsg.uuid,sMsg);
		int ret = 0;
		//if(head.big_acct_flag == 1)
		{
			char sAcctId[32] = {0};
			sprintf(sAcctId, "{\"hashkey\":\"%ld\"}", head.acct_id);
			string strRouteJson = sAcctId;
			ret = pTempClient->invokeAsync(serviceBILL, sMsg.uuid, sendmsg,"",0,strRouteJson);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "send CCR to BillingBolt,service:%s,RouteJson:%s", serviceBILL.c_str(),strRouteJson.c_str());
		}
		/*
		else
		{
			ret = m_client->invokeAsync(serviceBILL, sMsg.uuid, sendmsg,"",0);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "send CCR to BillingBolt,service:%s", serviceBILL.c_str());
		}
		*/
		if (ret < 0)
		{
			// 区分灰度服务和生产服务失败的情况
			bool isGrayClient = (pTempClient != m_client);
			int errCode = isGrayClient ? -390001 : -390002;
			char errCodeStr[16] = {0};
			sprintf(errCodeStr, "%d", errCode);
			const char *errTypeStr = isGrayClient ? "grayscale" : "production";

			DCBIZLOG(DCLOG_LEVEL_ERROR, errCode, "", "send failed: %s[%s] invokeAsync failed, return=%d", 
				errTypeStr, serviceBILL.c_str(), ret);
			m_pairList->out(sMsg.uuid,sMsg);//发送失败的不插入缓存
			
			DCKpiSender::instance()->cycle_array_inc(ptrBPMon, monGroup, "", "MXEc", "2007", 1);

			// 读取剩下的行算到记录级的错单量
			long lnErrorLine = lnSendLine;
			while (ifs.good())
			{
				if (getline(ifs, sline))
				{
					lnErrorLine++;
				}
			}

			ifs.close();
			DCKpiSender::instance()->cycle_array_inc(ptrBPMon, monGroup, "", "MXEr", NULL, lnErrorLine);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "kpi code[MXEr] latnid[%d] errCnt[%ld]", m_latnId, lnErrorLine);
			return -1;
		}

		DCKpiSender::instance()->cycle_array_inc(ptrBPMon, monGroup, "", "MXR", NULL, 1);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "kpi code[MXR] latnid[%d] inc", m_latnId);

		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","send msg: %s",sendmsg.c_str());
		count++;
		head.messagesource = tempMessageSource;
		freeFlag = 0;
		lnSendLine = 0;
		fBreakPoint.open(bakPath);
		fBreakPoint<<line;
		fBreakPoint.close();
		if(sline.length() == 0)
		{
			collet_cdr.stop();
			break;
		}
		if(lsendMsgCount && line%lsendMsgCount==0)
		{
			usleep(sleeptime);
		}
		//char uuid[50] = {0};
		if(head.messagesource % 2)
		{
			head.acct_id = recordBuf.nAcctID;
		}
		else
		{
			head.acct_id = recordBuf.nModGroupId;
		}
		head.mtype = recordBuf.if_realtime_disct;
		//sprintf(uuid,"%02d%s%014ld%014ld",head.mtype,task.batchId.c_str(),task.chargeFilesId,head.acct_id);
		uhd.uid = recordBuf.uid;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","msgpair size:%d",m_pairList->size());
		bSend = 0;
		pRollBody.VRealRatingFee.clear();
		head.serv_id.clear();
		head.cust_id.clear();
		SetRecord(stRatingFee,recordBuf);
		if((stRatingFee.IfCloud && !DCExchCfg::instance()->bDoubleEqualZero(stRatingFee.HprecisionCharge)) ||
				   (!stRatingFee.IfCloud && (stRatingFee.OriCharge + stRatingFee.DisctCharge != 0)))
		//if(stRatingFee.OriCharge + stRatingFee.DisctCharge != 0)
		{
			freeFlag = 1;
		}

        head.platform = recordBuf.nPlatform;
        head.service_nbr = recordBuf.cServiceNbr;
		head.serv_id.push_back(recordBuf.nServId);
		head.cust_id.push_back(atol(recordBuf.custId.c_str()));
		pRollBody.VRealRatingFee.push_back(stRatingFee);

		collet_cdr.stop();
	}
	//根据sourceid修改该文件下消息总数
	m_pairList->modify(count,task.chargeFilesId);
	ifs.close();
	//remove(strLocalFile.c_str());
	remove(bakPath);
	DCBIZLOG(DCLOG_LEVEL_INFO,0,"","msgNum: %d", count);

	collet.stop();

	return 0;
}

int DCTaskServiceSum::GetBatchInterceptTime(DCDBManer *dmdb, int iLatnId, string &strInterceptTime)
{
	char szInterceptTime[30] = {0};
	while (true)
	{
		UDBSQL* pQuery = dmdb->GetSQL("QueryInterceptTime");
		if (!pQuery)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "not find sqlcode[QueryInterceptTime]");
			return -1;
		}

		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "query intercept time, latnId[%d]", iLatnId);
		try
		{
			pQuery->UnBindParam();
			pQuery->BindParam(1, iLatnId);
			pQuery->Execute();
			if (pQuery->Next())
			{
				pQuery->GetValue(1, szInterceptTime);
			}
		}
		catch(UDBException& e)
		{
			std::string sql;
			pQuery->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "SQL[%s]", sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "query execption[%s]", e.ToString());
			int iRet = DCExchDB::CheckDBState(m_dmdb, sm_checktime);
			if (iRet != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", iRet);
				continue;
			}
			else
			{
				return -1;
			}
		}

		break;
	}

	strInterceptTime = szInterceptTime;
	return 0;
}

int DCTaskServiceSum::UpdateTaskStatus(DCDBManer *dmdb, int iLatnId)
{
	char sqlname[32] = {0};
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "update intercept sum task to wait deal status");
	sprintf(sqlname, "UpdateIntercetpTaskSum|%d", iLatnId);
	while (1)
	{
		UDBSQL* update = dmdb->GetSQL(sqlname);
		if (!update)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "not find sqlcode[%s]", sqlname);
			return -1;
		}

		try
		{
			update->UnBindParam();
			update->BindParam(1, m_strHostIp.c_str());
			update->Execute();
			update->Connection()->Commit();
		}
		catch(UDBException& e)
		{
			std::string sql;
			update->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "SQL[%s]", sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "update execption[%s]", e.ToString());
			int iRet = DCExchDB::CheckDBState(dmdb, sm_checktime);
			if (iRet != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", iRet);
				continue;
			}
			else
			{
				return -1;
			}
		}

		break;
	}

	return 0;

}

int DCTaskServiceSum::GetTask(DCDBManer *dmdb, SumTask& otask, int latnId)
{
	ACE_Guard<ACE_Thread_Mutex> guard(sm_aceMutex);
	if(!sm_qTask.try_dequeue(otask))
	{
		return 1;
	}

	int pid = getpid();
	int threadid = gettid();
	dmdb->FastReset();
	char buf[128]={0};
	sprintf(buf, "%d-%d", pid, threadid);
	int row = 0;
	char sqlname[32] = {0};
	sprintf(sqlname,"UpdateTaskSum|%d",latnId);
	while (1)
	{
		UDBSQL* update = dmdb->GetSQL(sqlname);
		if (!update)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "not find sqlname[%s]", sqlname);
			return -1;
		}

		try
		{
			update->UnBindParam();
			update->BindParam(1,buf);
			update->BindParam(2,m_strHostIp.c_str());
			update->Execute();
			row = update->GetRowCount();
			update->Connection()->Commit();
		}
		catch(UDBException& e)
		{
			std::string sql;
			update->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "SQL[%s]", sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "update execption[%s]", e.ToString());
			int _ret = DCExchDB::CheckDBState(dmdb, sm_checktime);
			if (_ret != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", _ret);
				continue;
			}
			else
			{
				return -2;
			}
		}

		break;
	}

	if (row > 0)
	{
		DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "UpdateTask rownum[%d]", row);
	}
	else
	{
		return 0;
	}

	SumTask task;
	sprintf(sqlname,"QueryTaskSum|%d",latnId);
	long lnM0DTaskSumCnt = 0;
	string strBatchTime = "";
	while (1)
	{
		lnM0DTaskSumCnt = 0;
		UDBSQL* query = dmdb->GetSQL(sqlname);
		if (!query)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "not find sqlname[%s]", sqlname);
			return -1;
		}

		try
		{
			query->UnBindParam();
			query->BindParam(1, buf);
			query->BindParam(2, m_strHostIp.c_str());
			query->Execute();
			while(query->Next())
			{
				query->GetValue(1, task.chargeFilesId);
				query->GetValue(2, task.batchId);
				query->GetValue(3, task.filePath);
				query->GetValue(4, task.fileName);
				query->GetValue(5, task.latnId);
				query->GetValue(6, task.billingCycleId);
				DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "get one record,ready enqueue,filesId[%ld],batchId[%s]", task.chargeFilesId, task.batchId.c_str());
				sm_qTask.enqueue(task);
				lnM0DTaskSumCnt++;
			}
		}
		catch(UDBException& e)
		{
			std::string sql;
			query->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "SQL[%s]", sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "query execption[%s]", e.ToString());
			int _ret = DCExchDB::CheckDBState(dmdb, sm_checktime);
			if (_ret != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", _ret);
				continue;
			}
			else
			{
				return -1;
			}
		}

		break;
	}

	long lnResultCount = DCExchGlobal::instance()->ReduceCount(G_EnM0CTaskSumCnt, lnM0DTaskSumCnt);
	DCKpiMon *ptrBPMon = DCKpiSender::instance()->GetKPIHandle("BILLING", "REC", "MSGEX");
	char monGroup[50] = {0};
	DCKpiSender::instance()->GetFullGroup("TraceKpiA", latnId, monGroup);
	DCKpiSender::instance()->state_array_set(ptrBPMon, monGroup, "", "MXTdF", NULL, lnResultCount);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "kpi code[MXTdF] latnid[%d] Cnt[%ld]", latnId, lnResultCount);
	long qTaskSize = sm_qTask.size();
	DCKpiSender::instance()->state_array_set(ptrBPMon, monGroup, "", "MXBl", NULL, qTaskSize);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "kpi code[MXBl] latnid[%d] Cnt[%d]", latnId, qTaskSize);
	
	if(!sm_qTask.try_dequeue(otask))
	{
		return 1;
	}

	return 0;
}

int DCTaskServiceSum::GetModGroupID(DCDBManer* dmdb, int latnId, long vi_nAcctID, long& vo_ModGroupID)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "GetModGroupID by acctid(%ld)", vi_nAcctID);
	char szSqlName[128] = { 0 };
	sprintf(szSqlName, "PARCROSSACCT|%d|%d", latnId, latnId);

	while (1)
	{
		UDBSQL* pQuery = dmdb->GetSQL(szSqlName);
		if (pQuery == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "SQLCODE[%s] not found", szSqlName);
			return -1;
		}

		try
		{
			pQuery->UnBindParam();
			pQuery->BindParam(1, vi_nAcctID);
			pQuery->Execute();
			if (pQuery->Next())
			{
				pQuery->GetValue(1, vo_ModGroupID);
			}
			else
			{
				return -1;
			}
		}
		catch(UDBException& e)
		{
			std::string sql;
			pQuery->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "SQL[%s]", sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "query execption[%s]", e.ToString());
			int _ret = DCExchDB::CheckDBState(dmdb, m_checktime);
			if (_ret != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", _ret);
				continue;
			}
			else
			{
				return -1;
			}
		}

		break;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Get ModGroupID = (%ld)", vo_ModGroupID);
	return 0;
}

void DCTaskServiceSum::GetBuf(TAccRecord & recordBuf,string & line)
{
	sscanf(line.c_str(),"%ld|%ld|%ld|%ld|%ld|%ld|%ld|%ld|%ld|%d|%d|%d|%d",
		&recordBuf.nServId,
		&recordBuf.nAcctID,
		&recordBuf.nAcctItemTypeId,
		&recordBuf.nOfferId,
		&recordBuf.nDuration,
		&recordBuf.nBillingDuration,
		&recordBuf.nCounts,
		&recordBuf.nCalls,
		&recordBuf.nFlux,
		&recordBuf.day,
		&recordBuf.nBelongDay,
		&recordBuf.nBillingMode,
		&recordBuf.nBelongBillingCycleId);
	string temp = line;
	for(int i = 0;i < 13; i++)
	{
		size_t pos = temp.find("|");
		temp = temp.substr(pos+1,temp.length());
	}

    recordBuf.uid.clear();
    recordBuf.cServiceNbr.clear();
    recordBuf.nStrategyId = 0;
	string value;
	for(int i = 0;i < 15;i++)
	{
		size_t pos = temp.find("|");
		value = temp.substr(0,pos);
		temp = temp.substr(pos+1,temp.length());
		switch(i)
		{
		case 0:
			recordBuf.cFeeType = value;
			break;
		case 1:
			recordBuf.cPayFlag = value;
			break;
		case 2:
			recordBuf.nDisounctCharge = atol(value.c_str());
			break;
		case 3:
			recordBuf.nOriCharge = atol(value.c_str());
			break;
		case 4:
			{
				if ( 0 != GetModGroupID(m_dmdb, m_latnId, recordBuf.nAcctID, recordBuf.nModGroupId) )
				{
					recordBuf.nModGroupId = atol(value.c_str());
				}
				break;
			}
		case 5:
			recordBuf.if_realtime_disct = atoi(value.c_str());
			break;
		case 6:
			recordBuf.sourceInstId= atol(value.c_str());
			break;
		case 7:
			recordBuf.custId = value;
			break;
        case 8:
            recordBuf.uid = value;
            break;
        case 9:
            recordBuf.cServiceNbr = value;
            break;
		case 10:
			recordBuf.nStrategyId = atol(value.c_str());
			break;
		case 11:
			recordBuf.nPlatform = atoi(value.c_str());
			break;
		case 12:
			recordBuf.nOnLineMode5G = atoi(value.c_str());
			break;
		case 13:
			recordBuf.HprecisionCharge = atof(value.c_str());
			break;
		case 14:
			recordBuf.IfCloud = atoi(value.c_str());
			break;
		}
	}

    /*
    recordBuf.nOnLineMode5G = atoi(temp.c_str());
    if (recordBuf.cServiceNbr.empty())
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "file format may be error.");
    }
    */

	return;
}

void DCTaskServiceSum::compress(string& buf)
{
	char szSize[64] = {0};
	string strSendMsg="";
	if(buf.size() > 1024*64)
	{
		DCBIZLOG(DCLOG_LEVEL_TRACE,0,"","message.size[%d] > 1024*64 must compress first. ",buf.size());
		int nRet = m_compress->Compress(buf.c_str(),buf.size());
		char * text =  m_compress->GetCompress();

		buf.clear();
		buf.resize(nRet+1,0);
		memcpy((void*)buf.c_str(),(void*)text,nRet);

		m_en.clear();
		m_en.encode(buf);
		strSendMsg = HexEncode(m_en.data(),m_en.size());

		buf.clear();
		sprintf(szSize,"%.10d",nRet);
		buf = "$$$$$$";
		buf += szSize;
		buf += strSendMsg;
		DCBIZLOG(DCLOG_LEVEL_TRACE,0,"","After compress the message is[%s]. ",buf.c_str());
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_TRACE,0,"","Don't need compress the message.");
	}
}

void DCTaskServiceSum::SetRecord(ocs::RatingFee &record,TAccRecord &buf)
{
	record.lnPrdInstId = buf.nServId;
	record.lnAcctId = buf.nAcctID;
	record.lnAcctItemTypeId = buf.nAcctItemTypeId;
	record.lnOfrId = buf.nOfferId;
	record.lnOfrInstId = buf.sourceInstId;
	record.lnDuration = buf.nDuration;
	record.lnBillingDuration = buf.nBillingDuration;
	record.lnCounts = buf.nCounts;
	record.lnCalls = buf.nCalls;
	record.lnFlux = buf.nFlux;
	record.nDay = buf.day;
	record.nBelongDay = buf.nBelongDay;
	record.nBillingMode = buf.nBillingMode;
	record.nBelongBillingCycleId = buf.nBelongBillingCycleId;
	record.cFeeType = buf.cFeeType;
	record.cPayFlag = buf.cPayFlag;
	record.DisctCharge = buf.nDisounctCharge;
	record.OriCharge = buf.nOriCharge;
    record.lnCustId = atol(buf.custId.c_str());
    record.lnStrategyId = buf.nStrategyId;
    record.cRepresentNbr = buf.cServiceNbr;
	record.nOnlineMod5g = buf.nOnLineMode5G;
	record.HprecisionCharge = buf.HprecisionCharge;
	record.IfCloud =buf.IfCloud;
	return;
}
//根据灰度路由规则表获取服务名
int DCTaskServiceSum::GetGrayServName(const int nLatnId, const long lnRouteAcctId, string &serviceBILL, string &cServiceNbr, dcf_new::DCFLocalClient *pClient)
{
	//路由规则获取服务名称
	string sServiceName = "";
	char szTmp[128] = {0};
	map<string,string> inMapParam;
	sprintf(szTmp,"%d",nLatnId);
	inMapParam.insert(map<string,string>::value_type("LatnID",szTmp));
	sprintf(szTmp,"%ld",lnRouteAcctId);
	inMapParam.insert(map<string,string>::value_type("ACCTID",szTmp));
	inMapParam.insert(map<string,string>::value_type("BillingNum",cServiceNbr));

	// 通过出参pDCFClient获取路由决定的DCFClient
	dcf_new::DCFLocalClient *pDCFClient = NULL;
	int ret = DCGrayscaleRoute::instance()->GetRouteServiceNameDCFClient(m_cfg->RouteACCTProcess.c_str(), inMapParam, sServiceName, pDCFClient);
	if (ret < 0 || 0 == sServiceName.length())
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Get RouteServiceName failed.");
		return -1;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","Get RouteServiceName[%s]",sServiceName.c_str());
		// 如果获取到有效的DCFClient，替换传入的指针
		if (pDCFClient != NULL && pClient != NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Got valid DCFClient from GetRouteServiceNameDCFClient");
			pClient = pDCFClient;
		}
	}
	serviceBILL = sServiceName;
	return 0;
}


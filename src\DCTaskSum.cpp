#include "DCTaskSum.h"
#include "DCLogMacro.h"
#include "DCParseXml.h"
#include "DCPerfLogStatistic.h"

#include <ace/Guard_T.h>
#include <string>
#include <utility>
#include <string.h>
#include <unistd.h>
#include <time.h>
#include <sys/time.h>
#include <sched.h>
#include <iostream>
#include <fstream>
#include <sys/wait.h>
#include <sys/types.h>
#include <netdb.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>


namespace DCTaskSumCPPGlobal
{
	int CopyFile(const char* sfile, const char* dfile)
	{
		using std::string;
		int ret = 0;
		ret = access(dfile, F_OK);
		if(ret == 0)
		{
			errno = EEXIST;
			return -1;
		}
		errno = 0;
		string strTmp = dfile;
		size_t pos = strTmp.find_last_of("/");
		if(pos == string::npos)
		{
			strTmp.insert(0,".tmp_");
		}
		else
		{
			strTmp.insert(pos+1, ".tmp_");
		}

		char buf[1024*1024];
		FILE* fpSrc = NULL;
		FILE* fpDst = NULL;
		size_t rsize = 0;

		fpSrc = fopen(sfile, "rb");
		if(!fpSrc)
		{
			return -1;
		}
		fpDst = fopen(strTmp.c_str(), "wb");
		if(!fpDst)
		{
			fclose(fpSrc);
			return -1;
		}

		while( (rsize = fread(buf, 1, sizeof(buf), fpSrc)) > 0)
		{
			fwrite(buf, rsize, 1, fpDst);
		}
		if(ferror(fpSrc) || ferror(fpDst))
		{
			fclose(fpSrc);
			fclose(fpDst);
			return -1;
		}
		fclose(fpSrc);
		fclose(fpDst);
		ret = rename(strTmp.c_str(), dfile);
		if(ret < 0)
		{
			return ret;
		}
		return 0;
	}


	int MoveFile(const char* sfile, const char* dfile)
	{
		int ret = 0;
		ret = access(dfile, F_OK);
		if(ret == 0)
		{
			errno = EEXIST;
			return -1;
		}
		ret = rename(sfile, dfile);
		if(ret < 0 && errno == EXDEV)
		{
			ret = CopyFile(sfile, dfile);
			if(ret < 0)
			{
				return ret;
			}
			unlink(sfile);
			return 0;
		}
		return ret;
	}

	int CheckExist(const char* sfile)
	{
		int ret = 0;
		ret = access(sfile, F_OK);
		if(ret == 0)
		{
			errno = EEXIST;
			return 0;
		}
		else
		{
			return -1;
		}
	}
}

using namespace std;
using namespace DCTaskSumCPPGlobal;

int DCTaskSum::m_sdone = 0;
DCTaskSum::DCTaskSum()
{
	m_dbm = NULL;
	m_exit = 0;
}

DCTaskSum::~DCTaskSum()
{
}

int DCTaskSum::run()
{
	int ret = 0;
	ret = activate(THR_NEW_LWP | THR_JOINABLE | THR_INHERIT_SCHED | THR_SCOPE_SYSTEM, 1);
	if(ret < 0)
	{
		return -1;
	}
	return 0;
}

int DCTaskSum::init(DCDBManer* dmdb,int mod,int latnId)
{
	m_dbm = dmdb;
	m_mod = mod;
	m_cfg = DCExchCfg::instance()->GetConfig();
	m_latnId = latnId;
	m_checktime = time(NULL);
	m_check = m_checktime;
	RefreshOprList();

	// 获取ip地址
    char szHostName[128] = { 0 };
    struct hostent *hent = NULL;
    gethostname(szHostName, sizeof(szHostName));
    hent = gethostbyname(szHostName);
    m_strHostIp = inet_ntoa(*(struct in_addr*)(hent->h_addr_list[0]));
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "hostIp[%s]", m_strHostIp.c_str());

	if (mod == 0)
	{
		long lnM0CTaskCount = 0;
		char szSqlCode[128] = {0};
		sprintf(szSqlCode, "CountUndoTaskSum|%d", latnId);
		while (true)
		{
			UDBSQL *pCountSql = m_dbm->GetSQL(szSqlCode);
			if (!pCountSql)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "not find sqlcode[CountUndoTaskSum]");
				return -1;
			}

			try
			{
				pCountSql->UnBindParam();
				pCountSql->BindParam(1, m_strHostIp.c_str());
				pCountSql->Execute();
				if (pCountSql->Next())
				{
					pCountSql->GetValue(1, lnM0CTaskCount);
				}
			}
			catch (UDBException& e)
			{
				std::string strSql;
				pCountSql->GetSqlString(strSql);
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "SQL[%s]", strSql.c_str());
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "select execption[%s]", e.ToString());
				int iRet = DCExchDB::CheckDBState(m_dbm, m_checktime);
				if (iRet != 0)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", iRet);
					continue;
				}
				else
				{
					return -1;
				}
			}

			break;
		}

		
		DCExchGlobal::instance()->SetCount(G_EnM0CTaskSumCnt, lnM0CTaskCount);
		DCKpiMon *ptrBPMon = DCKpiSender::instance()->GetKPIHandle("BILLING", "REC", "MSGEX");
		char monGroup[50] = {0};
		DCKpiSender::instance()->GetFullGroup("TraceKpiA", m_latnId, monGroup);
		DCKpiSender::instance()->state_array_set(ptrBPMon, monGroup, "", "MXTdF", NULL, lnM0CTaskCount);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "kpi code[MXTdF] latnid[%d] init Cnt[%d]", latnId, lnM0CTaskCount);
	}

	return 0;
}
int DCTaskSum::exit()
{
	return m_exit;
}
void DCTaskSum::state(int done)
{
	m_sdone = done;
}
int DCTaskSum::svc()
{
	multimap<time_t,FileInfo> taskMulti;
	int iRefreshCnt = 0;
	int nPauseCnt(0);
	while(1)
	{
		SExchCfg* pcfg = DCExchCfg::instance()->GetConfig();
		if(m_cfg!=pcfg)
		{
			m_cfg = pcfg;
		}

		if(m_sdone)
		{
			m_exit++;
			break;
		}

		if (0 > DCExchDB::CheckDBState(m_dbm, m_checktime))
		{
			sleep(30);
			continue;
		}
		if(++iRefreshCnt % 1000 == 0)
		{
			time_t cursec = time(NULL);//每五分钟自动重连
			if((m_check+60*240) < cursec)
			{
				RefreshOprList();
				m_check = cursec;
				DCBIZLOG(DCLOG_LEVEL_INFO,0,"","RefreshOperList");
				iRefreshCnt = 0;
			}
		}

		DCPerfLogStatistic::instance()->output_timeing();

		if (m_cfg->nSysStateFlag == 1) // 暂停状态不读取任务不处理
		{
			sleep(1);
			if ( 0 == (nPauseCnt++ % 600) )
			{
				DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "process state was set to 1, pausing");
				nPauseCnt = 0;
			}
			continue;
		}

		taskMulti.clear();
		int ret = GetFiles(taskMulti);
		if(ret < 0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","scan file error");
			DCKpiMon *ptrBPMon = DCKpiSender::instance()->GetKPIHandle("BILLING", "REC", "MSGEX");
			char monGroup[50] = {0};
			DCKpiSender::instance()->GetFullGroup("TraceKpi", m_latnId, monGroup);
			DCKpiSender::instance()->cycle_array_inc(ptrBPMon, monGroup, "", "MXEc", "2001", 1);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "kpi code[MXEc] latnid[%d] err[2001] msg[scan file error]", m_latnId);
		}
		if(taskMulti.size() == 0)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","have not file");
			sleep(1);
			continue;
		}
		ret = ArrangeFile(taskMulti);
		if(ret < 0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","arrange file error");
			DCKpiMon *ptrBPMon = DCKpiSender::instance()->GetKPIHandle("BILLING", "REC", "MSGEX");
			char monGroup[50] = {0};
			DCKpiSender::instance()->GetFullGroup("TraceKpi", m_latnId, monGroup);
			DCKpiSender::instance()->cycle_array_inc(ptrBPMon, monGroup, "", "MXEc", "2004", 1);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "kpi code[MXEc] latnid[%d] err[2004] msg[arrange file error]", m_latnId);
			return -1;
		}
	}
	return 0;
}
int DCTaskSum::GetFiles(multimap<time_t,FileInfo> &fileMulti)
{
	// check the parameter !
	if( 0 == m_cfg->scan_dir.length())
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","scan_dir is null");
		return -1;
	}
	string sumType = m_cfg->sumType;
	size_t pos;
	string scanDir;
	regex_t ex;
	char errormsg[256] = {0};
	int errorsize = 256;
	string reg  = m_cfg->sum_reg;
	int isreg_error=regcomp(&ex,reg.c_str(),REG_EXTENDED);
	if(isreg_error!=0)
	{
		regerror(isreg_error,&ex,errormsg,errorsize);
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","regerror[%s]",errormsg);
		return -1;
	}
	char sLatn[10] = {0};
	sprintf(sLatn,"%d",m_latnId);
	scanDir = m_cfg->scan_dir;
	string stringLatnid = "[latnId]";
	string sBillingCycle = "[BillingCycle]";
	pos = scanDir.find("[latnid]");
	if(pos == string::npos)
	{
		pos = scanDir.find(stringLatnid);
	}
	if(pos != string::npos)
	{
		scanDir.replace(pos,stringLatnid.length(),sLatn);
	}
	string iRoungeTemp = scanDir;
	for(int iRounge = 0;iRounge < 2;iRounge++)
	{
		scanDir = iRoungeTemp;
		pos = scanDir.find(sBillingCycle);
		if(pos != string::npos)
		{
			scanDir.replace(pos,sBillingCycle.length(),getBillingCycle(iRounge));
		}
		else
		{
			iRounge++;
		}
		string scanDirTemp = scanDir;
		string sumTypeTemp;
		sumType = m_cfg->sumType;
		while(sumType.length())
		{
			pos = sumType.find(",");
			scanDir = scanDirTemp;
			if(pos == string::npos)
			{

				sumTypeTemp = sumType;
				if(sumTypeTemp.find("realtime") != string::npos)
				{
					size_t n_pos = scanDir.find_last_of("/");
					scanDir = scanDir.substr(0,n_pos);
					scanDir = scanDir + '/' + sumTypeTemp + '/' + "common";
				}
				else
				{
					scanDir = scanDir + '/' + sumTypeTemp;
				}
				sumType.clear();
			}
			else
			{

				sumTypeTemp = sumType.substr(0,pos);
				if(sumTypeTemp.find("realtime") != string::npos)
				{
					size_t npos = scanDir.find_last_of("/");
					scanDir = scanDir.substr(0,npos);
					scanDir = scanDir + '/' + sumTypeTemp + '/' + "common";
				}
				else
				{
					scanDir = scanDir + '/' + sumTypeTemp;
				}
				sumType = sumType.substr(pos+1,sumType.length());
			}
			// check if dir_name is a valid dir
			char dir_name[512] = {0};
			sprintf(dir_name,"%s",scanDir.c_str());
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","dir_name[%s]",dir_name);
			struct stat s;
			lstat(dir_name, &s);
			if(!S_ISDIR(s.st_mode))
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","dir_name is not a valid directory[%s]",dir_name);
				continue;
			}

			struct dirent * filename;		// return value for readdir()
			DIR * dir;						// return value for opendir()
			dir = opendir(dir_name);
			if(NULL == dir)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","Can not open dir[%s]",dir_name);
				DCKpiMon *ptrBPMon = DCKpiSender::instance()->GetKPIHandle("BILLING", "REC", "MSGEX");
				char monGroup[50] = {0};
				DCKpiSender::instance()->GetFullGroup("TraceKpi", m_latnId, monGroup);
				DCKpiSender::instance()->cycle_array_inc(ptrBPMon, monGroup, "", "MXEc", "2002", 1);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "kpi code[MXEc] latnid[%d] err[2002] msg[Can not open dir:%s]", m_latnId, dir_name);
				continue;
			}

			/* read all the files in the dir ~ */
			while((filename = readdir(dir)) != NULL)
			{
				// get rid of "." and ".."

				//delete filename;
				//filename = NULL;
				if(strcmp(filename->d_name, ".") == 0 || strcmp(filename->d_name, "..") == 0)
					continue;

				if(regexec(&ex,filename->d_name,0,NULL,0))
					continue;
				string sfileName = filename->d_name;
				FileInfo fileInfo;
				fileInfo.fileName = scanDir + '/' + sfileName;
				if(ResolveFile(sfileName,fileInfo)<0)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","file name error[%s]",sfileName.c_str());
					DCKpiMon *ptrBPMon = DCKpiSender::instance()->GetKPIHandle("BILLING", "REC", "MSGEX");
					char monGroup[50] = {0};
					DCKpiSender::instance()->GetFullGroup("TraceKpi", m_latnId, monGroup);
					DCKpiSender::instance()->cycle_array_inc(ptrBPMon, monGroup, "", "MXEc", "2003", 1);
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "kpi code[MXEc] latnid[%d] err[2003] msg[scan file:%s , file name error]", m_latnId, sfileName.c_str());
					continue;
				}
				if(fileInfo.mod % m_cfg->arrangeThreadnum != m_mod)
					continue;
				struct stat fileStat;
				stat(filename->d_name,&fileStat);
				fileInfo.fileType = sumTypeTemp;
				if(sumTypeTemp.find("realtime") != string::npos)
				{
					fileInfo.isRealTime = 1;
				}
				else
				{
					fileInfo.isRealTime = 0;
				}
				fileMulti.insert(make_pair(fileStat.st_mtime,fileInfo));
				DCKpiMon *ptrBPMon = DCKpiSender::instance()->GetKPIHandle("BILLING", "REC", "MSGEX");
				char monGroup[50] = {0};
				DCKpiSender::instance()->GetFullGroup("TraceKpi", m_latnId, monGroup);
				DCKpiSender::instance()->cycle_array_inc(ptrBPMon, monGroup, "", "MXCr", NULL, 1);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "kpi code[MXCr] latnid[%d] inc", m_latnId);
			}
			closedir(dir);
		}
	}
	regfree(&ex);
	return 0;
}
int DCTaskSum::ResolveFile(string sfileName,FileInfo &fileInfo)
{
	try
	{
		size_t pos1 = sfileName.find("_");
		sfileName = sfileName.substr(pos1 + 1,sfileName.length());
		pos1 = sfileName.find("_");
		sfileName = sfileName.substr(pos1 + 1,sfileName.length());
		size_t pos2 = sfileName.find("_");
		sfileName =  sfileName.substr(pos2 + 1,sfileName.length());
		pos2 = sfileName.find("_");
		string tempfileName = sfileName.substr(0,pos2);
		fileInfo.latnId = atoi(tempfileName.c_str());
		sfileName = sfileName.substr(pos2 + 1,sfileName.length());
		pos2 = sfileName.find("_");
		tempfileName = sfileName.substr(0,pos2);
		fileInfo.billingCycleId = atoi(tempfileName.c_str());
		sfileName =  sfileName.substr(pos2 + 1,sfileName.length());
		pos2 = sfileName.find("_");
		tempfileName = sfileName.substr(0,pos2);
		fileInfo.operlistId = atoi(tempfileName.c_str());
		sfileName =  sfileName.substr(pos2 + 1,sfileName.length());
		pos2 = sfileName.find("_");
		sfileName =  sfileName.substr(pos2 + 1,sfileName.length());
		pos2 = sfileName.find("_");
		tempfileName = sfileName.substr(0,pos2);
		fileInfo.batchId = tempfileName;
		sfileName = sfileName.substr(pos2 + 1,sfileName.length());
		pos2 = sfileName.find("_");
		tempfileName = sfileName.substr(0,pos2);
		fileInfo.mod = atoi(tempfileName.c_str());
	}
	catch(...)
	{
		return -1;
	}
	return 0;
}
void DCTaskSum::GetBuf(TAccRecord & recordBuf,string & line)
{
	sscanf(line.c_str(),"%ld|%ld|%ld|%ld|%ld|%ld|%ld|%ld|%ld|%d|%d|%d|%d",
		&recordBuf.nServId,
		&recordBuf.nAcctID,
		&recordBuf.nAcctItemTypeId,
		&recordBuf.nOfferId,
		&recordBuf.nDuration,
		&recordBuf.nBillingDuration,
		&recordBuf.nCounts,
		&recordBuf.nCalls,
		&recordBuf.nFlux,
		&recordBuf.day,
		&recordBuf.nBelongDay,
		&recordBuf.nBillingMode,
		&recordBuf.nBelongBillingCycleId);
	string temp = line;
	for(int i = 0;i < 13; i++)
	{
		size_t pos = temp.find("|");
		temp = temp.substr(pos+1,temp.length());
	}

    bool IsEnd = false;
	recordBuf.uid.clear();
	recordBuf.cServiceNbr.clear();
	recordBuf.nStrategyId = 0;
	string value;
	for(int i = 0;i < 18;i++)
	{
		size_t pos = temp.find("|");
		value = temp.substr(0,pos);
		temp = temp.substr(pos+1,temp.length());
		if (i == 15)
		{
		    if (pos == string::npos)
			{
			   IsEnd = true;
			}
		}
		else if (i == 16 || i == 17)
		{
			if (IsEnd)
			{
			   value = string("0");
			}
		}
		switch(i)
		{
		case 0:
			recordBuf.cFeeType = value;
			break;
		case 1:
			recordBuf.cPayFlag = value;
			break;
		case 2:
			recordBuf.nDisounctCharge = atol(value.c_str());
			break;
		case 3:
			recordBuf.nOriCharge = atol(value.c_str());
			break;
		case 4:
		case 5:
		case 6:
			break;
		case 7:
			recordBuf.nModGroupId = atol(value.c_str());
			break;
		case 8:
			recordBuf.if_realtime_disct = atoi(value.c_str());
			break;
		case 9:
			recordBuf.sourceInstId = atol(value.c_str());
			break;
		case 10:
			recordBuf.custId = value;
			break;
		case 11:
			recordBuf.uid = value;
			break;
		case 12:
			recordBuf.cServiceNbr = value;
			break;
		case 13:
			recordBuf.nStrategyId = atol(value.c_str());
			break;	
		case 14:
			recordBuf.nPlatform = atoi(value.c_str());;
			break;
		case 15:
			recordBuf.nOnLineMode5G = atoi(value.c_str());
			break;
		case 16:
			recordBuf.HprecisionCharge = atof(value.c_str());;
			break;
		case 17:
			recordBuf.IfCloud = atoi(value.c_str());
			break;
		}
	}

	DCBIZLOG(DCLOG_LEVEL_WARN, 0, "", "platform[%d], onlineMode5G[%d], HprecisionCharge[%lf], IfCloud[%d].", recordBuf.nPlatform, recordBuf.nOnLineMode5G,recordBuf.HprecisionCharge,recordBuf.IfCloud);

	/*
	size_t pos = temp.find("|");
	if(pos != string::npos)
	{
		value = temp.substr(0,pos);
		temp = temp.substr(pos+1,temp.length());

		recordBuf.nPlatform = atoi(value.c_str());
		recordBuf.nOnLineMode5G = atoi(temp.c_str());
	}
	else
	{
		if (temp.size() > 0)
		{
			recordBuf.nPlatform = atoi(temp.c_str());
			DCBIZLOG(DCLOG_LEVEL_WARN, 0, "", "platform[%d], onlineMode5G[%d].", recordBuf.nPlatform, recordBuf.nOnLineMode5G);
			return;
		}

		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "file format error, lack of field.");
	}
	*/

	return;
}
void DCTaskSum::SetBuf(char* line,const TAccRecordCommon &common,TAccRecordCharge &charge)
{
	sprintf(line,"%ld|%ld|%ld|%ld|%ld|%ld|%ld|%ld|%ld|%d|%d|%d|%d|%s|%s|%ld|%ld|%ld|%d|%ld|%s|%s|%s|%ld|%d|%d|%.4f|%d",
		common.nServId,
		common.nAcctID,
		common.nAcctItemTypeId,
		common.nOfferId,
		charge.nDuration,
		charge.nBillingDuration,
		charge.nCounts,
		charge.nCalls,
		charge.nFlux,
		common.day,
		common.nBelongDay,
		common.nBillingMode,
		common.nBelongBillingCycleId,
		common.cFeeType.c_str(),
		common.cPayFlag.c_str(),
		charge.nDisounctCharge,
		charge.nOriCharge,
		common.nModGroupId,
		common.if_realtime_disct,
		common.sourceInstId,
		charge.custId.c_str(),
		charge.uid.c_str(),
		common.cServiceNbr.c_str(),
		charge.nStrategyId,
		common.nPlatform,
		charge.nOnLineMode5G,
		charge.HprecisionCharge,
		common.IfCloud);

	return;
}
int DCTaskSum::ArrangeFile(multimap<time_t,FileInfo> &fileNameMulti)
{
	map<TAccRecordCommon,TAccRecordCharge>*          sumMap;    //记录map
	map<TAccRecordCommon,TAccRecordCharge>::iterator mapIter;
	map<string,SumDetailsFile>                       sumFileMap;	//文件合并map
	map<string,SumDetailsFile>::iterator             sumFileIter;
	multimap<time_t,FileInfo>::iterator              multiMapIter;
	ifstream                                         ifile;
	ofstream                                         ofile;
	TAccRecord                                       recordBuf;
	TAccRecordCommon                                 recordCommon;
	TAccRecordCharge                                 recordCharge;
	int                                              isMerge = m_cfg->isMerge;
	//char                                             syscommand[1024] = { 0 };

	m_iChargeFilesId = 0;
	memset(m_sBatchID, 0x00, sizeof m_sBatchID);
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","get fileNum[%u],isMerge[%d]",fileNameMulti.size(),isMerge);
	
	for(multiMapIter = fileNameMulti.begin(); multiMapIter != fileNameMulti.end(); multiMapIter++)
	{
		// 处理前先判断原文件是否已经在备份路径下有记录
		string filename = multiMapIter->second.fileName;
		
		if (0 != CheckDupFile(m_cfg, multiMapIter))
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","file[%s] back/error path exist same file, continue",filename.c_str());
			continue;
		}

		string fileKey;
		if(isMerge)
		{
			fileKey = multiMapIter->second.fileType + '|' + multiMapIter->second.batchId;
		}
		else
		{
			fileKey = "1";
		}
		sumMap = &(sumFileMap[fileKey].sumMap);
		TAccRecordCharge chargeAll;
		//string filename = multiMapIter->second.fileName;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","start deal file[%s]",filename.c_str());
		ifile.open(filename.c_str());
		if(!ifile.is_open())
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","Can not open file[%s]",filename.c_str());
			continue;
		}
		string sline;
		int oriRecord = 0;
		while(ifile && getline(ifile,sline))
		{
			if(sline.length()==0)
			{
				break;
			}
			GetBuf(recordBuf,sline);
			recordCommon<<recordBuf;
			recordCharge<<recordBuf;
			if(multiMapIter->second.isRealTime == 0)
			{
				(*sumMap)[recordCommon] += recordCharge;
			}
			chargeAll += recordCharge;
			oriRecord++;
		}
		ifile.close();
		if(isMerge)
		{
			sumFileIter = sumFileMap.find(fileKey);
			sumFileIter->second.vSumFileInfo.push_back(multiMapIter->second);
			sumFileIter->second.vChargeAll.push_back(chargeAll);
			sumFileIter->second.chargeAll += chargeAll;
			sumFileIter->second.vOriRecord.push_back(oriRecord);
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","nDuration[%ld],nBillingDuration[%ld],nCounts[%ld],nCalls[%ld],nFlux[%ld],nDisounctCharge[%ld],nOriCharge[%ld],HprecisionCharge[%f]",chargeAll.nDuration,chargeAll.nBillingDuration,chargeAll.nCounts,chargeAll.nCalls,chargeAll.nFlux,chargeAll.nDisounctCharge,chargeAll.nOriCharge,chargeAll.HprecisionCharge);
			continue;
		}
		size_t npos;
		npos = multiMapIter->second.fileName.find_last_of("/");
		string scandir = multiMapIter->second.fileName.substr(0,npos);
		multiMapIter->second.fileName = multiMapIter->second.fileName.substr(npos + 1,128);

		string bakName;
		if(multiMapIter->second.isRealTime == 0)
		{
			string detail_dir = CheckAndCreateDir(m_cfg->detail_dir, multiMapIter->second.billingCycleId, multiMapIter->second.fileType);
			npos = multiMapIter->second.fileName.find("sum_");
			string defileFileName = multiMapIter->second.fileType + "_detail_"  + multiMapIter->second.fileName.substr(npos + 4,multiMapIter->second.fileName.length());
			string defileFile = detail_dir + '/' + defileFileName;
			ofile.open(defileFile.c_str());
			if(!ofile.is_open())
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","Can not open file[%s]",defileFile.c_str());
				return -1;
			}
			char buf[512] = {0};
			for(mapIter = sumMap->begin();mapIter != sumMap->end();mapIter++)
			{
				SetBuf(buf,mapIter->first,mapIter->second);
				ofile<<buf<<endl;
			}
			ofile.close();
			bakName = CheckAndCreateDir(m_cfg->bak_dir, multiMapIter->second.billingCycleId, multiMapIter->second.fileType);
			int nRet = InsertChargeFilesDetails(defileFileName,chargeAll,multiMapIter->second,oriRecord,sumMap->size(),bakName,detail_dir);
			bakName = bakName + '/' + multiMapIter->second.fileName;
			if(nRet < 0)
			{
				bakName = CheckAndCreateDir(m_cfg->error_dir, multiMapIter->second.billingCycleId, multiMapIter->second.fileType);
				bakName = bakName + '/' + multiMapIter->second.fileName;
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "InsertChargeFilesDetails failed, ChargeFilesID=%ld, batchid=%s, defilename=%s orifilename=%s", m_iChargeFilesId, m_sBatchID, defileFileName.c_str(), filename.c_str());
			}
		}
		else
		{
			bakName = CheckAndCreateDir(m_cfg->bak_dir, multiMapIter->second.billingCycleId, "realtimesum");
			int nRet = InsertChargeFiles(chargeAll,multiMapIter->second,oriRecord,bakName);
			bakName = bakName + '/' + multiMapIter->second.fileName;
			if(nRet < 0)
			{
				bakName = CheckAndCreateDir(m_cfg->error_dir, multiMapIter->second.billingCycleId, "realtimesum");
				bakName = bakName + '/' + multiMapIter->second.fileName;
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "InsertChargeFiles failed");
			}
		}
		/*
		// rename系统调用用于在同一个文件系统中做文件的rename操作。如果源和目的在不同mount点上，rename会返回错误EXDEV
		// rename(filename.c_str(),bakName.c_str());
		sprintf(syscommand, "mv %s %s", filename.c_str(), bakName.c_str());
		pid_t status;
		status = system(syscommand);
		if (status == -1 || !WIFEXITED(status) || WEXITSTATUS(status))
		{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "syscommand[%s] failed, return:%d, error message:%s", syscommand, status, strerror(errno));
		}
		// system调用mv指令还是会失败，这个坑没填完啊兄dei~~~改用自定义的move函数
		*/
		int status = MoveFile(filename.c_str(), bakName.c_str());
		if (0 > status )
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "MoveFile[%s] failed, return:%d, error message:%s", filename.c_str(), status, strerror(errno));
		}

		sumMap->clear();
	}

	m_iChargeFilesId = 0;
	memset(m_sBatchID, 0x00, sizeof m_sBatchID);
	if(!isMerge)
	{
		return 0;
	}

	for(sumFileIter = sumFileMap.begin();sumFileIter != sumFileMap.end(); sumFileIter++)
	{
		long billingCycleId = sumFileIter->second.vSumFileInfo[0].billingCycleId;
		string fileType = sumFileIter->second.vSumFileInfo[0].fileType;
		string detail_dir = CheckAndCreateDir(m_cfg->detail_dir, billingCycleId, fileType);
		string defileFileName = fileType + "_detail_" + sumFileIter->second.vSumFileInfo[0].batchId;
		char smod[10] = {0};
		sprintf(smod,"_%03d",m_mod);
		defileFileName = defileFileName + smod + ".sum";
		string defileFile = detail_dir + '/' + defileFileName;
		ofile.open(defileFile.c_str());
		if(!ofile.is_open())
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","Can not open file[%s]",defileFile.c_str());
			return -1;
		}
		char buf[512] = {0};
		for(mapIter = sumFileIter->second.sumMap.begin();mapIter != sumFileIter->second.sumMap.end();mapIter++)
		{
			SetBuf(buf,mapIter->first,mapIter->second);
			ofile<<buf<<endl;
		}
		ofile.close();
		for(unsigned int i = 0; i < sumFileIter->second.vSumFileInfo.size(); i++)
		{
			size_t npos;
			npos = sumFileIter->second.vSumFileInfo[i].fileName.find_last_of("/");
			string scandir = sumFileIter->second.vSumFileInfo[i].fileName;
			sumFileIter->second.vSumFileInfo[i].fileName = sumFileIter->second.vSumFileInfo[i].fileName.substr(npos + 1,128);
			string bakDir;
			bakDir = CheckAndCreateDir(m_cfg->bak_dir, billingCycleId, fileType);
			int nRet = InsertChargeFilesDetails(defileFileName,sumFileIter->second.vChargeAll[i],sumFileIter->second.vSumFileInfo[i],sumFileIter->second.vOriRecord[i],sumMap->size(),bakDir,detail_dir,i);
			if(nRet < 0)
			{
				bakDir = CheckAndCreateDir(m_cfg->error_dir, billingCycleId, fileType);
			}

			string fileName = bakDir + '/' + sumFileIter->second.vSumFileInfo[i].fileName;
			//sprintf(syscommand, "mv %s %s", scandir.c_str(), fileName.c_str());
			//pid_t status;
			//status = system(syscommand);
			//if (status == -1 || !WIFEXITED(status) || WEXITSTATUS(status))
			//{
			//    DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "syscommand[%s] failed, return:%d, error message:%s", syscommand, status, strerror(errno));
			//}

			int status = MoveFile(scandir.c_str(), fileName.c_str());
			if ( 0 > status )
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "MoveFile[%s] failed, return:%d, error message:%s", scandir.c_str(), status, strerror(errno));
			}

			//rename(scandir.c_str(),fileName.c_str());
		}
		TAccRecordCharge chargeAlldetails = sumFileIter->second.chargeAll;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","nDuration[%ld],nBillingDuration[%ld],nCounts[%ld],nCalls[%ld],nFlux[%ld],nDisounctCharge[%ld],nOriCharge[%ld],HprecisionCharge[%f]",chargeAlldetails.nDuration,chargeAlldetails.nBillingDuration,chargeAlldetails.nCounts,chargeAlldetails.nCalls,chargeAlldetails.nFlux,chargeAlldetails.nDisounctCharge,chargeAlldetails.nOriCharge,chargeAlldetails.HprecisionCharge);
	}
	return 0;
}

int DCTaskSum::InsertChargeFiles(TAccRecordCharge &charge,FileInfo fileInfo,int oriRecord,string scandir)
{
	string tableCode;
	map<int,string>::iterator iter = m_oprlist.find(fileInfo.operlistId);
	if(iter != m_oprlist.end())
	{
		tableCode = iter->second;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","no find operlistid[%d]",fileInfo.operlistId);
	}
	char sql[32]={0};
	sprintf(sql,"InsertChargeFilesRealTime|%d",fileInfo.latnId);

	while (1)
	{
		UDBSQL* stmt = m_dbm->GetSQL(sql);
		if(stmt == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","GetSQL[InsertChargeFilesRealTime] failed.");
			return -1;
		}
		try
		{
			stmt->UnBindParam();
			stmt->BindParam(1, fileInfo.batchId.c_str());
			stmt->BindParam(2, scandir.c_str());
			stmt->BindParam(3, fileInfo.fileName.c_str());
			stmt->BindParam(4, oriRecord);
			stmt->BindParam(5, charge.nOriCharge);
			stmt->BindParam(6, charge.nDisounctCharge);
			stmt->BindParam(7, charge.nDuration);
			stmt->BindParam(8, charge.nFlux);
			stmt->BindParam(9, charge.nCounts);
			stmt->BindParam(10, charge.nCalls);
			stmt->BindParam(11, fileInfo.billingCycleId);
			stmt->BindParam(12, tableCode.c_str());
			stmt->Execute();
			stmt->Connection()->Commit();
		}
		catch(UDBException& e)
		{
			std::string sql;
			stmt->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR,0, "", "sql[%s]",sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "InsertChargeFiles,execption[%s]" ,e.ToString());
			try { stmt->Connection()->Rollback(); }
			catch(...){}
			DCExchDB::CheckDBState(m_dbm, m_checktime);
			return -1;
		}

		break;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "InsertChargeFiles,filename:%s",fileInfo.fileName.c_str());
	return 0;
}

int DCTaskSum::InsertChargeFilesDetails(string filename,TAccRecordCharge &charge,FileInfo fileInfo,int oriRecord,int record,string scandir,string detailDir,int isDetails)
{
	long iChargeFilesID = 0;

	while (1)
	{
		UDBSQL* pQuery = m_dbm->GetSQL("getChargeFilesId");
		if(pQuery == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","GetSQL[getChargeFilesId] failed.");
			return -1;
		}
		try
		{
			pQuery->UnBindParam();
			pQuery->Execute();
			if(pQuery->Next())
			{
				pQuery->GetValue(1, iChargeFilesID);
			}
		}
		catch(UDBException& e)
		{
			std::string sql;
			pQuery->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR,0, "", "sql[%s]",sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "getChargeFilesId,execption[%s]" ,e.ToString());
			int _ret = DCExchDB::CheckDBState(m_dbm, m_checktime);
			if (_ret != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", _ret);
				continue;
			}
			else
			{
				return -1;
			}
		}

		break;
	}

	string tableCode;
	map<int,string>::iterator iter = m_oprlist.find(fileInfo.operlistId);
	if(iter != m_oprlist.end())
	{
		tableCode = iter->second;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","no find operlistid[%d]",fileInfo.operlistId);
	}
	char sql[32]={0};

	while (1)
	{
		sprintf(sql,"InsertChargeFiles|%d",fileInfo.latnId);
		UDBSQL* stmt = m_dbm->GetSQL(sql);
		if(stmt == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","GetSQL[InsertChargeFiles] failed.");
			return -1;
		}
		try
		{
			stmt->UnBindParam();
			stmt->BindParam(1, iChargeFilesID);
			stmt->BindParam(2, fileInfo.batchId.c_str());
			stmt->BindParam(3, scandir.c_str());
			stmt->BindParam(4, fileInfo.fileName.c_str());
			stmt->BindParam(5, oriRecord);
			stmt->BindParam(6, detailDir.c_str());
			stmt->BindParam(7, filename.c_str());
			stmt->BindParam(8, record);
			stmt->BindParam(9, charge.nOriCharge);
			stmt->BindParam(10, charge.nDisounctCharge);
			stmt->BindParam(11, charge.nDuration);
			stmt->BindParam(12, charge.nFlux);
			stmt->BindParam(13, charge.nCounts);
			stmt->BindParam(14, charge.nCalls);
			stmt->BindParam(15, fileInfo.billingCycleId);
			stmt->BindParam(16, tableCode.c_str());
			stmt->Execute();
			stmt->Connection()->Commit();
		}
		catch(UDBException& e)
		{
			std::string sql;
			stmt->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR,0, "", "sql[%s]",sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "InsertChargeFiles,execption[%s]" ,e.ToString());
			try { stmt->Connection()->Rollback(); }
			catch(...){}
			DCExchDB::CheckDBState(m_dbm, m_checktime, true);
			return -1;
		}

		break;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "InsertChargeFiles,filename:%s",filename.c_str());
	if(isDetails)
	{
		return 0;
	}

	while (1)
	{
		sprintf(sql,"InsertChargeFilesDetails|%d",fileInfo.latnId);
		UDBSQL* stmtdetail = m_dbm->GetSQL(sql);
		if(stmtdetail == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","GetSQL[InsertChargeFilesDetails] failed.");
			return -1;
		}
		try
		{
			stmtdetail->UnBindParam();
			stmtdetail->BindParam(1, iChargeFilesID);
			stmtdetail->BindParam(2, fileInfo.batchId.c_str());
			stmtdetail->BindParam(3, detailDir.c_str());
			stmtdetail->BindParam(4, filename.c_str());
			stmtdetail->BindParam(5, fileInfo.latnId);
			stmtdetail->BindParam(6, fileInfo.billingCycleId);
			stmtdetail->BindParam(7, "M0C");
			stmtdetail->BindParam(8, m_strHostIp.c_str());
			stmtdetail->Execute();
			stmtdetail->Connection()->Commit();
			long lnCount = DCExchGlobal::instance()->AddCount(G_EnM0CTaskSumCnt, 1);
			DCKpiMon *ptrBPMon = DCKpiSender::instance()->GetKPIHandle("BILLING", "REC", "MSGEX");
			char monGroup[50] = {0};
			DCKpiSender::instance()->GetFullGroup("TraceKpiA", m_latnId, monGroup);
			DCKpiSender::instance()->state_array_set(ptrBPMon, monGroup, "", "MXTdF", NULL, lnCount);
			m_iChargeFilesId = iChargeFilesID;
			strcpy(m_sBatchID, fileInfo.batchId.c_str());
		}
		catch(UDBException& e)
		{
			std::string sql;
			stmtdetail->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR,0, "", "sql[%s]",sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "InsertChargeFilesDetails,execption[%s]" ,e.ToString());
			try { stmtdetail->Connection()->Rollback(); }
			catch(...){}
			DCExchDB::CheckDBState(m_dbm, m_checktime, true);
			return -1;
		}

		break;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "InsertChargeFilesDetails,filename:%s",filename.c_str());
	return 0;
}

string DCTaskSum::getBillingCycle(int n)
{
	tm *p;
	time_t timep;
	time(&timep);
	p = localtime(&timep);
	if(n == 1)
	{
		if(p->tm_mon - 1 < 0)
		{
			p->tm_mon = 11;
			p->tm_year --;
		}
		else
		{
			p->tm_mon --;
		}

	}
	char v_date[7] = {0};
	sprintf(v_date,"%04d%02d",(1900+p->tm_year) , (1+p->tm_mon));
	string date = v_date;
	return date;
}

int DCTaskSum::RefreshOprList()
{
	while (1)
	{
		m_oprlist.clear();
		UDBSQL *pQuery = m_dbm->GetSQL("refreshOperList");
		int operlistId;
		string tableCode;
		if(pQuery == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "no find sql[refreshOperList]");
			return -1;
		}
		try
		{
			pQuery->UnBindParam();
			pQuery->Execute();
			while(pQuery->Next())
			{
				pQuery->GetValue(1,operlistId);
				pQuery->GetValue(2,tableCode);
				m_oprlist.insert(make_pair(operlistId,tableCode));
				DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "get operlistId[%d],tablecode[%s]",operlistId,tableCode.c_str());
			}
		}
		catch(UDBException& e)
		{
			std::string sql;
			pQuery->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR,0, "", "sql[%s]",sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "execption[%s]" ,e.ToString());
			int _ret = DCExchDB::CheckDBState(m_dbm, m_checktime);
			if (_ret != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", _ret);
				continue;
			}
			else
			{
				return -1;
			}
		}

		break;
	}

	return 0;
}

string DCTaskSum::CheckAndCreateDir(string rootDir,int billingCycleId,string fileType)
{

	char firstCheck[128] = {0};
	sprintf(firstCheck, "%s/%d/%d/%s", rootDir.c_str(), m_latnId,billingCycleId, fileType.c_str());
	string dir = rootDir;
	if(access(dir.c_str(),F_OK))
	{
		mkdir(dir.c_str(),S_IRWXU|S_IRGRP|S_IXGRP|S_IROTH|S_IXOTH);
	}
	char sTemp[10] = {0};
	sprintf(sTemp,"%d",m_latnId);
	dir = dir + '/' + sTemp;
	if(access(dir.c_str(),F_OK))
	{
		mkdir(dir.c_str(),S_IRWXU|S_IRGRP|S_IXGRP|S_IROTH|S_IXOTH);
	}
	sprintf(sTemp,"%d",billingCycleId);
	dir = dir + '/' + sTemp;
	if(access(dir.c_str(),F_OK))
	{
		mkdir(dir.c_str(),S_IRWXU|S_IRGRP|S_IXGRP|S_IROTH|S_IXOTH);
	}
	if(fileType.length() > 0)
	{
		dir = dir + '/' + fileType;
		if(access(dir.c_str(),F_OK))
		{
			mkdir(dir.c_str(),S_IRWXU|S_IRGRP|S_IXGRP|S_IROTH|S_IXOTH);
		}
	}
	return dir;
}

int DCTaskSum::CheckDupFile(const SExchCfg* vi_pExchCfg, multimap<time_t,FileInfo>::iterator vi_pIterator)
{
	char firstCheck[256] = {0};
	char firstCheckfile[256] = {0};
	char sbakFileName[128] = {0};
	string sOriFileName;

//	sprintf(firstCheck, "%s/%d/%d/%s", rootDir.c_str(), m_latnId,billingCycleId, fileType.c_str());

	size_t npos;
	npos = vi_pIterator->second.fileName.find_last_of("/");
	sOriFileName = vi_pIterator->second.fileName.substr(npos + 1,128);

	if (vi_pIterator->second.isRealTime == 0)
	{
		// 查找备份路径下是否有重名文件
		if (0 < vi_pIterator->second.fileType.length())
		{
			sprintf(firstCheck, "%s/%d/%d/%s/", vi_pExchCfg->bak_dir.c_str(), m_latnId, vi_pIterator->second.billingCycleId, vi_pIterator->second.fileType.c_str());
			sprintf(firstCheckfile, "%s%s", firstCheck, sOriFileName.c_str());
		}
		else
		{
			sprintf(firstCheck, "%s/%d/%d/", vi_pExchCfg->bak_dir.c_str(), m_latnId, vi_pIterator->second.billingCycleId);
			sprintf(firstCheckfile, "%s%s", firstCheck, sOriFileName.c_str());
		}

		if (0 == CheckExist(firstCheckfile))
		{
			// 已经存在
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "BakPathFile [%s] is Already Exist", firstCheckfile);

			MulitBackFile(firstCheck, vi_pIterator->second.fileName.c_str(), vi_pIterator);
			return -1;
		}

		// 查找错误路径下是否有重名文件
		memset(firstCheck, 0x00, sizeof(firstCheck));
		memset(firstCheckfile, 0x00, sizeof(firstCheckfile));
		if (0 < vi_pIterator->second.fileType.length())
		{
			sprintf(firstCheck, "%s/%d/%d/%s/", vi_pExchCfg->error_dir.c_str(), m_latnId, vi_pIterator->second.billingCycleId, vi_pIterator->second.fileType.c_str());
			sprintf(firstCheckfile, "%s%s", firstCheck, sOriFileName.c_str());
		}
		else
		{
			sprintf(firstCheck, "%s/%d/%d/", vi_pExchCfg->error_dir.c_str(), m_latnId, vi_pIterator->second.billingCycleId);
			sprintf(firstCheckfile, "%s%s", firstCheck, sOriFileName.c_str());
		}

		if (0 == CheckExist(firstCheckfile))
		{
			// 已经存在
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "BakPathFile [%s] is Already Exist", firstCheckfile);
//			MulitBackFile(firstCheck, firstCheckfile, vi_pIterator);
//			return -1;
		}
	}
	else
	{
		// 查找备份路径下是否有重名文件
//		if (0 < vi_pIterator->second.fileType.length())
//		{
			sprintf(firstCheck, "%s/%d/%d/%s/", vi_pExchCfg->bak_dir.c_str(), m_latnId, vi_pIterator->second.billingCycleId, "realtimesum");
			sprintf(firstCheckfile, "%s%s", firstCheck, sOriFileName.c_str());
//		}
//		else
//		{
//			sprintf(firstCheck, "%s/%d/%d/", vi_pExchCfg->bak_dir.c_str(), m_latnId, vi_pIterator->second.billingCycleId);
//			sprintf(firstCheckfile, "%s%s", firstCheck, vi_pIterator->second.fileName.c_str());
//		}

		if (0 == CheckExist(firstCheckfile))
		{
			// 已经存在
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "BakPathFile [%s] is Already Exist", firstCheckfile);

			MulitBackFile(firstCheck, vi_pIterator->second.fileName.c_str(), vi_pIterator);
			return -1;
		}

		// 查找错误路径下是否有重名文件
		memset(firstCheck, 0x00, sizeof(firstCheck));
		memset(firstCheckfile, 0x00, sizeof(firstCheckfile));
		sprintf(firstCheck, "%s/%d/%d/%s/", vi_pExchCfg->error_dir.c_str(), m_latnId, vi_pIterator->second.billingCycleId, "realtimesum");
		sprintf(firstCheckfile, "%s%s", firstCheck, sOriFileName.c_str());

		if (0 == CheckExist(firstCheckfile))
		{
			// 已经存在
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "BakPathFile [%s] is Already Exist", firstCheckfile);
		}
	}

	return 0;
}

int DCTaskSum::MulitBackFile(const char* vi_sBackPath, const char* vi_sfirstCheckfile, multimap<time_t,FileInfo>::iterator vi_pIterator)
{
	char sbakFileName[256] = {0};

	// 获取当前时间
	char v_date[32] = {0};

	string sOriFileName;
	size_t npos;
	npos = vi_pIterator->second.fileName.find_last_of("/");
	sOriFileName = vi_pIterator->second.fileName.substr(npos + 1,128);
	
	time_t ltime;
	struct tm *today;
	
	/* Set time zone from TZ environment variable. If TZ is not set,
	 * the operating system is queried to obtain the default value 
	 * for the variable.	  */
	tzset();
	/* Display operating system-style date and time. */
	
	time( &ltime );
	/* Convert to time structure. */
	
	today = localtime( &ltime );
	
	/* output the year, month and day ,hour,minute and second in format:"yyyymmddhhmmss" */
	sprintf(v_date,"%d%.2d%.2d%.2d%.2d%.2d",today->tm_year+1900,today->tm_mon+1,
		today->tm_mday,today->tm_hour,today->tm_min,today->tm_sec);
	
	v_date[14]='\0';
	
	// 备份为子文件
	int iSeq = 0;
	while(true)
	{
		iSeq++;
		memset(sbakFileName, 0x00, sizeof(sbakFileName));
		sprintf(sbakFileName, "%sMULTI_%s_%s_%d", vi_sBackPath, sOriFileName.c_str(), v_date, iSeq);
		if (0 != CheckExist(sbakFileName))
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "BakPathFileName [%s]", sbakFileName);
			break;
		}
	}
	
	int status = MoveFile(vi_sfirstCheckfile, sbakFileName);
	if (0 > status )
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "MoveFile[%s] failed, return:%d, error message:%s", vi_sfirstCheckfile, status, strerror(errno));
	}

	return 0;
}



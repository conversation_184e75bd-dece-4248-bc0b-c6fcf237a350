
#ifndef _DC_PERF_LOG_STATISTIC_H_H_
#define _DC_PERF_LOG_STATISTIC_H_H_

#include <errno.h>
#include <set>
#include <map>
#include <vector>
#include <string>
#include <list>
#include <stdio.h>
#include <stdlib.h>
#include <signal.h> // signal functions
#include <iostream>
#include <fstream>
#include <string.h>
#include <getopt.h>
#include <pthread.h>
#include <sys/time.h>
#include <sys/types.h>
#include <unistd.h>
#include <netdb.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <algorithm>
#include <sys/stat.h> 
#include <sys/ioctl.h>
#include <net/if.h>

#include "DCDBManer.h"
#include "DCPerfStatistic.h"
#include "DCPluginManer.h"

#define OUTPUT_PERF_LOG_INTERVAL 1

#define START_PERF_LOG(x,y) DCPerfTimeVCollect collet(DCPerfLogStatistic::instance()->GetPerfTimeStats(x,y), true);
#define STOP_PERF_LOG() collet.stop();


using namespace std;

class DCPerfLogStatistic
{
public:
	static DCPerfLogStatistic* instance()
	{ 
		if (!sm_pPerfLog)
			sm_pPerfLog = new DCPerfLogStatistic();
		return sm_pPerfLog;
	}
	static void destroy()
	{
		delete sm_pPerfLog;
		sm_pPerfLog = NULL;
	}


public:
	~DCPerfLogStatistic();

	DCPerfTimeStats* GetPerfTimeStats(const char* szModule, const char* szItem, DCDBManer* pdb = NULL);

	void output();
	void output_timeing();

	void reset();


private:
	static DCPerfLogStatistic* sm_pPerfLog;

	DCPluginManer     m_pmStatistic;
	set<string>       m_vecModuleList;
	set<DCDBManer*>   m_vecDBUsing;
	time_t            m_tOutput;


private:
	DCPerfLogStatistic();
};



#endif
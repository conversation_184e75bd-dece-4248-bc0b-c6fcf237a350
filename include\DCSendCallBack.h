#ifndef __DCSEND_CALLBACK_H__
#define __DCSEND_CALLBACK_H__
#include "CallbackAsyncHandlerNew.h"
#include "DCPairList.h"

/*struct DBInDex
{
	DCDBManer* dmdb;
	int threadid;
	DBInDex()
	{
		dmdb = NULL;
		threadid = 0;
	};
};*/


extern pthread_mutex_t 		mutex;
class DCSendCallBack:public dcf_new::CallbackAsyncHandler
{
public:
	DCSendCallBack(DCPairList *pairList);

	~DCSendCallBack();

public:
	void callback(const std::string& uuid, const std::string& serviceName, const char *pParams, int len);

	//void setList(vector<DBInDex> db);
	void outInfoFile();
	void dealErrorCode(string ipErrorCode);

private:
	DCPairList *m_pairList;
};

#endif

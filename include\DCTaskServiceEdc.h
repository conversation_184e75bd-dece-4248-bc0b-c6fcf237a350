﻿#ifndef __DCTASKSERVICE_EDC_H__
#define __DCTASKSERVICE_EDC_H__

#include "DCTQueue.h"
#include "DCDBManer.h"
#include "DCFile.h"


#include "DCFLocalClientNew.h"
#include "DCPerfStatistic.h"
#include "DCLogMacro.h"
#include "DCExchDB.h"
#include "DCDataDef.h"
#include "DCExchCfg.h"
#include <ace/Task.h>
#include <ace/Thread_Mutex.h>
#include <vector>
#include <stdio.h>
#include <list>
#include <map>

using namespace dcf_new;

class DCTaskServiceEdc : public ACE_Task_Base
{
public:
	DCTaskServiceEdc();
	~DCTaskServiceEdc();

	int init(DCDBManer* dmdb, dcf_new::DCFLocalClient* client, int threadNo);

	int run();

	virtual int svc();

	static void state(int done);
	static void stateQueue(int done);
	int CopyFile(const char* pSrcPath, const char* pDesPath);
	bool exit();

private:
	static int GetTask(DCDBManer *dmdb,STTask& task);

	int DoTask(const STTask& task);

	int ClearTask(DCDBManer *dmdb);

	int GetServiceName(const char * fileType,std::vector<string> &v);

private:
	static	int				sm_nDone;
	static	int				sm_nQueue;
	static  time_t          sm_checktime;
	bool					m_bexit;
	std::string             m_szLocalPath;
	static DCTQueue<STTask>	sm_qTask;
	static map<string,int>  sm_service;
	DCDBManer*              m_dmdb;
	DCExchDB*               m_exchdb;
	DCFile*                 m_file;
	DCFLocalClient*         m_client;
	time_t                  m_checktime;
	SExchCfg*               m_cfg;
	DCPerfTimeStats  			m_tstat;         //性能统计
	int 					m_nThreadNO;
};

#endif // __DCTASKSERVICE_EDC_H__

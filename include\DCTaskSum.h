#ifndef __DCTASKSUM_H__
#define __DCTASKSUM_H__

#include "DCTQueue.h"
#include "DCDBManer.h"
#include "DCLogMacro.h"
#include "DCPairList.h"
#include "DCDataDef.h"
#include "DCExchCfg.h"
#include "DCPerfStatistic.h"
#include <ace/Task.h>
#include <ace/Thread_Mutex.h>
#include <vector>
#include <stdio.h>
#include <list>
#include <map>
#include <regex.h>
#include "DCExchGlobal.h"
using namespace std;
struct TAccRecord
{
	long nServId;                       //用户标识
	long nAcctID;                       //--新增
	long nAcctItemTypeId;               //帐目类型
	long nOfferId;                      //商品标识
	long nDuration;                     //通话时长
	long nBillingDuration;              //计费时长
	long nCounts;                       //单元次数
	long nCalls;                        //话单张数
	long nFlux;                         //话单流数
	int day;
	int nBelongDay;
	int nBillingMode;
	int nBelongBillingCycleId;
	string cFeeType;                      //--新增，计费填1；一次费填4
	string cPayFlag;                      //是否扣费：-未扣费，-已扣费
	long nDisounctCharge;               //优惠费用
	long nOriCharge;                    //原始费用
	long nModGroupId;
	int if_realtime_disct;
	long sourceInstId;
	string custId;
	string uid;
    string cServiceNbr;                // 用户号码
    long nStrategyId;                  // 策略Id
	int nPlatform;                     // 平台标识
	int nOnLineMode5G;                 // 在线离线
	double HprecisionCharge;          //云网融合高精度费用
    int  IfCloud;                     //云网融合标识

	TAccRecord()
	{
		nServId = 0;
		nAcctID = 0;
		nAcctItemTypeId = 0;
		nOfferId = 0;
		nDuration = 0;
		nBillingDuration = 0;
		nCounts = 0;
		nCalls = 0;
		nFlux = 0;
		day = 0;
		nBelongDay = 0;
		nBillingMode = 0;
		nBelongBillingCycleId = 0;
		cFeeType = "";
		cPayFlag = "";
		nDisounctCharge = 0;
		nOriCharge = 0;
		nModGroupId = 0;
		if_realtime_disct = 0;
		sourceInstId = 0;
		custId = "";
		uid = "";
		cServiceNbr = "";
		nStrategyId = 0;
		nPlatform = 0;
		nOnLineMode5G = 2;
		HprecisionCharge = 0.0;
		IfCloud = 0;
	}
};
struct TAccRecordCommon
{
	long nServId;                       //用户标识
	long nAcctID;                       //--新增
	long nAcctItemTypeId;               //帐目类型
	long nOfferId;                      //商品标识
	int day;
	int nBelongDay;
	int nBillingMode;
	int nBelongBillingCycleId;
	string cFeeType;                      //--新增，计费填1；一次费填4
	string cPayFlag;                      //是否扣费：-未扣费，-已扣费
	long nModGroupId;
	int if_realtime_disct;
	long sourceInstId;
    string cServiceNbr;
    int nPlatform;                      // 平台标识
    int IfCloud;

	TAccRecordCommon()
	{
		cFeeType = "";
		cPayFlag = "";
        cServiceNbr = "";
		IfCloud = 0;
	}

	TAccRecordCommon operator <<(const TAccRecord in)
	{
		this->nServId = in.nServId;
		this->nAcctID = in.nAcctID;
		this->nAcctItemTypeId = in.nAcctItemTypeId;
		this->nOfferId = in.nOfferId;
		this->day = in.day;
		this->nBelongDay = in.nBelongDay;
		this->nBillingMode = in.nBillingMode;
		this->nBelongBillingCycleId = in.nBelongBillingCycleId;
		this->cFeeType = in.cFeeType;
		this->cPayFlag = in.cPayFlag;
		this->nModGroupId = in.nModGroupId;
		this->if_realtime_disct = in.if_realtime_disct;
		this->sourceInstId = in.sourceInstId;
        this->cServiceNbr = in.cServiceNbr;
        this->nPlatform = in.nPlatform;
		this->IfCloud = in.IfCloud;
		return *this;
	};
	bool operator<(const TAccRecordCommon in) const
	{
		if(this->nModGroupId < in.nModGroupId)
			return true;
		else if(this->nModGroupId > in.nModGroupId)
			return false;
		if(this->nModGroupId == 0)
		{
			if(this->nAcctID < in.nAcctID)
				return true;
			else if (this->nAcctID > in.nAcctID)
				return false;
			if(this->if_realtime_disct < in.if_realtime_disct)
				return true;
			else if(this->if_realtime_disct > in.if_realtime_disct)
				return false;
		}
		else
		{
			if(this->if_realtime_disct < in.if_realtime_disct)
				return true;
			else if(this->if_realtime_disct > in.if_realtime_disct)
				return false;
			if(this->nAcctID < in.nAcctID)
				return true;
			else if (this->nAcctID > in.nAcctID)
				return false;
		}
        if (this->cServiceNbr < in.cServiceNbr)
            return true;
        else if (this->cServiceNbr > in.cServiceNbr)
            return false;
		if(this->nServId < in.nServId)
			return true;
		else if (this->nServId > in.nServId)
			return false;
		if(this->nAcctItemTypeId < in.nAcctItemTypeId)
			return true;
		else if (this->nAcctItemTypeId > in.nAcctItemTypeId)
			return false;
		if(this->nOfferId < in.nOfferId)
			return true;
		else if (this->nOfferId > in.nOfferId)
			return false;
		if(this->day < in.day)
			return true;
		else if (this->day > in.day)
			return false;
		if(this->nBelongDay < in.nBelongDay)
			return true;
		else if (this->nBelongDay > in.nBelongDay)
			return false;
		if(this->nBillingMode < in.nBillingMode)
			return true;
		else if (this->nBillingMode > in.nBillingMode)
			return false;
		if(this->nBelongBillingCycleId < in.nBelongBillingCycleId)
			return true;
		else if (this->nBelongBillingCycleId > in.nBelongBillingCycleId)
			return false;
		if(this->cFeeType < in.cFeeType)
			return true;
		else if (this->cFeeType > in.cFeeType)
			return false;
		if(this->cPayFlag < in.cPayFlag)
			return true;
		else if(this->cPayFlag > in.cPayFlag)
			return false;
		if(this->IfCloud < in.IfCloud)
			return true;
		else if(this->IfCloud > in.IfCloud)
			return false;
		if(this->sourceInstId < in.sourceInstId)
			return true;
		else 
			return false;
	};
	bool operator==(const TAccRecordCommon in) const
	{
		return (this->nServId == in.nServId &&
		this->nAcctID == in.nAcctID &&
		this->nAcctItemTypeId == in.nAcctItemTypeId &&
		this->nOfferId == in.nOfferId &&
		this->day == in.day &&
		this->nBelongDay == in.nBelongDay &&
		this->nBillingMode == in.nBillingMode &&
		this->nBelongBillingCycleId == in.nBelongBillingCycleId &&
		this->cFeeType == in.cFeeType &&
		this->cPayFlag == in.cPayFlag &&
		this->nModGroupId == in.nModGroupId &&
		this->if_realtime_disct == in.if_realtime_disct &&
		this->sourceInstId == in.sourceInstId &&
        this->cServiceNbr == in.cServiceNbr &&
		this->IfCloud == in.IfCloud);
	};
};
struct FileInfo
{
	string fileName;
	int latnId;
	int billingCycleId;
	string batchId;
	int mod;
	string fileType;
	int isRealTime;
	int operlistId;
};
struct SumTask
{
	long chargeFilesId;
	string batchId;
	string filePath;
	string fileName;
	int latnId;
	int billingCycleId;
};
struct TAccRecordCharge
{
	long nDuration;                     //通话时长
	long nBillingDuration;              //计费时长
	long nCounts;                       //单元次数
	long nCalls;                        //话单张数
	long nFlux;                         //话单流数
	long nDisounctCharge;               //优惠费用
	long nOriCharge;                    //原始费用
	string custId;
	string uid;
    long nStrategyId;
	int nOnLineMode5G;                  // 在线离线
	double HprecisionCharge;            //高精度费用

	TAccRecordCharge()
	{
		nDuration = 0;                     //通话时长
		nBillingDuration = 0;              //计费时长
		nCounts = 0;                       //单元次数
		nCalls = 0;                        //话单张数
		nFlux = 0;                         //话单流数
		nDisounctCharge = 0;               //优惠费用
		nOriCharge = 0;                    //原始费用
		custId = "";
		uid = "";
        nStrategyId = 0;
		nOnLineMode5G = 0;
		HprecisionCharge = 0.0;            //高精度费用
	};
	TAccRecordCharge operator <<(const TAccRecord in)
	{
		this->nDuration = in.nDuration;
		this->nBillingDuration = in.nBillingDuration;
		this->nCounts = in.nCounts;
		this->nCalls = in.nCalls;
		this->nFlux = in.nFlux;
		this->nOriCharge = in.nOriCharge;
		this->nDisounctCharge = in.nDisounctCharge;
		this->custId = in.custId;
		this->uid = in.uid;
        this->nStrategyId = in.nStrategyId;
		this->nOnLineMode5G = in.nOnLineMode5G;
		this->HprecisionCharge = in.HprecisionCharge;
		return *this;
	};
	TAccRecordCharge operator +=(const TAccRecordCharge b)
	{
		this->nDuration += b.nDuration;
		this->nBillingDuration += b.nBillingDuration;
		this->nCounts += b.nCounts;
		this->nCalls += b.nCalls;
		this->nFlux += b.nFlux;
		this->nDisounctCharge += b.nDisounctCharge;
		this->nOriCharge += b.nOriCharge;
		this->custId = b.custId;
		this->uid = b.uid;
        this->nStrategyId = b.nStrategyId;
		this->nOnLineMode5G = b.nOnLineMode5G;
		this->HprecisionCharge += b.HprecisionCharge;
		return *this;
	};
};
struct SumDetailsFile
{
	std::vector<FileInfo> vSumFileInfo;
	map<TAccRecordCommon,TAccRecordCharge> sumMap;
	std::vector<TAccRecordCharge> vChargeAll;
	std::vector<int> vOriRecord;
	TAccRecordCharge chargeAll;
	~SumDetailsFile()
	{
		vSumFileInfo.clear();
		sumMap.clear();
		vChargeAll.clear();
		vOriRecord.clear();
	};
};
class DCTaskSum : public ACE_Task_Base
{
public:
	DCTaskSum();
	~DCTaskSum();

	int init(DCDBManer* dmdb,int threadNo,int latnId);
	int GetFiles(multimap<time_t,FileInfo> &fileNameMulti);
	int run();
	int exit();
	static void state(int done);
	string getBillingCycle(int n);
	virtual int svc();
	int ResolveFile(string sfilename,FileInfo &fileInfo);
	void GetBuf(TAccRecord & recordBuf,string & line);
	void SetBuf(char* line,const TAccRecordCommon &common,TAccRecordCharge &charge);
	int ArrangeFile(multimap<time_t,FileInfo> &fileNameMulti);
	int InsertChargeFilesDetails(string filename,TAccRecordCharge &charge,FileInfo fileInfo,int oriRecord,int record,string dirname,string detailName,int isDetails = 0);
	int InsertChargeFiles(TAccRecordCharge &charge,FileInfo fileInfo,int oriRecord,string scandir);
	int RefreshOprList();
	string CheckAndCreateDir(string rootDir,int billingCycleId,string fileType);
	int CheckDupFile(const SExchCfg* vi_pExchCfg, multimap<time_t,FileInfo>::iterator vi_pIterator);
	int MulitBackFile(const char* vi_sBackPath, const char* vi_sfirstCheckfile, multimap<time_t,FileInfo>::iterator vi_pIterator);

private:
	static int                                  m_sdone;
	std::map<TAccRecordCommon,TAccRecordCharge> m_sumMap;
	int                                         maxThread;
	int                                         threadNo;
	long                                        m_iChargeFilesId;
	char                                        m_sBatchID[128];
	SExchCfg*                                   m_cfg;
	DCDBManer*                                  m_dbm;
	int                                         m_mod;
	int                                         m_latnId;
	std::map<int,string>                        m_oprlist;
	time_t                                      m_check;
	time_t                                      m_checktime;
	int                                         m_exit;
	string                    					m_strHostIp;
};

#endif // __DCTaskSum_H__

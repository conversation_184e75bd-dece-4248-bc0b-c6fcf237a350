﻿#include <signal.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <netdb.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include "DCFClientLogNew.h"
#include "DCTaskService.h"
#include "DCLogMacro.h"
#include "DCParseXml.h"
#include "DCMon.h"
#include "DCMCastEvtFun.h"
#include "DCPairList.h"
#include "DCPairManager.h"
#include "CallbackAsyncHandlerNew.h"
#include "DCExchCfg.h"
#include "DCSendCallBack.h"
#include "DCDBHander.h"
#include "DCTaskServiceSum.h"
#include "DCTaskServiceEdc.h"
#include "DCTaskSum.h"

#define RETURN(x) { sleep(1); return x; }//make sure that the log message had been writen.

//rcsid版本签名，可在linux通过ident命令查看
static const char rcsid[] = "$ID: dcup | Area: " AREA " | SvnVersion: " SVN_VERSION " | ReleaseVersion: " RELEASE_VERSION " | build_time : " __DATE__  " " __TIME__ " | $";

using namespace std;
using namespace dcf_new;

int runmode;//0:从分布式文件系统获取消息；1:从MQ获取消息
DCFLocalClient* dcfLocalclient = NULL;
DCFLocalClient* clientBill = NULL;

void sig_deal_func(int sig)
{
	switch(sig)
	{
		case SIGTERM:
		case SIGINT:
		case SIGQUIT:
		{
			 DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","get quit sig");
			// stop task
			if(0==runmode)
			{
				DCTaskService::state(2);
			}
			else if(2==runmode)
			{
				DCTaskServiceSum::state(2);
				DCTaskSum::state(2);
			}
			else if (10==runmode)
			{
				DCTaskService::state(2);
			}
	        break;
		}
	}
}

void ParseString(string& szSourString,vector<std::string>&vecDest ,const char* szSeparator)
{
	string::size_type pos = 0, prev_pos = 0;
	int nCounter = 1;
	string strTemp ="";

	vecDest.clear();

	while (( pos = szSourString.find(szSeparator , pos )) != string::npos)
	{
		strTemp = szSourString.substr( prev_pos, pos - prev_pos );

		vecDest.push_back(strTemp);
		prev_pos = ++pos;
		++nCounter;
	}

	strTemp = szSourString.substr( prev_pos, pos - prev_pos ) ;
	vecDest.push_back(strTemp);

	return ;
}

//取队列的积压值

int	setQueueFlag()
{
	std::map<std::string, std::string> filequeueMap = dcfLocalclient->getFileQueueTable();
	if(filequeueMap.size()==0)
	{
		return 0;
	}

	int blocksize = 0;
	vector<std::string> vResult;
	std::map<std::string, std::string>::iterator iter=filequeueMap.begin();
	string value = iter->second;
	ParseString(value,vResult,",");
	if(vResult.size()==3)
	{
		blocksize = atoi(vResult[2].c_str());
		DCBIZLOG(DCLOG_LEVEL_WARN,0,"","blocking worker:%s,task:%s,size:%d", iter->first.c_str(),vResult[1].c_str(),blocksize);
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_WARN,0,"","filequeue record fmt error");
		return 0;
	}

	SExchCfg* pcfg = DCExchCfg::instance()->GetConfig();

	if(blocksize >= pcfg->BlockQueueSize)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","system filequeue overload[%d]",blocksize);
		DCTaskService::queueState(1);
	}
	return 0;
}


int main(int argc, char*argv[])
{
	//version
	if ( argc == 2 && (!strcmp(argv[1],"-V") || !strcmp(argv[1],"-v")))
	{
		printf("%s \n", rcsid);
		return 0;
	}

	if(argc < 3)
	{
		fprintf(stderr, "fastdfs   Usage: %s msgexch_cfg.xml 0\n", argv[0]);
		fprintf(stderr, "sum       Usage: %s msgexch_cfg.xml 2 latn_id\n", argv[0]);
		fprintf(stderr, "edc       Usage: %s msgexch_cfg.xml 10\n", argv[0]);
		return 1;
	}

	sigset(SIGPIPE, SIG_IGN);

	runmode = atoi(argv[2]);

	if(0==runmode || 10==runmode)
	{
		fprintf(stdout, "runmode is %d\n",runmode);
	}
	else if (2 == runmode && 4 >= argc)
	{
		fprintf(stdout, "runmode is %d\n",runmode);
	}
	else
	{
		fprintf(stderr, "runmode is error!\n");
		fprintf(stderr, "fastdfs   Usage: %s cfg_xml_filepath 0\n", argv[0]);
		fprintf(stderr, "sum       Usage: %s cfg_xml_filepath 2 latn_id\n", argv[0]);
		fprintf(stderr, "detect    Usage: %s cfg_xml_filepath 10\n", argv[0]);
		return -1;
	}

	//配置初始化
	int ret = DCExchCfg::instance()->Init(argv[1]);
	if(ret)
	{
		fprintf(stderr, "init cfg failed\n");
		return -1;
	}
	ret = DCExchCfg::instance()->run();
	if(ret < 0)
	{
		fprintf(stderr, "run cfg failed\n");
		return -1;
	}
	SExchCfg *pcfg = DCExchCfg::instance()->GetConfig();
	pcfg->nRunmode = runmode;

	//日志初始化	
	//ret = DCLOGINIT("CLOUD","msgexch",pcfg->loglevel,pcfg->logserv.c_str());
	ret = DCLOGINITNEW("CLOUD","msgexch","","11",pcfg->staffId.c_str(),pcfg->loglevel,pcfg->logserv.c_str());
	if(ret)
	{
		fprintf(stderr, "init log failed\n");
		return -1;
	}
	DCLOG_SETABID("110001");
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","log path:%s, level:%d",pcfg->logserv.c_str(), pcfg->loglevel);


	DCLOG_SETLEVEL(DCLOG_CLASS_PERF,pcfg->perf_level);
	DCLOG_SETCTL(DCLOG_MASK_PERF,pcfg->perf_threshold*1000);//单位转化为微秒
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","init  perf success");

	
	//注册监听事件
	DCMCastManer* m_mcm = new DCMCastManer();
	const char* mcast = getenv("OCS_MCAST_CMD_ADDR");
	if(mcast)
	{
		ret = m_mcm->init(mcast);
		if(ret < 0)
		{
		    DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","init  DCMCastManer failed: %s", strerror(errno));
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","init  DCMCastManer success");

		LogEventFun evfun(m_mcm);
        evfun.register_event("msgexch");
	}

	char *szconfig =getenv("BILLING_CONFIG");
	if(NULL==szconfig)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","env BILLING_CONFIG not config");
		RETURN(-1);
	}

	string sqlPath;
	sqlPath = szconfig;
	sqlPath = sqlPath + '/' + pcfg->sqlConf;

	//锟斤拷锟今化碉拷锟斤拷
	DCFClientLog::init("./DCFClient");
	DCFClientLog::setLogLevel(pcfg->servLogLevel);
/*
	dcfLocalclient = new dcf_new::DCFLocalClient();
	dcfLocalclient->setAESPassword(pcfg->aespwd);
	dcfLocalclient->setZookAddrAndRootDir(pcfg->zkAddr,pcfg->zkRoot);
	dcfLocalclient->setZookAddrAndRootDir(pcfg->zkAddr,pcfg->zkRootQueue,true);

	dcfLocalclient->setZookAddrAndRootDir(pcfg->zkAddrBILL,pcfg->zkRootBILL);
	dcfLocalclient->setZookAddrAndRootDir(pcfg->zkAddrBILL,pcfg->zkRootQueueBILL,true);
*/

	if (0 == runmode || 10 == runmode)
	{
		dcfLocalclient = new dcf_new::DCFLocalClient();
		dcfLocalclient->setAESPassword(pcfg->aespwd);
		dcfLocalclient->setZookAddrAndRootDir(pcfg->zkAddr, pcfg->zkRoot);
		dcfLocalclient->setZookAddrAndRootDir(pcfg->zkAddr, pcfg->zkRootQueue, true);

		clientBill = new dcf_new::DCFLocalClient();		
		clientBill->setAESPassword(pcfg->aespwd);
		clientBill->setZookAddrAndRootDir(pcfg->zkAddrBILL,pcfg->zkRootBILL);
		clientBill->setZookAddrAndRootDir(pcfg->zkAddrBILL,pcfg->zkRootQueueBILL,true);
	}
	else if (2 == runmode)
	{
		clientBill = new dcf_new::DCFLocalClient();
		clientBill->setAESPassword(pcfg->aespwd);
		clientBill->setZookAddrAndRootDir(pcfg->zkAddrBILL, pcfg->zkRootBILL);
		clientBill->setZookAddrAndRootDir(pcfg->zkAddrBILL, pcfg->zkRootQueueBILL, true);
	}

	DCDBManer *dbManerGray = new DCDBManer();
	ret = dbManerGray->Init(sqlPath.c_str());
	if (ret < 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "init  sql failed");
		RETURN(-1);
	}

	DCDBManer *dmdbtimeout = new DCDBManer();
	if(!dmdbtimeout)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","create oracle instance  failed");
		RETURN(-1);
	}
	ret = dmdbtimeout->Init(sqlPath.c_str());
	if(ret < 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "init  sql failed");
		RETURN(-1);
	}

	//初始化指标采集接口
	DCMon *screen[4];
	DCMon *m_gth;
	DCMon *m_cdr;
	DCMon *m_statFiles;
	if(0==pcfg->addr.length())
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "init MsgExch/screen faild!");
		fprintf(stderr, "init MsgExch/screen faild!\n");
		RETURN(-1);
	}

	const char* host =  DCParseXml::Instance()->GetParam("host","MsgExch/gth");
	if( !host)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "init MsgExch/gth faild!");
		fprintf(stderr, "init MsgExch/gth faild!\n");
		RETURN(-1);
	}

	m_gth = new DCMon();
	ret = m_gth->init(pcfg->delay,pcfg->portocol,pcfg->addr.c_str());
	if(ret < 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","DCMon init failed{addr:%s}.",pcfg->addr.c_str());
		RETURN(-1);
	}
	m_gth->head("BILLING","GTH","MSGEXCH");
	m_cdr = new DCMon();
	m_cdr->init(pcfg->delay,pcfg->portocol,pcfg->addr.c_str());
	m_cdr->head("BILLING", "CDR", "SCREEN");
	m_statFiles = new DCMon();
	m_statFiles->init(pcfg->delay,pcfg->portocol,pcfg->addr.c_str());
	m_statFiles->head("BILLING", "FILE", "SCREEN");
	screen[0] = NULL;
	screen[1] = m_gth;
	screen[2] = m_cdr;
	screen[3] = m_statFiles;

	DCPairList *pairList;
	if(runmode == 2)
	{
		pairList = new DCPairList(dmdbtimeout,clientBill,1);
	}
	else
	{
		pairList = new DCPairList(dmdbtimeout,dcfLocalclient);
	}

	// 设置pairList指针灰度刷新用
	DCExchCfg::SetPairList(pairList);

	DCSendCallBack *pSendCallback = new DCSendCallBack(pairList);
	
	/*
	dcfLocalclient->addCallbackAsyncHandler(pSendCallback);
	dcfLocalclient->setClientCallbackThreadsMax(pcfg->threadnum);
	*/

	if (runmode == 2)
	{
		clientBill->addCallbackAsyncHandler(pSendCallback);
		clientBill->setClientCallbackThreadsMax(pcfg->threadnum);
	}
	else
	{
		dcfLocalclient->addCallbackAsyncHandler(pSendCallback);
		dcfLocalclient->setClientCallbackThreadsMax(pcfg->threadnum);

		clientBill->addCallbackAsyncHandler(pSendCallback);
		clientBill->setClientCallbackThreadsMax(pcfg->threadnum);
	}

	// 设置dfs客户端初始化等待时间
	if (0 == runmode)
	{
		dcfLocalclient->setInitWait(pcfg->bDCFInitWait);
		dcfLocalclient->setInitWaitTimeoutMills(pcfg->lnDCFInitWaitTimeout);
		dcfLocalclient->setSerivceWorkNum(pcfg->serviceCTPC, pcfg->nDCFWorkNumCTPC);
		// dcfLocalclient->setSerivceWorkNum(pcfg->serviceBILL, pcfg->nDCFWorkNumBILL);

		clientBill->setInitWait(pcfg->bDCFInitWait);
		clientBill->setInitWaitTimeoutMills(pcfg->lnDCFInitWaitTimeout);
		clientBill->setSerivceWorkNum(pcfg->serviceBILL, pcfg->nDCFWorkNumBILL);
	}
	else if (2 == runmode)
	{
		clientBill->setInitWait(pcfg->bDCFInitWait);
		clientBill->setInitWaitTimeoutMills(pcfg->lnDCFInitWaitTimeout);
		clientBill->setSerivceWorkNum(pcfg->serviceBILL, pcfg->nDCFWorkNumBILL);
	}

	//启动消息超时处理线程 m_pairList nTimeOut
	DCPairManager	*pairManager = new DCPairManager();
	pairManager->init(pairList,pcfg->FlowTimeOut,NULL);
	pairManager->start();
/*
	if(dcfLocalclient->start())
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","start client failed{zook:%s}.",pcfg->zkAddr.c_str());
		RETURN(-1);
	}
*/

	if (0 == runmode || 10 == runmode)
	{
		if(dcfLocalclient->start())
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","start clientFmt failed{zook:%s}.",pcfg->zkAddr.c_str());
			RETURN(-1);
		}

		if(clientBill->start())
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","start clientBill failed{zook:%s}.",pcfg->zkAddrBILL.c_str());
			RETURN(-1);
		}
	}
	else if (2 == runmode)
	{
		if(clientBill->start())
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","start clientBill failed{zook:%s}.",pcfg->zkAddrBILL.c_str());
			RETURN(-1);
		}
	}


	if (0 == runmode || 2 == runmode)
	{
		// 采预图服务订阅，仅runmode=0调用
		if (0 == runmode && 0 > dcfLocalclient->subscribeService(pcfg->serviceCTPC))
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Init service call fail! servers_name=%s", pcfg->serviceCTPC.c_str());
			RETURN(-1);
		}
		// 采预、账务部分订阅批价服务
		/*if (0 == runmode && 0 > dcfLocalclient->subscribeService(pcfg->serviceBILL))
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Init service call fail! servers_name=%s", pcfg->serviceBILL.c_str());
			RETURN(-1);
		}
		else */if (2 == runmode && 0 > clientBill->subscribeService(pcfg->serviceBILL))
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Init service call fail! servers_name=%s", pcfg->serviceBILL.c_str());
			RETURN(-1);
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Init service call success!");
	}

	DCExchCfg::instance()->InitGrayscaleRoute(dbManerGray, pcfg->Subscriber);
 
	int worker_num = pcfg->worker_num;
	if(0 == runmode || 10 == runmode)
	{
		worker_num ++;
	}
	DCTaskService       vTask[worker_num];
	DCTaskServiceSum    vSum[worker_num];
	DCTaskServiceEdc    vEdc[worker_num];
	int threadNum = pcfg->arrangeThreadnum;
	DCTaskSum vTaskSum[threadNum];
    for(int i = 0; i < worker_num; i++)
	{
		//锟斤拷锟捷匡拷某锟绞硷拷锟?		
		DCDBManer *dmdb = new DCDBManer();
		if(!dmdb)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","create DCDBManer instance  failed");
			RETURN(-1);
		}
		ret = dmdb->Init(sqlPath.c_str());
		if(ret < 0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","init  sql failed");
			RETURN(-1);
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Init dmdb success!");

		if(0==runmode)
		{
            if (i == 0)
            {
                ret = DCExchCfg::instance()->InitSplitInfo(dmdb);
                if (ret < 0)
                {
                    DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "init splitinfo failed");
                    RETURN(-1);
                }
            }
            
			ret = vTask[i].init(dmdb,dcfLocalclient,clientBill,pairList,screen,i);
			if(ret < 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "init DCTaskService failed, worker[%d]", i);
				RETURN(1);
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","init DCTaskService success, worker[%d]",i);
		}
		else if(2==runmode)
		{
			int latnId = atoi(argv[3]);
			ret = vSum[i].init(dmdb,clientBill,pairList,latnId, i);

			if(ret < 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","init DCTaskServiceSum failed, worker[%d]", i);
				RETURN(1);
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","init DCTaskServiceSum success, worker[%d]",i);

		}
		else if (10 == runmode)
		{
			ret = vEdc[i].init(dmdb,dcfLocalclient,i);
			if(ret < 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","init DCTaskService failed, worker[%d]", i);
				RETURN(1);
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","init DCTaskService success, worker[%d]",i);
		}
	}
	//锟斤拷锟捷匡拷某锟绞硷拷锟?	
	DCDBManer *dmdb = new DCDBManer();
	if(!dmdb)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "create oracle instance failed");
		RETURN(-1);
	}
	ret = dmdb->Init(sqlPath.c_str());
	if(ret < 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","init  sql failed");
		RETURN(-1);
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Init dmdb success!");
	if(2 == runmode)
	{
		int latnId = atoi(argv[3]);
		for(int i = 0; i < threadNum; i++)
		{
			ret = vTaskSum[i].init(dmdb,i,latnId);
			if(ret < 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","init DCTaskSum failed, worker[%d]", i);
				RETURN(-1);
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","init DCTaskSum success, worker[%d]",i);
		}
	}

    DCDBHander vDBhand[pcfg->nTreadNum];
	for(int i = 0; i < pcfg->nTreadNum; i++)
	{
		//锟斤拷锟捷匡拷某锟绞硷拷锟?		
		DCDBManer *dmdb = new DCDBManer();
		if(!dmdb)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "create oracle instance  failed");
			RETURN(-1);
		}
		ret = dmdb->Init(sqlPath.c_str());
		if(ret < 0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "init  sql failed");
			RETURN(-1);
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Init dmdb success!");
		if(runmode == 2)
		{
			ret = vDBhand[i].init(dmdb,pairList,pcfg->deleteflag, pcfg->iDelCheckUidFlag, atoi(argv[3]), pcfg->FlowTimeOut);
		}
		else
		{
			ret = vDBhand[i].init(dmdb,pairList,pcfg->deleteflag, pcfg->iDelCheckUidFlag);
		}
		if(ret < 0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","init DCDBHandler failed, worker[%d]", i);
			RETURN(1);
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","init DCDBHandler success, worker[%d]",i);
	}

	for(int i = 0; i < pcfg->nTreadNum; i++)
	{
		ret = vDBhand[i].run();
		if(ret < 0)
		{

			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","start DCDBHandler failed, worker[%d]", i);
			RETURN(1);
		}

		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","start DCDBHandler success, worker[%d]",i);
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","start to deal task");
	for(int i = 0; i < worker_num; i++)
	{
		if(0==runmode)
		{
			ret = vTask[i].run();
			if(ret < 0)
			{

				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","start DCTaskService failed, worker[%d]", i);
				return 1;
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","start DCTaskService success, worker[%d]",i);
		}
		else if(2 == runmode)
		{
			ret = vSum[i].run();
			if(ret < 0)
			{

				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","start vSum failed, worker[%d]", i);
				return 1;
			}

		}
		else if (10 == runmode)
		{
			ret = vEdc[i].run();
			if(ret < 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","start DCTaskService failed, worker[%d]", i);
				return 1;
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","start DCTaskService success, worker[%d]",i);
		}
	}
	if(2 == runmode)
	{
		for(int i = 0; i < threadNum;i++)
		{
			ret = vTaskSum[i].run();
			if(ret < 0)
			{

				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","start vTaskSum failed, worker[%d]", i);
				return 1;
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","start vTaskSum success, worker[%d]",i);
		}
	}
	sleep(2);

	for(int i = 0; i < worker_num; i++)
	{
		if((0==runmode && vTask[i].exit()) || (2==runmode && vSum[i].exit()) || (10 == runmode && vEdc[i].exit()))
			return 1;
	}

	// start to deal task
	sigset(SIGTERM, sig_deal_func);
	sigset(SIGINT, sig_deal_func);
	sigset(SIGQUIT, sig_deal_func);

	sleep(1);

	if(0==runmode)
	{
		DCTaskService::state(1);
	    while(1)
		{
			setQueueFlag();
			int exit = 0;
			for(int i = 0; i < worker_num; i++)
			{
				if(vTask[i].exit())
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","worker[%d] is exit",i);
					exit++;
				}
			}
			if (exit == worker_num && 0 == vTask[0].getSize())
			{
				DCDBHander::state(1);
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","worker is exit");
				break;
			}
			if (exit == worker_num)
			{
				DCTaskService::state(2);
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","ready exit,pairlistSize[%d],",vTask[0].getSize());
			}
			sleep(1);
		}

		if(dcfLocalclient)
		{
			dcfLocalclient->shutdown();
			SAFE_DELETE_PTR(dcfLocalclient);
		}
		if (clientBill)
		{
			clientBill->shutdown();
			SAFE_DELETE_PTR(clientBill);
		}
	}
	else if(2 == runmode)
	{
		DCTaskServiceSum::state(1);
	    while(1)
		{
			//setQueueFlag();
			int exit = 0;
			int sumExit = 0;
			for(int i = 0; i < worker_num; i++)
			{
				if(vSum[i].exit())
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","worker[%d] is exit",i);
					exit++;
				}
			}
			for(int i = 0; i < threadNum; i++)
			{
				if(vTaskSum[i].exit())
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","TaskSum[%d] is exit,all[%d]",i,threadNum);
					sumExit++;
				}
			}
			if (exit == worker_num && 0 == vSum[0].getSize() && sumExit == threadNum)
			{
				DCDBHander::state(1);
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","worker is exit");
				break;
			}

			if (sumExit == threadNum)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","ready exit,pairlistSize[%d],",vSum[0].getSize());
			}

			sleep(1);
		}

		if(clientBill)
		{
			SAFE_DELETE_PTR(clientBill);
		}
	}
	else if (10 == runmode)
	{
		DCTaskServiceEdc::state(1);
		while(1)
		{
			setQueueFlag();
			int exit = 0;
			for(int i = 0; i < worker_num; i++)
			{
				if(vEdc[i].exit())
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "worker[%d] is exit", i);
					exit++;
				}
			}
			if (exit == worker_num)
			{
				DCDBHander::state(1);
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","worker is exit");
				break;
			}
			sleep(1);
		}
		if(dcfLocalclient)
		{
			SAFE_DELETE_PTR(dcfLocalclient);
		}
	}

	while(1)
	{
		int exit = 0;
		for(int i = 0; i < pcfg->nTreadNum; i++)
		{
			if(vDBhand[i].exit())
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","DBhandler[%d] is exit",i);
				exit++;
			}
		}

		if (exit == pcfg->nTreadNum)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","DBhandler is exit");
			break;
		}
		sleep(1);
	}

    while (!pairManager->done())
    {
        sleep(1);
    }

    SAFE_DELETE_PTR(pairManager);
    SAFE_DELETE_PTR(pairList);
	DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","msgexch is exit now!!!");
	if(dcfLocalclient)
	{
		dcfLocalclient->shutdown();
		SAFE_DELETE_PTR(dcfLocalclient);
	}
	if(clientBill)
	{
		clientBill->shutdown();
		SAFE_DELETE_PTR(clientBill);
	}

	return 0;
}

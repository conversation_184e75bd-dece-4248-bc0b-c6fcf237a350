#include <iostream>
#include <string>
#include <cctype>

// Base64解码表
static const std::string base64_chars = 
    "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    "abcdefghijklmnopqrstuvwxyz"
    "0123456789+/";

// Base64解码函数
std::string base64_decode(const std::string& encoded_string) {
    int in_len = encoded_string.size();
    int i = 0;
    int in = 0;
    unsigned char char_array_4[4], char_array_3[3];
    std::string ret;

    while (in_len-- && (encoded_string[in] != '=') && 
           (isalnum(encoded_string[in]) || (encoded_string[in] == '+') || (encoded_string[in] == '/'))) {
        char_array_4[i++] = encoded_string[in]; in++;
        if (i == 4) {
            for (i = 0; i < 4; i++)
                char_array_4[i] = base64_chars.find(char_array_4[i]);

            char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
            char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
            char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];

            for (i = 0; (i < 3); i++)
                ret += char_array_3[i];
            i = 0;
        }
    }

    if (i) {
        for (int j = 0; j < i; j++)
            char_array_4[j] = base64_chars.find(char_array_4[j]);

        char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
        char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);

        for (int j = 0; (j < i - 1); j++) ret += char_array_3[j];
    }

    return ret;
}

// URL解码函数
std::string url_decode(const std::string& str) {
    std::string result;
    for (size_t i = 0; i < str.length(); ++i) {
        if (str[i] == '%') {
            if (i + 2 < str.length()) {
                std::string hex = str.substr(i + 1, 2);
                char c = static_cast<char>(std::stoi(hex, nullptr, 16));
                result += c;
                i += 2;
            }
        } else if (str[i] == '+') {
            result += ' ';
        } else {
            result += str[i];
        }
    }
    return result;
}

// 解密函数
std::string decrypt_password(const std::string& str) {
    if (str.empty()) {
        std::cout << "decrypt_password: Input password is empty" << std::endl;
        return str;
    }

    int str_length = str.length();
    
    // 提取最后一位数字（分割位置）
    char last_char = str[str_length - 1];
    
    // 检查最后一位是否为数字，如果不是数字则认为是明文
    if (!isdigit(last_char)) {
        std::cout << "decrypt_password: Last char is not digit, treat as plaintext, password_length=" << str_length << std::endl;
        std::cout << "decrypt_password: Return plaintext password: " << str << std::endl;
        return str; // 返回原始明文
    }
    
    int r = last_char - '0'; // 转换为数字
    
    // 验证分割位置是否有效
    if (r < 1 || r > 9) {
        std::cout << "decrypt_password: Invalid split position " << r << ", treat as plaintext" << std::endl;
        std::cout << "decrypt_password: Return plaintext password: " << str << std::endl;
        return str; // 返回原始字符串，可能是明文
    }

    // 检查字符串长度是否足够进行解密操作
    if (str_length < r + 26 + 1) {
        std::cout << "decrypt_password: String length " << str_length << " insufficient for decryption (need at least " << (r + 26 + 1) << "), treat as plaintext" << std::endl;
        std::cout << "decrypt_password: Return plaintext password: " << str << std::endl;
        return str; // 返回原始字符串，可能是明文
    }

    std::cout << "decrypt_password: Begin decrypt, password_length=" << str_length << ", split_position=" << r << std::endl;

    // 提取第一部分（0到r位置）
    std::string first_str = str.substr(0, r);
    
    // 提取第二部分（跳过26个随机字符）
    int end_start = r + 26;
    std::string end_str = str.substr(end_start, str_length - 1 - end_start);

    // 重新组合Base64字符串
    std::string combined = first_str + end_str;
    
    std::cout << "decrypt_password: Combined base64 string length=" << combined.length() << std::endl;
    
    // Base64解码
    std::string decoded_str;
    try {
        decoded_str = base64_decode(combined);
        std::cout << "decrypt_password: Base64 decode success, decoded_length=" << decoded_str.length() << std::endl;
    } catch (const std::exception& e) {
        // Base64解码失败，返回原始字符串
        std::cout << "decrypt_password: Base64 decode failed: " << e.what() << ", treat as plaintext" << std::endl;
        std::cout << "decrypt_password: Return plaintext password: " << str << std::endl;
        return str;
    }

    // URL解码
    std::string final_str = url_decode(decoded_str);
    
    std::cout << "decrypt_password: Decrypt success, final_password_length=" << final_str.length() << std::endl;
    std::cout << "decrypt_password: Return decrypted password: " << final_str << std::endl;
    
    return final_str;
}

int main() {
    std::cout << "=== 密码解密测试程序 ===" << std::endl;
    
    // 测试明文密码
    std::cout << "\n测试1: 明文密码" << std::endl;
    std::string plaintext = "password123";
    std::string result1 = decrypt_password(plaintext);
    std::cout << "输入: " << plaintext << std::endl;
    std::cout << "输出: " << result1 << std::endl;
    
    // 测试空密码
    std::cout << "\n测试2: 空密码" << std::endl;
    std::string empty = "";
    std::string result2 = decrypt_password(empty);
    std::cout << "输入: (空)" << std::endl;
    std::cout << "输出: (空)" << std::endl;
    
    // 测试用户输入的密文
    std::cout << "\n测试3: 用户输入密文" << std::endl;
    std::string encrypted;
    std::cout << "请输入要解密的密文 (或输入 'quit' 退出): ";
    std::getline(std::cin, encrypted);
    
    if (encrypted != "quit" && !encrypted.empty()) {
        std::string result3 = decrypt_password(encrypted);
        std::cout << "输入: " << encrypted << std::endl;
        std::cout << "输出: " << result3 << std::endl;
    }
    
    return 0;
} 
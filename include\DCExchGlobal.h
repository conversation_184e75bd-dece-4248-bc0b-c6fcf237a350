#ifndef __DCEXCH_GLOBAL_H__
#define __DCEXCH_GLOBAL_H__
#include <pthread.h>
#include <unistd.h>
#include <string>
#include <map>
using namespace std;

typedef map<int, long> MapCount;

enum CountKey
{
	G_EnM0CTaskSumCnt = 0,
	G_EnErrorSumRecordCnt = 1,
	
};

class DCExchGlobal
{
public:
    static DCExchGlobal* instance();

	void SetCount(int iKey, long lnCount = 0);
	/*
	 * 函数名: long AddCount(int iKey, int lnCount = 1)
	 * 函数功能: 增加计数, 未设置key则新增并且从0增加计数
	 * 返回值: 返回设置后的计数
	*/
	long AddCount(int iKey, int lnCount = 1);
	/*
	 * 函数名: long ReduceCount(int iKey, int lnCount = 1)
	 * 函数功能: 减少计数, 未设置key则新增并且从0减少计数
	 * 返回值: 返回设置后的计数
	*/
	long ReduceCount(int iKey, int lnCount = 1);
	/*
	 * 函数名: long GetCount(int iKey)
	 * 函数功能: 获取计数, 非已知key, 返回0
	 * 返回值: 返回计数
	*/
	long GetCount(int iKey);

protected:
	DCExchGlobal();
	~DCExchGlobal();

private:
	static DCExchGlobal* m_pInstance;
	pthread_mutex_t m_GmtxCout;
	MapCount m_GmapCout;
};

#endif
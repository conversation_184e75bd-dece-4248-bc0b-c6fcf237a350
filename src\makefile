include ../../../comm.mk

AREA				= "\"ShanXi"\"
RELEASE_VERSION		= "\"V5.0.8.0\""
SVN_VERSION			= "\"*****\""
DATE_TIME			= $(shell date "+%Y-%m-%d %H:%M:%S")

VERSION_DEF=-DAREA=$(AREA) -DRELEASE_VERSION=$(RELEASE_VERSION) -DSVN_VERSION=$(SVN_VERSION) -DBUILD_DATE="\"$(DATE_TIME)\""

RELEASE_PATH:=$(LBSPUBROOT)/CTPC/release
PWD=$(shell pwd)
DGR=$(LBSPUBROOT)/third/DCGrayscaleRoute
BASE_ROOT	:= ../../
BASE_INC	:= -I$(BASE_ROOT)/DCMsgExch/include


BINS= ./MsgExch

INCS		:= -I/usr/include/ \
               $(BASE_INC) \
		-I$(ACE_INC_PATH)  \
		-I$(AVRO)/include \
		       -I$(DFM)/include \
               -I$(ITF)/include \
               -I$(TXML)/include \
               -I$(DFM_INC_PATH) $(BILLCOM_INC) \
		       -I$(DCFILE_INC) \
               -I$(DCLOGCLI)/include \
               -I$(DCFC_INC) \
               -I$(DCF_BASE)/src/kpr \
               -I$(JSON_INC) \
               -I$(JAVA_HOME)/include \
		       -I$(JAVA_HOME)/include/linux \
		 -I$(KPI_SENDER_INC) \
		 -I$(DGR)/include
          
LIBS		:= -L$(LBSPUBROOT) -L$(ACE_LIB_PATH) -lACE -L$(DCLOGCLI)/lib -ldclogcli -L$(DFM)/lib -ldfm -L$(AVRO)/lib $(TXML)/lib/libtinyxml.a  -L$(DCFC_LIB) -L$(KPI_SENDER_LIB) -lcpprest -ldcfclient_new -lhiredis_vip -L$(DCFILE_LIB) -ldcfilesdk -L$(JSON_LIB) -ljsoncpp -lz -lpthread -lkpisender  -L$(DGR)/lib -ldcgrayscaleroute


EXCH_SRCS	:= MsgExch.cpp DCTaskService.cpp DCPairList.cpp DCPairManager.cpp DCSendCallBack.cpp TThread.cpp DCDBHander.cpp DCExchCfg.cpp DCOprStruct.cpp DCExchDB.cpp DCTaskSum.cpp DCTaskServiceSum.cpp DCTaskServiceEdc.cpp DCPerfLogStatistic.cpp DCExchGlobal.cpp
DCS_OBJS	:= $(addsuffix .o,$(basename ${EXCH_SRCS}))
OBJS		:= $(DCS_OBJS)
EXFLAGS = -Wall -Wno-unused-function -Wno-format -DLINUX -DACE_LACKS_PRAGMA_ONCE -DACE_HAS_INLINED_OSCALLS
bintarget=$(BINS)

.PHONY:all clean dup

all: ${bintarget}

#****************************************************************************
${bintarget}: ${DCS_OBJS}
	$(CC)  $(LDFLAGS)  $(EXFLAGS) -o  ${bintarget} ${DCS_OBJS} ${LIBS}
	tar -zcvf MsgExch.tar.gz MsgExch

%.o : %.cpp
	$(CC) -c -DACE_LACKS_IOSTREAM_TOTALLY -std=c++11 $(CXXFLAGS) $(VERSION_DEF) $(EXFLAGS) $< -o $@ ${INCS}

clean:
	-rm -f core ${OBJS} ${bintarget}
	 
dup:
	@cp -pf ${bintarget} $(PROJECT_RPATH)/other/bin && echo "dup $(bintarget) to $(PROJECT_RPATH)/other/bin"

#include "DCExchCfg.h"
#include "DCExchDB.h"
#include "DCLogMacro.h"
#include "DCParseXml.h"
#include "DCSendCallBack.h"

DCExchCfg* DCExchCfg::m_instance = NULL;
DCPairList* DCExchCfg::m_pairList = NULL;


DCExchCfg* DCExchCfg::instance()
{
	if (NULL == m_instance)
	{
		m_instance = new DCExchCfg;
	}
	return m_instance;
}

DCExchCfg::DCExchCfg()
{
	m_activeConfig = ACTIVE_CONFIG_NULL;
	m_dbm = NULL;
}

DCExchCfg::~DCExchCfg()
{

}


int DCExchCfg::Init(const char* path)
{
	strcpy(m_configPath, path);

	int ret = DCParseXml::Instance()->Init("MsgExch",path);
	if(ret)
	{
		printf("init config[%s] failed\n,",path);
		return -1;
	}

	SExchCfg *sConfig = &m_exchCfg[0];
	ret = LoadCfg(sConfig);
	if (ret < 0)
	{
		return ret;
	}

	m_activeConfig = ACTIVE_CONFIG_MASTER;

	return 0;
}

void DCExchCfg::InitGrayscaleRoute(DCDBManer* dbManer, std::string strSubscriber)
{
	//灰度配置数据初始化
	m_dbm = dbManer;
	
	SExchCfg *sConfig = GetConfig();

	// 传递路由环节列表
	int iRet = DCGrayscaleRoute::instance()->init(m_dbm, strSubscriber.c_str()); 
	if (iRet < 0)
	{
		printf("DCGrayscaleRoute init data failed,Subscriber=[%s]\n", strSubscriber.c_str());
	}

	// 构建路由环节列表
	std::vector<std::string> routeProcesses;
	if (!sConfig->RouteCTGProcess.empty())
	{
		routeProcesses.push_back(sConfig->RouteCTGProcess);
	}
	if (!sConfig->RouteRATEProcess.empty())
	{
		routeProcesses.push_back(sConfig->RouteRATEProcess);
	}
	if (!sConfig->RouteACCTProcess.empty())
	{
		routeProcesses.push_back(sConfig->RouteACCTProcess);
	}

	// 初始化灰度路由的DCF客户端
	if (!routeProcesses.empty())
	{
		DCExchCfg::instance()->InitGrayRouteDCFClients(routeProcesses);
	}

}

int DCExchCfg::UpdateConfig()
{
	int ret = DCParseXml::Instance()->Init("MsgExch",m_configPath);
	if(ret)
	{
		printf("init config[%s] failed\n,",m_configPath);
		return -1;
	}
	SExchCfg *sConfig = &m_exchCfg[1];
	if(ACTIVE_CONFIG_MASTER == m_activeConfig)
	{//当前正在使用主，则加载数据到备
		sConfig = &m_exchCfg[1];
		printf("exch load slave data\n");
	}
	else 
	{
		sConfig = &m_exchCfg[0];
		printf("exch load master data\n");
	}
	
	ret = LoadCfg(sConfig);
	if (ret)
	{
		return ret;
	}

	return 0;
}


int DCExchCfg::LoadCfg(SExchCfg *cfg)
{
	cfg->logserv = DCParseXml::Instance()->GetParam("logAddr","MsgExch/log");
	cfg->loglevel  = atoi(DCParseXml::Instance()->GetParam("level","MsgExch/log"));
	const char* perf_level_param = DCParseXml::Instance()->GetParam("perf","MsgExch/log");
    if(perf_level_param)
	{
		cfg->perf_level = atoi(perf_level_param);
	}
	else
	{
		cfg->perf_level = 0;
	}
	
	const char* perf_threshold_param = DCParseXml::Instance()->GetParam("perf.ms","MsgExch/log");
	if(perf_threshold_param)
	{
		cfg->perf_threshold = atoi(perf_threshold_param);
	}
	else
	{
		cfg->perf_threshold = 50;
	}
	cfg->staffId = DCParseXml::Instance()->GetParam("LogOperId","MsgExch/log");
	
	const char *szParamValue = DCParseXml::Instance()->GetParam("FlowTimeOut","MsgExch");
	if(!szParamValue){
        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","Get MsgExch/FlowTimeOut fail");
        return -1;
	}
	else{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","Get MsgExch/FlowTimeOut: %s",szParamValue);
	}
	int FlowTimeOut = atoi(szParamValue);
	cfg->FlowTimeOut = FlowTimeOut<=0?100:FlowTimeOut;//超时时间默认100ms

	const char *pdelay = DCParseXml::Instance()->GetParam("delay","MsgExch");
	if(pdelay){
		char temp[128] = {0};
		sprintf(temp,"%s",pdelay);
		char *p = strtok(temp,",");
		while(p != NULL)
		{
			cfg->vdelay.push_back(atoi(p));
			p = strtok(NULL,",");
		}
	}	
	
	cfg->iGetFileWay = atoi(DCParseXml::Instance()->GetParam("getfileway","MsgExch"));	
	if (cfg->iGetFileWay == 0)
	{
		cfg->iGetFileWay == 1;
	}
	else if (cfg->iGetFileWay > 2)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","Get MsgExch configuration getfileway fail");
		return -1;
	}
	
    cfg->zkAddr = DCParseXml::Instance()->GetParam("zkAddr","MsgExch");	
    cfg->serviceCTPC = DCParseXml::Instance()->GetParam("service_ctpc","MsgExch");	
	cfg->zkRoot      = DCParseXml::Instance()->GetParam("zkRoot","MsgExch");
	cfg->zkRootQueue = DCParseXml::Instance()->GetParam("zkRootQueue","MsgExch");

    cfg->zkAddrBILL  = DCParseXml::Instance()->GetParam("zkAddrBILL","MsgExch");	
    cfg->serviceBILL = DCParseXml::Instance()->GetParam("service_bill","MsgExch");	
	cfg->serviceBILLBigAcct = DCParseXml::Instance()->GetParam("serviceBILLBigAcct","MsgExch");	
	cfg->zkRootBILL      = DCParseXml::Instance()->GetParam("zkRootBILL","MsgExch");
	cfg->zkRootQueueBILL = DCParseXml::Instance()->GetParam("zkRootQueueBILL","MsgExch");
	
	cfg->loginpwd = DCParseXml::Instance()->GetParam("login_password","MsgExch");
	cfg->aespwd   = DCParseXml::Instance()->GetParam("aes_password","MsgExch");

	cfg->fileServ = DCParseXml::Instance()->GetParam("fileServAddr","MsgExch");	
	cfg->filepath = DCParseXml::Instance()->GetParam("filepath","MsgExch");
	cfg->servLogLevel = atoi(DCParseXml::Instance()->GetParam("service_log_level","MsgExch"));	
	cfg->deleteflag =  atoi(DCParseXml::Instance()->GetParam("is_deletetask","MsgExch"));
	cfg->iDelCheckUidFlag = atoi(DCParseXml::Instance()->GetParam("is_delete_dca_checkuid","MsgExch"));
	cfg->sendtype = atoi(DCParseXml::Instance()->GetParam("sendtype","MsgExch"));	

	szParamValue = DCParseXml::Instance()->GetParam("FlowControlSize","MsgExch");
	if(!szParamValue){
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","Get MsgExch/FlowControlSize fail");
		return -1;
	}
	else{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","Get MsgExch/FlowControlSize: %s",szParamValue);
	}
	int FlowControlSize = atoi(szParamValue);
	cfg->FlowControlSize = FlowControlSize<=0?1024:FlowControlSize;//默认流控大小1024

	cfg->BlockQueueSize = atoi(DCParseXml::Instance()->GetParam("blockQueueSize","MsgExch"));//filequeue流控值

	cfg->addr =	DCParseXml::Instance()->GetParam("screenAddr","MsgExch/screen");
	cfg->delay =  atoi(DCParseXml::Instance()->GetParam("delay","MsgExch/screen"));
	cfg->portocol =	atoi(DCParseXml::Instance()->GetParam("portocol","MsgExch/screen"));
	cfg->host =	DCParseXml::Instance()->GetParam("host","MsgExch/gth");

	cfg->lsendMsgCount = atol(DCParseXml::Instance()->GetParam("speednum","MsgExch"));	
	cfg->sleeptime = atoi(DCParseXml::Instance()->GetParam("speedtime","MsgExch")); 


	int printRate =  atoi(DCParseXml::Instance()->GetParam("print_rate","MsgExch"));
	cfg->printRate=printRate>0?printRate:5;		

	cfg->deleteflag = atoi(DCParseXml::Instance()->GetParam("is_deletetask","MsgExch"));
	cfg->threadnum  = atoi(DCParseXml::Instance()->GetParam("ansthread","MsgExch"));	
	cfg->arrangeThreadnum  = atoi(DCParseXml::Instance()->GetParam("arrangeThread","MsgExch"));
	if(cfg->arrangeThreadnum <= 0)
	{
		cfg->arrangeThreadnum = 1;
	}
	cfg->worker_num= atoi(DCParseXml::Instance()->GetParam("workernum","MsgExch"));
	cfg->nTreadNum= atoi(DCParseXml::Instance()->GetParam("DBTreadNum","MsgExch"));

	cfg->bakFilePath = DCParseXml::Instance()->GetParam("bakPath","MsgExch");
	if(cfg->bakFilePath.length() == 0)
	{
		cfg->bakFilePath = "./";
	}
	else if(cfg->bakFilePath.length() > 0)
	{
		if(cfg->bakFilePath[cfg->bakFilePath.length()-1] != '/')
		{
			cfg->bakFilePath += "/";
		}
	}
	
	cfg->brokers =	DCParseXml::Instance()->GetParam("mqservAddr","MsgExch/mq");
	cfg->topic = DCParseXml::Instance()->GetParam("topic","MsgExch/mq");
	cfg->groupconsumer = DCParseXml::Instance()->GetParam("groupName","MsgExch/mq");
	const char* LogRatingSwitch = DCParseXml::Instance()->GetParam("LogRatingSwitch","MsgExch");
	if(LogRatingSwitch)
	{
		cfg->recordLogRatingSwitch = atoi(LogRatingSwitch)>0?1:0;
	}
	else
	{
		cfg->recordLogRatingSwitch = 0;
	}
	cfg->examineSwitch= atoi(DCParseXml::Instance()->GetParam("examineFileSwitch","MsgExch"));
	cfg->examinePath = DCParseXml::Instance()->GetParam("examineFilePath","MsgExch");
	cfg->sqlConf = DCParseXml::Instance()->GetParam("sql_config","MsgExch");
	if(cfg->sqlConf.length() == 0)
	{
		cfg->sqlConf = "msgExch.sql.xml";
	}
	cfg->routeByNbr = atoi(DCParseXml::Instance()->GetParam("routeByNbr","MsgExch"));
	cfg->breakPointPath = DCParseXml::Instance()->GetParam("breakPointPath","MsgExch");
	cfg->scan_dir = DCParseXml::Instance()->GetParam("scan_dir","MsgExch");
	cfg->bak_dir = DCParseXml::Instance()->GetParam("bak_dir","MsgExch");
	cfg->detail_dir = DCParseXml::Instance()->GetParam("detail_dir","MsgExch");
	cfg->error_dir = DCParseXml::Instance()->GetParam("error_dir","MsgExch");
	cfg->sum_brk_dir = DCParseXml::Instance()->GetParam("sum_brk_dir","MsgExch");
	cfg->Info_dir = DCParseXml::Instance()->GetParam("Info_dir","MsgExch");
	cfg->sumType = DCParseXml::Instance()->GetParam("sumTypeList","MsgExch");
	cfg->errorCodeTime = atoi(DCParseXml::Instance()->GetParam("errorCodeTimes","MsgExch"));
	if(cfg->errorCodeTime == 0)
	{
		cfg->errorCodeTime = 20;
	}
	cfg->infoTimeDelay = atoi(DCParseXml::Instance()->GetParam("infoTimeDelay","MsgExch"));
	if(cfg->infoTimeDelay == 0)
	{
		cfg->infoTimeDelay = 5;
	}
	cfg->sum_reg = DCParseXml::Instance()->GetParam("sumreg","MsgExch");
	if(cfg->sum_reg.length() == 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","Get MsgExch/sumreg fail");
		return -1;
	}
	cfg->errorCodeList = DCParseXml::Instance()->GetParam("errorCodeList","MsgExch");
	cfg->breakIP = DCParseXml::Instance()->GetParam("msgexchIP","MsgExch");
	cfg->password = DCParseXml::Instance()->GetParam("msgexchIPPassword","MsgExch");
	cfg->isMerge = atoi(DCParseXml::Instance()->GetParam("isMerge","MsgExch"));
	cfg->logCodePre = DCParseXml::Instance()->GetParam("logCodePre","MsgExch");
    cfg->operList = DCParseXml::Instance()->GetParam("operList", "MsgExch");

	//task get mode
	const char* param = NULL;
	param = DCParseXml::Instance()->GetParam("taskmode", "MsgExch");
	if(param && param[0])
	{
		cfg->taskmode = atoi(param);
	}
	else
	{
		cfg->taskmode = 0;
	}
	param = DCParseXml::Instance()->GetParam("tsklimitnum", "MsgExch");
	if(param && param[0])
	{
		cfg->tasklimitnum = atoi(param);
	}
	else
	{
		cfg->tasklimitnum = 1;
	}
	//filter cfg
	param = DCParseXml::Instance()->GetParam("filterFlag", "MsgExch");
	if(param && param[0])
	{
		cfg->filterFlag = atoi(param);
	}
	else
	{
		cfg->filterFlag = 0;
	}
	param = DCParseXml::Instance()->GetParam("filterlist", "MsgExch");
	if(param && param[0])
	{
		cfg->filterlist = param;
	}
	else
	{
		cfg->filterlist = "";
	}

	param = DCParseXml::Instance()->GetParam("timeoutRange", "MsgExch");
	if(param && param[0])
	{
		cfg->lnTimeoutRange = atol(param);
	}
	else
	{
		cfg->lnTimeoutRange = 600;
	}
	param = DCParseXml::Instance()->GetParam("timeoutCount", "MsgExch");
	if(param && param[0])
	{
		cfg->lnTimeoutCount = atol(param);
	}
	else
	{
		cfg->lnTimeoutCount = 1000000000;
	}

	param = DCParseXml::Instance()->GetParam("dcfInitWait", "MsgExch");
	if(param && param[0])
	{
		cfg->bDCFInitWait = (param[0] == '1');
	}
	else
	{
		cfg->bDCFInitWait = false;
	}
	param = DCParseXml::Instance()->GetParam("dcfInitWaitTimeout", "MsgExch");
	if(param && param[0])
	{
		cfg->lnDCFInitWaitTimeout = atol(param);
	}
	else
	{
		cfg->lnDCFInitWaitTimeout = 1000;
	}
	param = DCParseXml::Instance()->GetParam("dcfWorkNumCTPC", "MsgExch");
	if(param && param[0])
	{
		cfg->nDCFWorkNumCTPC = atoi(param);
	}
	else
	{
		cfg->nDCFWorkNumCTPC = 1;
	}
	param = DCParseXml::Instance()->GetParam("dcfWorkNumBILL", "MsgExch");
	if(param && param[0])
	{
		cfg->nDCFWorkNumBILL = atoi(param);
	}
	else
	{
		cfg->nDCFWorkNumBILL = 1;
	}

	param = DCParseXml::Instance()->GetParam("SysStateFlag", "MsgExch");
	if(param && param[0])
	{
		cfg->nSysStateFlag = atoi(param);
	}
	else
	{
		cfg->nSysStateFlag = 0;
	}

	param = DCParseXml::Instance()->GetParam("ContinuousFailExit", "MsgExch");
	if(param && param[0])
	{
		cfg->nContinueFailExit = atoi(param);
	}
	else
	{
		cfg->nContinueFailExit = 10;
	}

	param = DCParseXml::Instance()->GetParam("TraceNumFlag", "MsgExch");
	if(param && param[0])
	{
		cfg->nTraceNumFlag = atoi(param);
	}
	else
	{
		cfg->nTraceNumFlag = 0;
	}

	cfg->sBackUpFileType = DCParseXml::Instance()->GetParam("BackupFileType", "MsgExch");
	param = DCParseXml::Instance()->GetParam("CheckFileLines", "MsgExch");
	if(param && param[0])
	{
		cfg->nCheckFileLines = atoi(param);
	}
	else
	{
		cfg->nCheckFileLines = 0;
	}

	param = DCParseXml::Instance()->GetParam("BatchTime", "MsgExch");
	if (param && param[0])
	{
		cfg->iBatchTime = atoi(param);
		if (cfg->iBatchTime == 0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "find invalid batchtime[%d]", cfg->iBatchTime);
			return -1;
		}
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Get MsgExch/BatchTime fail, send to RE need");
		return -1;
	}

	param = DCParseXml::Instance()->GetParam("KpiDelayMs", "MsgExch");
	if (param && param[0])
	{
		DCKpiSender::instance()->SetParam("delay", param);
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Get MsgExch/KpiDelayMs fail");
	}

	param = DCParseXml::Instance()->GetParam("KpiPortocol", "MsgExch");
	if (param && param[0])
	{
		DCKpiSender::instance()->SetParam("protocol", param);
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Get MsgExch/KpiPortocol fail");
	}

	param = DCParseXml::Instance()->GetParam("KpiFlag", "MsgExch");
	if (param && param[0])
	{
		DCKpiSender::instance()->SetParam("flag", param);
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Get MsgExch/KpiFlag fail");
	}

	param = DCParseXml::Instance()->GetParam("KpiAddr", "MsgExch");
	if (param && param[0])
	{
		DCKpiSender::instance()->SetParam("addr", param);
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Get MsgExch/KpiAddr fail");
	}

	if (DCKpiSender::instance()->Init() != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "DCKpiSender init fail. error_code=%d, error_info=%s", 
			DCKpiSender::instance()->ErrorCode(), DCKpiSender::instance()->ErrorInfo());
	}

	param = DCParseXml::Instance()->GetParam("KpiLatn", "MsgExch");
	if (param && param[0])
	{
		std::list<string> listLatn;
		string strLatn = param;
		SplitString(strLatn.c_str(), '|', listLatn);
		DCKpiMon *ptrBPMon = DCKpiSender::instance()->GetKPIHandle("BILLING", "REC", "MSGEX");
		if (ptrBPMon)
		{
			DCKpiSender::instance()->group_all_init(ptrBPMon, "TraceKpi", listLatn);
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "monitor init fail. error code:%d, error info:%s", 
				DCKpiSender::instance()->ErrorCode(), DCKpiSender::instance()->ErrorInfo());
		}

		if (ptrBPMon)
		{
			DCKpiSender::instance()->group_all_init(ptrBPMon, "TraceKpiA", listLatn);
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "monitor init fail. error code:%d, error info:%s", 
				DCKpiSender::instance()->ErrorCode(), DCKpiSender::instance()->ErrorInfo());
		}
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Get MsgExch/KpiLatn fail");
	}
	cfg->Subscriber = DCParseXml::Instance()->GetParam("Subscriber","MsgExch/gray");
	cfg->RouteCTGProcess = DCParseXml::Instance()->GetParam("RouteCTGProcess","MsgExch/gray");
	cfg->RouteACCTProcess = DCParseXml::Instance()->GetParam("RouteACCTProcess","MsgExch/gray");
	cfg->RouteRATEProcess = DCParseXml::Instance()->GetParam("RouteRATEProcess","MsgExch/gray");
	cfg->nRefreshIntr = atoi(DCParseXml::Instance()->GetParam("GrayRefreshIntr","MsgExch/gray"));

	return 0;
}

int DCExchCfg::SplitString(const char* pszStr, const char cSeparator, std::list<std::string>& vecStr)
{
	if (!pszStr)
	{
		return 0;
	}

	string strField;
	strField.clear();
	for (const char* p = pszStr; *p; p++)
	{
		if ((*p) != cSeparator)
		{
			strField.push_back(*p);
			continue;
		}

		vecStr.push_back(strField);
		strField.clear();
	}

	vecStr.push_back(strField);

	return 0;
}

int DCExchCfg::InitSplitInfo(DCDBManer* dbManer)
{
    if (dbManer == NULL)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "dbManer NULL");
        return -1;
    }

    dbManer->FastReset();
    UDBSQL *getFileType = dbManer->GetSQL("PARSPLITFIRST");
    if (getFileType == NULL)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "GetSQL[SPLITFIRST] failed");
        return -1;

    }
    char fileType[16] = { 0 };
    int switchTypeId = 0;
    int switchId = 0;
    char value[16] = { 0 };
    try
    {
        getFileType->UnBindParam();
        getFileType->Execute();
        while (getFileType->Next())
        {
            memset(fileType, 0x0, sizeof(fileType));

            getFileType->GetValue(1, switchTypeId);
            getFileType->GetValue(2, fileType);
            getFileType->GetValue(3, switchId);
            if (!strcmp(fileType, "03"))
            {
                DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "get split file,switchId[%d]!", switchId);
                SplitInfo info;   // 需要对值进行初始化
                int iIndexSize = sizeof(info.index) / sizeof(info.index[0]);
                for (int i = 0; i < iIndexSize; i++)
                {
                    info.index[i] = -1;
                }
                int findFlag = 0;
                char eleamCode[10] = { 0 };
                UDBSQL* m_acQuery = dbManer->GetSQL("PARSPLIT");
                if (m_acQuery == NULL)
                {
                    DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "GetSQL[PARSPLIT] failed");
                    return -1;
                }
                try
                {
                    m_acQuery->UnBindParam();
                    m_acQuery->BindParam(1, switchTypeId);
                    m_acQuery->Execute();
                    while (m_acQuery->Next())
                    {
                        memset(eleamCode, 0x0, sizeof(eleamCode));
                        m_acQuery->GetValue(1, eleamCode);
                        if (!strcmp(eleamCode, "OID"))
                        {
                            m_acQuery->GetValue(2, info.index[0]);
                        }
                        else if (!strcmp(eleamCode, "CER"))
                        {
                            m_acQuery->GetValue(2, info.index[1]);
                        }
                        else if (!strcmp(eleamCode, "CAN"))
                        {
                            m_acQuery->GetValue(2, info.index[2]);
                        }
                        m_acQuery->GetValue(3, value);
                        info.splitChar = value[0];
                        findFlag = 1;
                    }
                }
                catch (UDBException& e)
                {
                    DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "UDBException error[%s]!", e.what());
					DCExchDB::CheckDBState(dbManer, m_checktime);
                    return -1;
                }

                if (findFlag == 1)
                {
                    m_mapSplitInfo.insert(make_pair(switchId, info));
                }
            }
        }
    }
    catch (UDBException& e)
    {
        getFileType->UnBindParam();
        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "UDBException error[%s]!\n", e.what());
		DCExchDB::CheckDBState(dbManer, m_checktime);
        return -1;
    }

    return 0;
}

SExchCfg* DCExchCfg::GetConfig()
{
	SExchCfg* pConfig = NULL;
	if(ACTIVE_CONFIG_MASTER == m_activeConfig)
	{
		pConfig = &m_exchCfg[0];
	}
	else
	{
		pConfig = &m_exchCfg[1];		
	}
	return pConfig;

}

int DCExchCfg::run()
{
	int ret = 0;
	ret = activate(THR_NEW_LWP | THR_JOINABLE | THR_INHERIT_SCHED | THR_SCOPE_SYSTEM, 1);
	if(ret < 0)
	{
		return -1;
	}
	return 0;
}	


int DCExchCfg::svc()
{
	int ret = 0;
	long mtime_old = time(NULL);
	long mtime_new = time(NULL);
	time_t lasttime = time(NULL);
	SExchCfg* pConfig = NULL;
	struct stat newStat;

	while(1)
	{
		sleep(1);
		
		//配置文件更新检查
		ret = stat(m_configPath, &newStat);
		if(ret != 0)
		{
			printf("error: no finded config file %s\n",m_configPath);
			continue;
		}
		
		if(newStat.st_mtime > mtime_new)
		{
		    mtime_new = newStat.st_mtime;
		}
		
		SExchCfg * pConfig = NULL;
		if(mtime_new > mtime_old)
		{
			mtime_old = mtime_new;
			ret = UpdateConfig();
			if(ret != 0)
			{		
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, ""," config file load error %s",m_configPath);
			}
			else
			{			
				//加载成功，切换标识, m_activeConfig
				if(ACTIVE_CONFIG_MASTER == m_activeConfig)
				{
					m_activeConfig = ACTIVE_CONFIG_SLAVE;
					pConfig = &m_exchCfg[1];
				}
				else
				{
					m_activeConfig = ACTIVE_CONFIG_MASTER;
					pConfig = &m_exchCfg[0];
				}

                // 修改日志级别
               DCLOG_SETLEVEL(DCLOG_CLASS_SYS,  pConfig->loglevel);
               DCLOG_SETLEVEL(DCLOG_CLASS_BIZ,  pConfig->loglevel);
               DCLOG_SETLEVEL(DCLOG_CLASS_PERF,  pConfig->perf_level);		   
			   DCLOG_SETCTL(DCLOG_MASK_PERF,pConfig->perf_threshold*1000);//单位转化为微秒
			}
		}
		// 灰度配置数据刷新
		pConfig = GetConfig();
		time_t curtime = time(NULL);
		if (curtime > lasttime + pConfig->nRefreshIntr)
		{
			lasttime = curtime;
			if (NULL == m_dbm)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "m_dbm not init.");
				//				return -1;
			}
			ret = DCGrayscaleRoute::instance()->init(m_dbm, pConfig->Subscriber.c_str()); // 刷新数据
			if (ret < 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "DCGrayscaleRoute update data failed,Subscriber=[%s]", pConfig->Subscriber.c_str());
				//				return -1;
			}

			// 初始化灰度路由DCF客户端
			std::vector<std::string> routeProcesses;
			if (!pConfig->RouteCTGProcess.empty())
				routeProcesses.push_back(pConfig->RouteCTGProcess);
			if (!pConfig->RouteACCTProcess.empty())
				routeProcesses.push_back(pConfig->RouteACCTProcess);
			if (!pConfig->RouteRATEProcess.empty())
				routeProcesses.push_back(pConfig->RouteRATEProcess);

			if (!routeProcesses.empty())
			{
				ret = InitGrayRouteDCFClients(routeProcesses);
				if (ret < 0)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "InitGrayRouteDCFClients failed for Subscriber=[%s]", pConfig->Subscriber.c_str());
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "InitGrayRouteDCFClients success for Subscriber=[%s]", pConfig->Subscriber.c_str());
				}
			}
		}
	}
	return 0;
}

//判断double类型的数值是否为0
bool DCExchCfg::bDoubleEqualZero(double dValue)
{	
    double epsilon = 1e-8; // 设置一个很小的阈值
    if (fabs(dValue) > epsilon)
	{
		return false;
    } 
	else
	{
		return true;
    }	
}

int DCExchCfg::InitGrayRouteDCFClients(const std::vector<std::string> &routeProcesses)
{
	SExchCfg *sConfig = GetConfig();

	if (sConfig == NULL || routeProcesses.empty())
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "InitGrayRouteDCFClients: Invalid parameters");
		return -1;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Begin InitGrayRouteDCFClients with %d processes", routeProcesses.size());

	int ret = 0;
	
	// 获取main函数中的pairList指针
	DCPairList *mainPairList = GetPairList();
	if (mainPairList == NULL)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "InitGrayRouteDCFClients: Main pairList pointer is NULL");
		return -1;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Using main pairList: %p", mainPairList);

	// 为每个路由环节创建DCF客户端并初始化
	for (size_t i = 0; i < routeProcesses.size(); i++)
	{
		const std::string &routeProcess = routeProcesses[i];
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Initializing DCF client for route process: %s", routeProcess.c_str());

		std::vector<St_route_env> envs;
		ret = DCGrayscaleRoute::instance()->GetRouteEnvs(routeProcess.c_str(), envs);
		if (ret < 0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, ret, "", "GetRouteEnvs failed for process=%s",
					 routeProcess.c_str());
			continue;
		}

		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Got %d envs for process=%s, Runmode=%d",
				 envs.size(), routeProcess.c_str(), sConfig->nRunmode);

		if (sConfig->RouteACCTProcess == routeProcess && sConfig->nRunmode !=2)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Skipping RouteACCTProcess for process=%s in Runmode=%d",
					 routeProcess.c_str(), sConfig->nRunmode);
			continue;
		}
		else if ((sConfig->RouteCTGProcess == routeProcess || sConfig->RouteRATEProcess == routeProcess) && (sConfig->nRunmode != 0 && sConfig->nRunmode != 10))
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Skipping RouteCTGProcess or RouteRATEProcess for process=%s in Runmode=%d",
					 routeProcess.c_str(), sConfig->nRunmode);
			continue;
		}

		for (size_t j = 0; j < envs.size(); j++)
		{
			const St_route_env &env = envs[j];
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Got env for process=%s, env_code=%s, addr=%s, path=%s",
					 routeProcess.c_str(), env.env_code.c_str(), env.env_addr.c_str(), env.env_path.c_str());

			// 检查是否需要初始化
			if (!env.need_init)
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Skipping env %s for process=%s as it does not need initialization",
						 env.env_code.c_str(), routeProcess.c_str());
				continue;
			}

			// 创建DCF客户端实例
			dcf_new::DCFLocalClient *pDcfClient = NULL;
			pDcfClient = new dcf_new::DCFLocalClient();
			if (pDcfClient == NULL)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Failed to create DCFLocalClient for process=%s, env_code=%s",
						 routeProcess.c_str(), env.env_code.c_str());
				continue;
			}

			// 设置AES密码
			pDcfClient->setAESPassword(sConfig->aespwd);
			pDcfClient->setZookAddrAndRootDir(env.env_addr, env.env_path);
			pDcfClient->setZookAddrAndRootDir(env.env_addr, env.env_path, true);

			// 使用主函数中的pairList创建回调处理对象
			DCSendCallBack *pSendCallback = new DCSendCallBack(mainPairList);
			
			// 添加回调处理器
			pDcfClient->addCallbackAsyncHandler(pSendCallback);

			// 设置客户端回调线程数
			pDcfClient->setClientCallbackThreadsMax(sConfig->threadnum);

			// 设置初始化等待选项
			pDcfClient->setInitWait(sConfig->bDCFInitWait);
			pDcfClient->setInitWaitTimeoutMills(sConfig->lnDCFInitWaitTimeout);

			// 设置工作线程数
			if (sConfig->nRunmode == 0 && sConfig->RouteCTGProcess == routeProcess)
			{
				pDcfClient->setSerivceWorkNum(sConfig->serviceCTPC, sConfig->nDCFWorkNumCTPC);
			}
			else if ((sConfig->nRunmode == 0 && sConfig->RouteRATEProcess == routeProcess) ||
					 (sConfig->nRunmode == 2 && sConfig->RouteACCTProcess == routeProcess))
			{
				pDcfClient->setSerivceWorkNum(sConfig->serviceBILL, sConfig->nDCFWorkNumBILL);
			}

			// 启动DCF客户端
			if (pDcfClient->start())
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Failed to start DCFLocalClient for process=%s, env_code=%s",
						 routeProcess.c_str(), env.env_code.c_str());
				delete pDcfClient;
				delete pSendCallback;
				continue;
			}

			// 将DCF客户端与环境绑定
			ret = DCGrayscaleRoute::instance()->InitDCFClient(
				routeProcess.c_str(),
				env.env_code.c_str(),
				pDcfClient);

			if (ret < 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, ret, "", "InitDCFClient failed for process=%s, env_code=%s",
						 routeProcess.c_str(), env.env_code.c_str());
				pDcfClient->shutdown();
				delete pDcfClient;
				delete pSendCallback;
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Successfully initialized DCF client for process=%s, env_code=%s",
						 routeProcess.c_str(), env.env_code.c_str());
			}
		}
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "End InitGrayRouteDCFClients");
	return 0;
}

void DCExchCfg::SetPairList(DCPairList* pairList)
{
    m_pairList = pairList;
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "DCExchCfg::SetPairList - pairList pointer set: %p", m_pairList);
}

DCPairList* DCExchCfg::GetPairList()
{
    return m_pairList;
}

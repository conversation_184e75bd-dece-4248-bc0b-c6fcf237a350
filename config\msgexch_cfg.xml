<?xml version="1.0" standalone="no"?>
<configuration>
  <MsgExch>
    <log>
      <param name="logAddr">127.0.0.1:7081</param>
      <param name="level">7</param>
      <param name="perf">2</param>
      <param name="perf.ms">0</param>
    </log>
    <param name="sql_config">msgexch_sql.xml</param><!--sql配置文件-->
    <param name="service_log_level">3</param>
    <param name="workernum">1</param>
    <param name="DBTreadNum">30</param>
    <param name="ansthread">15</param><!--应答线程数-->
    <param name="zkAddr">133.64.177.71:22810,133.64.177.72:22810,133.64.177.73:22810</param>
    <param name="zkRoot">/JSTORM01</param>
    <param name="zkAddrBILL">133.64.177.71:22810,133.64.177.72:22810,133.64.177.73:22810</param>
    <param name="zkRootBILL">/JSTORM01</param>
    <param name="service_ctpc">fmtservice_dmdb_919</param>
    <param name="service_bill">billingservice_dmdb_919</param>
    
    <param name="login_password">abcdefghijklmnopqrstuvwxyz</param>
    <param name="aes_password">aesabc</param>
    <param name="fileServAddr">client.json</param>
    <param name="filepath">/project/ducc03/sx_billt/data/file/</param>
    <param name="speednum">40</param><!--发送条数可配-->
    <param name="speedtime">10000</param><!--单位us，每发送多少条休眠-->
    <param name="is_deletetask">0</param><!--是否删除charge_files_details_[latn_id]-->
	<param name="is_delete_dca_checkuid">0</param><!--是否删除CACHE.CHECKUID-->
    <param name="LogRatingSwitch">1</param><!--写lograting表开关，1为打开，0关闭-->
    <param name="print_rate">5</param><!--几秒打印一次性能统计-->
    <param name="delay">1000,5000,10000,30000,60000,1800000</param><!--统计消息耗时时间间隔，单位ms-->
    <screen><!--推送前台指标-->
      <param name="screenAddr">************:9600</param><!--大屏指标接口服务-->
      <param name="delay">1000</param><!--大屏指标发送延迟，单位：毫秒，建议1000, 2500, 5000-->
      <param name="portocol">1</param><!--大屏指标发送协议，0：UDP，1：TCP-->
    </screen>
    <gth><!--推送后台指标-->
      <param name="host">127.0.0.1:9007</param><!--大屏指标发送的本机host-->
    </gth>
    <param name="sendtype">1</param><!--大屏开关-->
    <param name="FlowControlSize">10000</param><!--流控队列大小-->
    <param name="FlowTimeOut">600</param><!--超时时间，单位s-->
    <param name="zkRootQueue">/JSTORM01</param><!--filequeue队列zk路径-->
    <param name="zkRootQueueBILL">/JSTORM01</param><!--filequeue队列zk路径-->
    <param name="blockQueueSize">10000</param><!--filequeue流控大小-->
    <param name="examineFileSwitch">0</param><!--清单稽核写文件开关-->
    <param name="examineFilePath">/project/ducc03/sx_billt/data/msgexch</param><!--清单稽核写文件路径-->
    <param name="breakPointPath">/project/ducc03/sx_billt/data/msgexch/breakPoint</param><!--话单业务断点文件路径-->
    <param name="scan_dir">/project/ducc03/sx_billt/data/rollsum/bak/[latnId]/sum</param><!--sum文件扫描目录-->
    <param name="sumTypeList">realtimesum,common,bigacct,crossacct,superacct</param><!--sum文件类型目录列表-->
    <param name="sumreg">[a-zA-Z0-9_-]*_(sum)|(realtimesum)_[0-9]{3}_(888)|(5[5|6][0-9])_[0-9]{6}_[0-9]{4}_[0-9]{4}_[0-9]{14}_[0-9]{3}_[0-9]{6}.sum</param>
    <param name="bak_dir">/project/ducc03/sx_billt/data/msgexch/sumbak</param><!--扫描文件的备份路径-->
    <param name="detail_dir">/project/ducc03/sx_billt/data/msgexch/sumdetail</param><!--规整后sum文件目录-->
    <param name="error_dir">/project/ducc03/sx_billt/data/msgexch/errfile</param><!--异常文件存放路径-->
    <param name="Info_dir">/project/ducc03/sx_billt/data/msgexch/infofile</param><!--信息点文件路径-->
    <param name="sum_brk_dir">/project/ducc03/sx_billt/data/msgexch/sumbreakPoint</param><!--信息点文件路径-->
    <param name="errorCodeTimes">10000</param><!--5分钟内callback收到同一错误码次数达到阈值时程序安全退出-->
    <param name="infoTimeDelay">1</param><!--callback统计间隔，单位分钟-->
    <param name="errorCodeList">-10569,-30373,-110,-10000,-8</param><!--错误码监控列表，使用','分隔-->
    <param name="msgexchIP">bss_net@**************</param><!--其它主机msgexch的ip及用户，异常退出重启传输断点文件，多个用逗号分隔-->
    <param name="msgexchIPPassword">bss_net$2018</param><!--其它主机msgexch的ip及用户的密码-->
    <param name="operList">6119,7002,6113</param><!--配置是否按号码路由的业务列表-->
    <param name="bakPath"></param><!--断点文件路径，侦测中心用-->
    <param name="taskmode">1</param><!--1:查询任务再更新任务 其它:先更新,再查询 不配置默认为0-->
    <param name="tsklimitnum">20</param><!--taskmode=1 时 一次查询任务的数量-->
    <param name="filterFlag">0</param><!--taskmode=1 时 文件过滤开关 暂时保留，目前未实现-->
    <param name="filterlist"></param><!--taskmode=1 时 文件全路径包含过滤字符 暂时保留，目前未实现-->
    <param name="timeoutRange"></param><!--超时消息在timeoutRange时间内达到timeoutCount程序退出。单位：秒-->
    <param name="timeoutCount"></param><!--超时消息在timeoutRange时间内达到timeoutCount程序退出。单位：个-->
    <param name="dcfInitWait">0</param><!--是否等待所有dcf服务初始化开关，1：开，0：关-->
    <param name="dcfInitWaitTimeout">5000</param><!--dcf服务初始化等待超时时间，单位：毫秒ms-->
    <param name="dcfWorkNumCTPC">5</param><!--采预目标dcf服务对应的topo，worker个数-->
    <param name="dcfWorkNumBILL">5</param><!--批价目标dcf服务对应的topo，worker个数-->
    <param name="SysStateFlag">0</param> <!--0:正常处理 1：任务暂停.默认正常处理,当前任务处理完成后暂停-->
    <param name="ContinuousFailExit">10</param><!--连续处理任务失败（包括文件下载失败、数据库操作失败等）达到一定次数退出-->
    <param name="TraceNumFlag">0</param><!--号码追踪开关。0：关闭，1：打开，默认：0-->
	<param name="BackupFileType">101,102,103</param><!--需要额外写稽核日志表的FILE_TYPE 新增配置-->
    <param name="CheckFileLines">1</param><!--是否需要比较文件行数对性能有一定损耗 0 不稽核 其他稽核-->
	<param name="BatchTime"></param> <!-- 生成批次号的时间间隔，分钟-->
	<param name="KpiDelayMs"></param>
	<param name="KpiPortocol"></param>
	<param name="KpiFlag"></param>
	<param name="KpiAddr"></param>
	<param name="KpiLatn"></param>
	<param name="getfileway">2</param> <!--取文件的方式，1：从服务器上取，2：根据查表的路径-->
	<gray><!--灰度路由-->
	<param name="Subscriber">MsgExch</param> <!--灰度路由订阅名-->
	<param name="RouteACCTProcess">ToAcct</param> <!--灰度路由下游环节-->
	<param name="RouteCTGProcess">ToCTG</param> <!--灰度路由下游环节-->
	<param name="RouteRATEProcess">ToRate</param> <!--灰度路由下游环节-->
	<param name="GrayRefreshIntr">60</param> <!--灰度路由表数据刷新间隔-->	
    </gray>
  </MsgExch>
</configuration>
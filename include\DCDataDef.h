﻿#ifndef _DCDATA_DEF_H_
#define _DCDATA_DEF_H_

#include <pthread.h>
#include <sys/syscall.h> 
#include <time.h>
#include <sys/time.h>
#include <stdio.h>
using namespace std;

#define gettid() syscall(__NR_gettid)

#define SAFE_DELETE_PTR(X) \
    if(NULL != (X)){delete (X); (X)=NULL;}


struct MsgObj
{
	int  result;
	int	 line;
	long us_start;
 	long us_send;
 	long us_recv;
	char sessionid[128];    //sessionid
	char body[4096];	    //消息体
};


struct STTask
{
	long task_id;
    long source_id;
    long parent_source_id;
    long oper_list_id;
    long switch_id;
    long collect_id;
	long lines;
    int  latn_id;
	char oper_type[8];
	char source_name[256];
	char bill_path[256];
	char proc_id[2];
	char batch_id[15];
	char fileType [32];
    char breakIp[65];
	int  iFilewayMode;
};

struct STLogRating
{
	long task_id;
	long source_id;
	long parent_source_id;
	int  latn_id;
    long normal_nr;
    long invalid_nr;
    long abnormal_nr;
    long nouser_nr;
    long dual_nr;
    long charge_nr;
    long ori_charge;
    long disct_charge;
    char begin_time[16];
    char end_time[16];

    long total_nr;
    long send_nr;
    long recv_nr;
    long timeout_nr;

    char topic[64];
    
    bool is_timeout;
};
struct SplitInfo
{
    char splitChar;
    int index[3];
};
#endif

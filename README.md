# 灰度路由系统文档

## 项目简介
灰度路由系统(DCGrayscaleRoute)是一个分布式计费系统的灰度发布路由组件，用于根据配置的灰度规则将业务请求分发到不同的服务实例上。系统支持按照电话号码、区域、用户组等多种因素进行灰度路由控制，实现业务请求的精确分流和服务实例的无缝切换。

## 主要功能
- 根据预设的灰度规则获取业务服务名称
- 支持多种灰度分流策略（电话号码匹配、号段匹配、用户组匹配、区域匹配等）
- 跨集群数据加载和同步（主/备数据切换）
- 灰度场景版本管理和版本比较
- 灰度路由环境配置和管理
- 获取DCF客户端实例用于消息传递
- 支持灰度条件判断和复杂逻辑组合

## 系统架构
灰度路由系统采用单例模式设计，主要由以下部分组成：
1. **核心路由引擎**：负责根据灰度规则进行路由决策
2. **数据管理模块**：加载和管理灰度规则数据
3. **DCF客户端管理**：维护与各环境通信的DCF客户端
4. **条件分析引擎**：解析和评估灰度条件

系统支持主备数据切换机制，确保在数据更新期间服务持续可用。

## 关键组件介绍

### DCGrayscaleRoute组件
DCGrayscaleRoute是系统的核心组件，实现了DCGrayRoute接口，提供以下主要功能：

1. **初始化与数据加载**：
   - 加载灰度路由规则和用户组配置
   - 加载跨集群灰度场景、规则、条件和环境配置

2. **路由规则分析**：
   - 基于树状结构的路由规则分析
   - 条件匹配与评估

3. **DCF客户端管理**：
   - 创建和管理与各环境通信的DCF客户端
   - 支持按需初始化客户端

### DCFLocalClient组件使用说明
本项目中使用DCFLocalClient组件（DCFLocalClientNew版本）作为分布式通信框架的客户端，用于与服务端进行通信。主要功能包括：

1. 建立与ZooKeeper的连接，获取服务注册信息
2. 向指定服务发送消息
3. 接收服务响应消息
4. 管理消息的序列化、加密和传输

#### 使用流程
在灰度路由系统中，DCFLocalClient的使用流程如下：

1. 在`LoadEnvData`函数中加载环境配置并初始化DCFLocalClient实例
2. 使用以下方法进行DCFLocalClient初始化：
   - `setAESPassword`: 设置AES加密密码
   - `setZookAddrAndRootDir`: 设置ZooKeeper地址和根目录
   - `setInitWait`和`setInitWaitTimeoutMills`: 设置初始化等待参数
   - `start`: 启动客户端
3. 通过`GetRouteServiceNameDCFClient`函数获取特定环境对应的DCFLocalClient实例
4. 使用获取到的DCFLocalClient实例发送消息
5. 在系统关闭时，使用`shutdown`方法关闭DCFLocalClient客户端，再释放资源

#### 注意事项
1. 初始化DCFLocalClient时必须使用正确的顺序：先设置参数，再调用`start`方法
2. 销毁DCFLocalClient实例前必须调用`shutdown`方法进行清理
3. 每个环境配置对应一个DCFLocalClient实例，在内存中缓存复用
4. 在环境配置变更时，需要重新创建DCFLocalClient实例
5. 系统会自动管理DCF客户端的生命周期，防止内存泄漏

## 灰度路由使用示例
```cpp
// 1. 创建灰度路由实例
DCGrayscaleRoute* pGrayscaleRoute = DCGrayscaleRoute::instance();

// 2. 初始化路由实例
pGrayscaleRoute->init(pDBManager, "SUBSCRIBER_CODE");

// 3. 设置路由参数
map<string, string> params;
params["BillingNum"] = "13888888888";  // 计费号码
params["LatnID"] = "10";               // 本地网ID
params["OperType"] = "CREATE";         // 操作类型
// 其他可选参数：SectionNum(号段)、ACCTID(账户ID)、CUSTID(客户ID)等

// 4. 获取服务名称(不含DCF客户端)
string serviceName;
int ret = pGrayscaleRoute->GetRouteServiceName("MyProcess", params, serviceName);
if (ret == 0) {
    // 使用服务名称进行后续处理
    cout << "Service Name: " << serviceName << endl;
}

// 5. 获取服务名称和DCF客户端
DCFLocalClient* pDCFClient = NULL;
ret = pGrayscaleRoute->GetRouteServiceNameDCFClient("MyProcess", params, serviceName, pDCFClient);
if (ret == 0 && pDCFClient != NULL) {
    // 使用DCF客户端发送消息
    string uuid = "msg_" + to_string(time(NULL));
    string content = "Hello, Service!";
    pDCFClient->sendMsg(serviceName, uuid, content);
}

// 6. 获取路由环境配置
vector<St_route_env> routeEnvs;
ret = pGrayscaleRoute->GetRouteEnvs("MyProcess", routeEnvs);
if (ret == 0) {
    // 处理环境配置信息
    for (auto env : routeEnvs) {
        cout << "Env Code: " << env.env_code << endl;
    }
}
```

## 数据结构
系统中的主要数据结构包括：

### 基础数据结构
- `St_route_service_cfg`: 路由服务配置
  - 包含路由规则的ID、父ID、优先级、路由码类型、路由值范围、服务名称等

- `St_grey_user_group_cfg`: 灰度用户组配置
  - 包含组ID、用户标识(IMSI/号码/账户ID)等信息

- `St_grayscale_route_node`: 灰度路由树节点
  - 用于构建路由规则树，支持基于优先级的子节点排序

- `St_grayscale_route_tree`: 灰度路由树
  - 存储路由规则的树结构，支持多棵树并存

### 跨集群数据结构
- `St_route_scene`: 灰度场景配置
  - 包含场景ID、名称、版本、状态等信息

- `St_route_rule`: 灰度规则配置
  - 包含规则ID、名称、场景ID、环境编码、服务路径等

- `St_route_condition`: 灰度条件配置
  - 包含条件ID、规则ID、路由类型、操作符、条件值等

- `St_route_type`: 灰度类型定义
  - 包含路由类型ID、编码、名称等

- `St_route_env`: 灰度环境配置
  - 包含环境编码、地址、路径、用户凭据及DCF客户端等

## 数据库表
系统依赖的数据库表包括：

- `route_service_cfg`: 路由服务配置表
  - 存储基础路由规则信息

- `grey_user_group_cfg`: 灰度用户组配置表
  - 存储用户分组信息

- `dcf_route_scene_his`: 灰度场景历史表
  - 存储跨集群灰度场景信息

- `dcf_route_rule_his`: 灰度规则历史表
  - 存储跨集群灰度规则信息

- `dcf_route_condition_his`: 灰度条件历史表
  - 存储跨集群灰度条件信息

- `dcf_route_type`: 灰度类型表
  - 存储灰度路由类型定义

- `dcf_route_env`: 灰度环境表
  - 存储灰度环境配置信息

- `dcf_route_refresh_log`: 灰度刷新日志表
  - 记录灰度规则刷新结果

## 错误码说明
- `ERR_NOT_FIND_SERVICE_NAME (1000)`: 获取服务名称失败
- `ERR_NOT_FIND_RECORD (1001)`: 找不到数据
- `ERR_NOT_FIND_SQL (-1002)`: 找不到SQL
- `ERR_NOT_FIND_INMAPPARAM (-1003)`: 输入参数为空

## 路由码定义
- `BillingNum`: 计费号码
- `SectionNum`: 号段
- `LatnID`: 本地网
- `OperType`: 业务类型
- `ACCTID`: 账户ID
- `CUSTID`: 客户ID
- `OFFERINSTID`: 销售品实例ID
- `OFFERID`: 销售品ID
- `Imsi`: IMSI号

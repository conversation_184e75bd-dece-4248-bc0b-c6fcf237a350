#ifndef __DCOprStruct_NEW_H__
#define __DCOprStruct_NEW_H__
#include "DCDBManer.h"
#include <vector>
#include <map>

typedef struct
{
    int col;
    std::string name;
    int offset;
    int len;
}STOprColInfo;

class DCOprStruct
{
public:
    DCOprStruct();

    ~DCOprStruct();

    int InitParam(DCDBManer*);

    int SetOperListId(int operListId);

    int SetBuff(std::string buff);

    void ClearBuffer();

public://override
    const char* GetCurrValue(const char* vi_sFieldCode);

    int SetCurrValue(const char* vi_sFieldCode, const char* vi_sValue);

    std::string GetBuff();

    void ShowAllCols();

private:
    int GetColInfo(const char *vi_sKey, STOprColInfo& info);

private:
    int m_iOperListId;
    int m_iSize;
    std::string m_buff;
    std::string m_curStr;
    std::map<std::string, STOprColInfo> m_mapName2ColInfo;
	time_t m_checktime;

private:
	DCDBManer* m_dbm;
    UDBSQL* m_acQuery;
};
#endif


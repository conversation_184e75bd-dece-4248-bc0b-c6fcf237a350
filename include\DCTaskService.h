﻿#ifndef __DCTASKSERVICE_H__
#define __DCTASKSERVICE_H__

#include "DCTQueue.h"
#include "DCDBManer.h"
#include "DCFile.h"
#include "DCSeriaOp.h"
#include "UFmtMsg.h"
#include "DCFLocalClientNew.h"
#include "DCMon.h"
#include "DCLogMacro.h"
#include "DCPairList.h"
#include "DCDataDef.h"
#include "DCExchCfg.h"
#include "DCPerfStatistic.h"
#include "DCOprStruct.h"
#include <ace/Task.h>
#include <ace/Thread_Mutex.h>
#include <vector>
#include <stdio.h>
#include <list>
#include <map>
#include "DCGrayscaleRoute.h"



class DCTaskService : public ACE_Task_Base
{
public:
	DCTaskService();
	~DCTaskService();

	int GetTableId(long lnOperListId, int &iTableId);
	string GetBatchId(long lnOperListId);

	int init(DCDBManer* dmdb, dcf_new::DCFLocalClient* client,dcf_new::DCFLocalClient *clientbill,DCPairList *pairlist, DCMon *screen[],int threadNo);

	int run();

	virtual int svc();

	static void state(int done);

	static void queueState(int queueState);

	bool exit();

	int getSize()
	{
		return m_pairList->size();
	}

private:
	static int GetTask(DCDBManer *dmdb,STTask& task);
	static int GetTaskQFirst(DCDBManer *dmdb,int v_ilimitnum);

    int SplitString(const char* pszStr, const char cSeparator, std::vector<std::string>& vecStr);
    int GetBreakPointFile(string strRemoteIp, long lnTaskId, long lnSourceId);
    int UpdateBreakPointIp(long lnTaskId, long lnSourceId);
	int DoTask(const STTask& task);

	int DealAnsMsg(const MsgObj& obj);

	const char* GenUnifiledSeq(long sourceid,int lineno);
	void        GenUnifiledLogSeq(char *v_date);
	int         ClearTask(DCDBManer *dmdb);
	std::string GetBillingNbrOpr(string operType, string buff);
	int         SetOperListId(long oper_list_id);
	std::string GetBillingNbrSplit(string operType, string buff);
	int         SetSplitInfo(int switchId);
	int         InitSplit();
	std::string GetBillingNbrCAN(string operType, string buff);
	int GetGrayServName(ocs::UFmtHead& head,string& serviceBILL, string sRouteProcess, dcf_new::DCFLocalClient *pClient=NULL);

	int         SpecialBackUp();

private:
	static int                sm_nDone;
	static int                m_nQueueState;
	static time_t             sm_checktime;
	bool                      m_bexit;
	STTask*                   m_task;    
	std::string               m_szLocalPath;
	long                      m_tseq;
	char                      m_sseq[64];
	static DCTQueue<STTask>   sm_qTask;
	static ACE_Thread_Mutex   sm_aceMutex;
	STLogRating               m_lograte;
	DCTQueue<MsgObj>          m_queue;
	DCDBManer*                m_dmdb;
	DCExchDB*                 m_exchdb;
	DCFile*                   m_file;    
	DCFLocalClient*           m_client;
	DCFLocalClient*           m_clientbill;
	DCPairList*               m_pairList;
	DCSeriaEncoder            m_en;        
	DCSeriaPrinter            m_print;
	DCMon*                    m_gth;
	DCMon*                    m_screen;
	DCMon*                    m_cdr;
	DCMon*                    m_statFiles;
	std::map <int,long>       m_latnlist;
	time_t                    m_checktime;
	SExchCfg*                 m_cfg;
	DCPerfTimeStats               m_tstat;         //性能统计
	int                       m_nThreadNO;
	long                      m_logSeq;
	DCOprStruct*              m_oprStruct;
	SplitInfo                 m_info;
    string                    m_strHostIp;
	vector<string>            m_vecFileType;
	long                      m_lSuccessNum;   //成功条数
};

#endif // __DCTASKSERVICE_H__

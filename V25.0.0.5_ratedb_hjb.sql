/*新增表*/

CREATE TABLE `dcf_route_scene_his`  (
  `his_scene_id` bigint(12) NOT NULL AUTO_INCREMENT COMMENT '历史标识',
  `scene_id` int(6) NOT NULL COMMENT '主键, 场景ID',
  `scene_name` varchar(255)  NOT NULL COMMENT '场景名称',
  `version` varchar(50)  DEFAULT NULL COMMENT '版本号',
  `center_id` int(9) NOT NULL COMMENT '中心ID，对应dcf_center.center_id',
  `client_code` varchar(255)  NOT NULL COMMENT '客户端编码',
  `app_type` varchar(10)  NOT NULL COMMENT 'service:服务型 dcf:密框型',
  `env_type` varchar(60)  NOT NULL COMMENT 'physical：物理机集群 cloud：研发云 hybrid：集群+研发云',
  `upgrade_type` varchar(60)  NOT NULL COMMENT 'canary金丝雀、bluegreen蓝绿、roll滚动',
  `flow_json` varchar(4000)  NOT NULL COMMENT '存储流程图json报文',
  `desc` varchar(4000)  DEFAULT NULL COMMENT '场景说明',
  `status` int(2) NULL DEFAULT 1 COMMENT '状态 1：有效 0：无效',
  `approval_status` int(2) NOT NULL COMMENT '0草稿 1待审批、2审批通过、3审批不通过 4已下发 -1下发失败',
  `create_staff` varchar(60)  NOT NULL COMMENT '创建人工号',
  `create_date` datetime NOT NULL COMMENT '创建时间，格式:YYYY-MM-DD HH:MM:SS',
  `approver` varchar(60)  DEFAULT NULL COMMENT '审核人工号',
  `approval_date` datetime DEFAULT NULL COMMENT '审核时间，格式:YYYY-MM-DD HH:MM:SS',
  `is_sms_notify` int(1) DEFAULT NULL COMMENT '是否短信通知审核人 1:是  0:否',
  `operator_staff` varchar(60)  DEFAULT NULL COMMENT '操作人工号',
  `operator_date` datetime DEFAULT NULL COMMENT '操作时间，格式:YYYY-MM-DD HH:MM:SS',
  PRIMARY KEY (`his_scene_id`) ,
  INDEX `index_scene_id`(`scene_id`) 
) ENGINE = InnoDB CHARACTER SET = utf8  COMMENT='路由场景历史表';

CREATE TABLE `dcf_route_rule_his`  (
  `his_rule_id` int(6) NOT NULL AUTO_INCREMENT COMMENT '历史 ID',
  `rule_id` int(6) NOT NULL COMMENT '主键ID',
  `rule_name` varchar(255)  NOT NULL COMMENT '路由规则名称',
  `version` varchar(50)  DEFAULT NULL COMMENT '版本号',
  `scene_id` int(6) NOT NULL COMMENT '场景ID',
  `env_code` varchar(60)  NOT NULL COMMENT '关联环境定义表，密框型为zk集群，服务型为http地址',
  `dispatch_path` varchar(255)  DEFAULT NULL COMMENT '转发路径，密框型应用配置目标服务名，服务型应用配置转派路径',
  `client_code` varchar(60)  DEFAULT NULL COMMENT '客户端程序名，密框配置客户端程序编码',
  `topology_name` varchar(60)  DEFAULT NULL COMMENT 'topology图名，密框应用图名',
  `priority` int(2) NOT NULL DEFAULT 5 COMMENT '优先级',
  PRIMARY KEY (`his_rule_id`) ,
  INDEX `index_scene_id`(`scene_id`) 
) ENGINE = InnoDB CHARACTER SET = utf8 COMMENT='路由规则历史表' ;

CREATE TABLE `dcf_route_condition_his`  (
  `his_cond_id` int(6) NOT NULL AUTO_INCREMENT COMMENT '历史条件 ID',
  `cond_id` int(9) NOT NULL COMMENT '条件 ID',
  `rule_id` int(9) NOT NULL COMMENT '规则ID',
  `version` varchar(50)  DEFAULT NULL COMMENT '版本号',
  `group_id` int(2) NOT NULL COMMENT '分组ID,同组“且”、异组“或”',
  `route_type_id` varchar(60)  NOT NULL COMMENT '路由类型ID，关联dcf_route_type.route_type_id',
  `operator` varchar(20)  NOT NULL COMMENT '条件判断的操作符，如 \"=\"、\"LIKE\"、\"!=\"、\"NOT LIKE\"',
  `value` varchar(4000)  NOT NULL COMMENT '条件判断的值，如 \"551\"、\"180*\"',
  `desc` varchar(255)  DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (`his_cond_id`) ,
  INDEX `index_rule_id`(`rule_id`) 
) ENGINE = InnoDB CHARACTER SET = utf8 COMMENT='路由条件历史表';

CREATE TABLE `dcf_route_type`  (
  `route_type_id` int(6) NOT NULL AUTO_INCREMENT COMMENT '主键ID，路由类型ID',
  `route_type_code` varchar(60)  NOT NULL COMMENT '路由类型编码,如latn_id',
  `route_type_name` varchar(255)  NOT NULL COMMENT '路由类型名称, 如本地网',
  `center_id` int(11) NOT NULL COMMENT '中心ID，对应dcf_center.center_id',
  PRIMARY KEY (`route_type_id`) 
) ENGINE = InnoDB CHARACTER SET = utf8  COMMENT='路由类型表';

CREATE TABLE `dcf_route_env`  (
  `env_id` int(6) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `center_id` int(9) NOT NULL COMMENT '中心ID，对应dcf_center.center_id',
  `env_code` varchar(60)  NOT NULL COMMENT '环境编码',
  `env_type` varchar(60)  NOT NULL COMMENT '环境类型，physical：物理机集群 cloud：研发云集群',
  `cluster_code` varchar(255)  NOT NULL COMMENT '集群编码，物理机集群：基础平台集群编码，研发云集群：研发云集群编码',
  `env_name` varchar(60)  NOT NULL COMMENT '环境名称',
  `app_type` varchar(60)  NOT NULL COMMENT '应用类型，service:服务型 dcf:密框型',
  `env_addr` varchar(255)  NOT NULL COMMENT '环境地址，服务型路由环境地址：http://ip:port 密框型应用的zk地址：ip:port/zkroot',
  `env_path` varchar(255)  DEFAULT NULL COMMENT '环境路径，密框应用配置zk root',
  `user_name` varchar(255)  DEFAULT NULL COMMENT 'zk认证用户名',
  `password` varchar(255)  DEFAULT NULL COMMENT 'zk认证密码',
  `create_staff` varchar(60)  NOT NULL COMMENT '创建人工号',
  `create_date` datetime NOT NULL COMMENT '创建时间,格式：YYYY-MM-DD HH:MM:SS',
  `update_staff` varchar(60)  NOT NULL COMMENT '修改人工号',
  `update_date` datetime NOT NULL COMMENT '修改时间，格式:YYYY-MM-DD HH:MM:SS',
  PRIMARY KEY (`env_id`) 
) ENGINE = InnoDB CHARACTER SET = utf8  COMMENT='环境定义表';

CREATE TABLE `dcf_route_refresh_log` (
  `log_id` bigint(12) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `refresh_time` varchar(255) NOT NULL COMMENT '刷新时间，格式:YYYY-MM-DD HH:MM:SS',
  `center_id` varchar(255) NOT NULL COMMENT '中心ID，可能包含多个中心ID，以逗号分隔',
  `module_name` varchar(255) NOT NULL COMMENT '模块名称',
  `pid` varchar(60) NOT NULL COMMENT '进程ID',
  `change_info` varchar(4000) DEFAULT NULL COMMENT '变更信息',
  `success` varchar(20) NOT NULL COMMENT '刷新状态，success或fail',
  PRIMARY KEY (`log_id`),
  INDEX `idx_refresh_time` (`refresh_time`),
  INDEX `idx_center_id` (`center_id`)
) ENGINE = InnoDB CHARACTER SET = utf8 COMMENT='路由刷新日志表';

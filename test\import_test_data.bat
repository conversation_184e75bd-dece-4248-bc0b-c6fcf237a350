@echo off
echo 正在导入灰度路由系统测试数据...

REM 设置MySQL连接信息，请根据实际情况修改
set MYSQL_HOST=***************
set MYSQL_PORT=8892
set MYSQL_USER=dcbp
set MYSQL_PASS=dcbp123!@#
set MYSQL_DB=DCBP_DEV

REM 导入测试数据
echo 导入新版表测试数据...
mysql -h%MYSQL_HOST% -P%MYSQL_PORT% -u%MYSQL_USER% -p%MYSQL_PASS% %MYSQL_DB% < GrayscaleRoute_test_data.sql

echo 导入旧版表测试数据...
mysql -h%MYSQL_HOST% -P%MYSQL_PORT% -u%MYSQL_USER% -p%MYSQL_PASS% %MYSQL_DB% < GrayscaleRoute_legacy_test_data.sql

echo 测试数据导入完成！
echo 使用以下命令测试灰度路由系统:
echo demo.exe GrayscaleRoute.sql.xml 127.0.0.1:9081 SUBSCRIBER ToRate
pause

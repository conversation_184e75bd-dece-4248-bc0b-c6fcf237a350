<?xml version="1.0" encoding="utf-8"?>

<dcsql>
  <!--db 属性字段解释-->
  <!--字段name：     必选  db实例名字-->
  <!--字段category： 必选  数据库实现名字，与imp.category对应-->
  <!--字段env：      可选  数据库实现的附加参数-->
  <!--字段master：   必选  主连接串-->
  <!--字段standby：  可选  备连接串，可不配置-->
  <!--module 属性字段解释-->
  <!--字段name：     必选  module名字 -->
  <!--字段db：       必选  module里面的sql所用的dbinst名字 -->
  <!--字段policy：   必选  SQL加载策略：must 启动时初始化sql, demand 使用时初始化sql, none 不使用 -->
  <!--sql 属性字段解释-->
  <!--字段name：     必选  sql名字，若存在sub, 则对应名字为 "name|sub" -->
  <!--字段bind:      必选  绑定参数串，每一位代表一个绑定参数类型，1-int, 2-long, 3-char*, 4-blob -->
  <!--字段sub:       可选  sql子索引，以逗号分割，针对会话表、累帐表等场景，将sqltext中的"[@]"依次替换成子索引以获取多个sql语句 -->
  <data>
    <dbimp>
      <imp category="UDBDRIVER" version="1.0.0">libdriverutil.so</imp>
      <imp category="UDCA" version="2.0.0">libdcautil.so</imp>
      <imp category="UM2DB" version="1.0.0">libm2dbutil.so</imp>
    </dbimp>
    <dbinst>
      <db name="ORA_Main" category="UDBDRIVER">
        <env>CONTYPE=1;DBTYPE=6;DBMAX=100;DRV=libmydrv.so</env>
        <master>odbc:mysql//*************:8901/DUCC_BILL_SX?user=billuser&amp;password=1VBZ^0t#xi</master>
      </db>
      <db name="dca" category="UDCA">
        <env>FMT=string;SPLIT=^</env>
        <master>IP=*************;PORT=17022;UUID=TEST;ACCTID=tydic;USER=test;PWD=123456</master>
      </db>
      <db name="fmt_m2db" category="UM2DB">
        <master>0</master>
      </db>
    </dbinst>
  </data>
  <app>
    <module name="qry_serv_inst" db="dca" policy="must">
      <sql name="QueryRedisKey" bind="3" sub="">select cache@1 from CACHE.CHECKUID on key(uid=?)</sql>
      <sql name="DeleteRedisKey" bind="3" sub="">delete from CACHE.CHECKUID on key(uid=?)</sql>
    </module>
    <module name="msgexch" db="ORA_Main" policy="must">
	  <sql name="UpdateTaskToDupRecord" bind="322">UPDATE /*[sqlId:UpdateTaskToDupRecord,appName:msgexch,Mode:SC]*/ task_manager_msgexch a SET a.pid = ?, a.handle_time = sysdate(), a.task_state = '4' WHERE a.task_state = '0' AND a.source_id = ? AND a.task_id = ?</sql>
      <sql name="MarkTaskToDup" bind="32">update /*[sqlId:MarkTaskToDup,appName:msgexch,Mode:SC]*/ task_manager_msgexch set task_state=? where task_id=?</sql>
      <sql name="SpecialBackUpAudit" bind="332322">insert /*[sqlId:SpecialBackUpAudit,appName:msgexch,Mode:SC]*/ into special_back_up_audit(File_PATH,FILE_NAME,create_time,Update_time,SOURCE_ID,BATCH_ID,Lines,Deal_flag,OperList_Id) values(?,?,sysdate(),sysdate(),?,?,?,0,?)</sql>
      <sql name="UpdateUidAll" bind="3222" sub="">update /*[sqlId:UpdateUidAll,appName:msgexch,Mode:SC]*/ tsk_bill_filerecord_audit set state = ?, TotalNum = ? where sourceid = ? and taskid = ? and state = "0"</sql>
      <sql name="QueryTask" bind="3">select /*[sqlId:sel_task_manager_msgexch,appName:msgexch,Mode:SC]*/ task_id, source_id, parent_source_id, oper_list_id, switch_id, collect_id, latn_id, oper_type, TASK_NAME, proc_list, batch_id, a.lines, break_ip, file_type from task_manager_msgexch a where task_state = '1' and latn_id = 919 and pid = ? order by source_id desc</sql>
      <sql name="UpdateTaskToDeal" bind="3">UPDATE /*[sqlId:upd_deal_task_manager_msgexch,appName:msgexch,Mode:SC]*/ task_manager_msgexch a SET a.pid =?, a.handle_time = sysdate(), a.task_state = '1' WHERE a.task_state = '0' and a.source_id IN ( SELECT b.source_id FROM ( SELECT c.source_id FROM task_manager_msgexch c WHERE c.task_state = '0' AND c.latn_id = 919 and task_name not like '%rollback%' and task_name not like '%CALLBACK%' and task_name not like '%CALLMISS%' ORDER BY c.source_id DESC ) b ) limit 50</sql>
      <sql name="UpdateTaskState" bind="2">update /*[sqlId:upd_state_task_manager_msgexch,appName:msgexch,Mode:SC]*/ task_manager_msgexch set task_state='9' where task_id=? and task_state='1'</sql>
      <sql name="UpdateTaskToExp" bind="2">update /*[sqlId:upd_exp_task_manager_msgexch,appName:msgexch,Mode:SC]*/ task_manager_msgexch set task_state='2' where task_id=?</sql>
      <sql name="DeleteTask" bind="2">delete /*[sqlId:del_task_manager_msgexch,appName:msgexch,Mode:SC]*/ from task_manager_msgexch where task_id=?</sql>
      <sql name="UpdateFileState" bind="32">update /*[sqlId:udp_source_files,appName:msgexch,Mode:SC]*/ source_files set source_state=? where source_id=?</sql>
      <sql name="InSertEptRecord" bind="233313" sub="919">insert /*[sqlId:ins_DAT_MISS,appName:msgexch,Mode:SC]*/  into DAT_MISS_[@](SOURCEID,UUID,procer_id,state,is_sum,mistype, billing_nbr) values(?,?,?,?,?,0,?)</sql>
      <sql name="InSertUidAll" bind="222" sub="">insert /*[sqlId:ins_tsk_bill_filerecord_audit,appName:msgexch,Mode:SC]*/ into tsk_bill_filerecord_audit(TotalNum,SOURCEID,State,UpdateDate,ProcessId,ResultCode,MisNum,SameNum,ChgAuditFileNum,taskid) values(?,?,"0",sysdate(),0,NULL,0,0,0,?)</sql>
      <sql name="UpdateTaskSum" bind="33" sub="919">update /*[sqlId:udp_charge_files_detail,appName:msgexch,Mode:SC]*/ charge_files_detail_[@] set ptid=?, Deal_state = 'M0D' where Deal_state = 'M0C' and dealing_ip = ? limit 200</sql>
      <sql name="QueryInterceptTime" bind="1">select Intercept_beginTim from tb_bil_sum_batch_intercept_cfg where Is_valid = 1 and Latn_id=?</sql>
      <sql name="UpdateIntercetpTaskSum" bind="3" sub="">update /*[sqlId:QueryTableId,appName:msgexch,Mode:SC]*/ charge_files_detail_[@] set Deal_state = 'M0D' where Deal_state = 'M0I' and dealing_ip = ?</sql>     <sql name="QueryTaskQFirst" bind="1">SELECT /*[sqlId:sel_qfirst_task_manager_msgexch,appName:msgexch,Mode:SC]*/ task_id, source_id, parent_source_id, oper_list_id, switch_id, collect_id, latn_id, oper_type, TASK_NAME, proc_list, batch_id, a.LINES, break_ip, file_type FROM task_manager_msgexch a WHERE a.task_state = '0' AND a.latn_id = 919 AND a.task_name NOT LIKE '%rollback%' AND a.task_name NOT LIKE '%CALLBACK%' AND a.task_name NOT LIKE '%CALLMISS%' AND a.task_path NOT LIKE '%/temp11/%' ORDER BY a.source_id ASC LIMIT ?</sql>
	  <sql name="InsertChargeFiles" bind="2333133122222213" sub="919">insert /*[sqlId:ins_charge_files,appName:msgexch,Mode:SC]*/ into charge_files_[@](charge_files_id,batch_id,Ori_file_path,Ori_file_name,Ori_records,File_path,file_name,Records,ori_charge,Disct_charge,duration,flux,Counts,calls,billing_cycle_id,table_code,Create_date) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,sysdate())</sql>
      <sql name="InsertChargeFilesDetails" bind="23331133" sub="919">insert /*[sqlId:ins_charge_files_detail,appName:msgexch,Mode:SC]*/ into charge_files_detail_[@](charge_files_id,batch_id,File_path,file_name,latn_id,billing_cycle_id,Create_date,Deal_state,PTID,dealing_ip) values(?,?,?,?,?,?,sysdate(),?,NULL,?)</sql>
	  <sql name="InsertChargeFilesRealTime" bind="333122222213" sub="919">insert /*[sqlId:ins_realtime_charge_files,appName:msgexch,Mode:SC]*/ into charge_files_[@](charge_files_id,batch_id,Ori_file_path,Ori_file_name,Ori_records,ori_charge,Disct_charge,duration,flux,Counts,calls,billing_cycle_id,table_code,Create_date) values(nextval('charge_files_id'),?,?,?,?,?,?,?,?,?,?,?,?,sysdate())</sql>
      <sql name="getChargeFilesId" bind="">select nextval('charge_files_id')</sql>
     <sql name="QueryTaskSum" bind="33" sub="919">select /*[sqlId:sel_charge_files_detail,appName:msgexch,Mode:SC]*/ charge_files_id, batch_id, File_path, file_name, Latn_id, billing_cycle_id from charge_files_detail_[@] where Deal_state = 'M0D' and ptid = ? and dealing_ip = ?</sql>
	 <sql name="UpdateFileStateSum" bind="3233" sub="919">update /*[sqlId:upd_charge_files_detail,appName:msgexch,Mode:SC]*/ charge_files_detail_[@] set Deal_state=? where charge_files_id=? and batch_id=? and Deal_state = ?</sql>
      <sql name="DeleteTaskSum" bind="23" sub="919">delete /*[sqlId:del_charge_files_detail,appName:msgexch,Mode:SC]*/ from charge_files_detail_[@] where charge_files_id=? and batch_id=?</sql>
      <sql name="ClearTask" bind="3">update /*[sqlId:upd_task_manager_msgexch,appName:msgexch,Mode:SC]*/ task_manager_msgexch set pid=NULL, task_state='0' where pid=? and task_state='1' and latn_id = 919</sql>
      <sql name="refreshOperList" bind="">select /*[sqlId:sel_PAR_OPER_LIST,appName:msgexch,Mode:SC]*/ oper_list_id,table_code from PAR_OPER_LIST</sql>
      <sql name="UpdateTaskToDealQFirst" bind="322">UPDATE /*[sqlId:udp_qfirst_task_manager_msgexch,appName:msgexch,Mode:SC]*/ task_manager_msgexch a SET a.pid = ?, a.handle_time = sysdate(), a.task_state = '1' WHERE a.task_state = '0' AND a.source_id = ? AND a.task_id = ?</sql>
      <sql name="PARSPLIT" bind="1">SELECT /*[sqlId:sel_PAR_SWITCH_SPLIT,appName:msgexch,Mode:SC]*/ A .element_code, A .col_index, b.SPLIT_FLAG FROM par_switch_split_second A, PAR_SWITCH_SPLIT_FIRST b WHERE A .element_code IN ('OID', 'CAN', 'CER') AND A .SWITCH_TYPE_ID = b.SWITCH_TYPE_ID AND b.SWITCH_TYPE_ID = ?</sql>
      <sql name="UpdateBreakPointIp" bind="322">update task_manager_msgexch set break_ip=? where task_id=? and source_id=?</sql>
      <sql name="UpdateUidAll" bind="1222" sub="">update /*[sqlId:UpdateUidAll,appName:msgexch,Mode:SC]*/ tsk_bill_filerecord_audit set state = ?, TotalNum = ? where sourceid = ? and taskid = ? and state = "0</sql>
      <sql name="CountUndoTaskSum" bind="3" sub="919">select /*[sqlId:CountUndoTaskSum,appName:msgexch,Mode:SC]*/ count(*) from charge_files_detail_[@] where Deal_state = 'M0C' and dealing_ip = ?</sql>
   </module>
    <module name="msg_m2db" db="fmt_m2db" policy="must">
      <sql name="QueryTableId" bind="2">select tableID from tb_par_oper_list on index_tb_par_oper_list(oper_list_id=?)</sql>
      <sql name="PAROPRSTRUCT" bind="11">select record_type, offset, len, format, dual, load, col_no, tmp, record_id from par_oper_struct on indbilopr(oper_list_id=?, version=?)</sql>
      <sql name="PARSPLITFIRST" bind="">select switch_type_id, switch_file_type,switch_id from par_switch_info</sql>
      <sql name="PARCROSSACCT" bind="2" dyn="AB">select mod_group_id from tb_bil_cross_account_[@A] on index_bil_cross_account_[@B](acct_id=?)</sql><!--sum文件部分修正mod_group_id-->
      <sql name="ParTraceNumberQuery" bind="3">select trace_num from par_trace_number on idx_par_trace_number(trace_num=?)</sql><!--检查是否配置号码追踪-->
    </module>
  </app>
</dcsql>
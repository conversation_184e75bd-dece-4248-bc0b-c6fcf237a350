#ifndef __DCEXCHCFG_H__
#define __DCEXCHCFG_H__

#include "DCDBManer.h"
#include "DCDataDef.h"
#include <map>
#include <vector>
#include <ace/Task.h>
#include <ace/Thread_Mutex.h>
#include "DCKpiSender.h"
#include "DCGrayscaleRoute.h"
#include <math.h> // 包含 fabs 函数的头文件


class DCPairList;

using namespace std;

enum EActiveConfig
{
	ACTIVE_CONFIG_NULL = 0,
	ACTIVE_CONFIG_MASTER,
	ACTIVE_CONFIG_SLAVE
};



struct SExchCfg
{ 
	int nRunmode;

	int servLogLevel;
	int deleteflag;
	int iDelCheckUidFlag;
	int sendtype;
	int FlowControlSize;	
	int BlockQueueSize;
	int FlowTimeOut;
	int recordLogRatingSwitch;
		
	long lsendMsgCount;
	long sleeptime;

	string logserv;
	int    loglevel;
	int    perf_level;
	int    perf_threshold;

    string zkAddr;	
    string serviceCTPC;	
	string zkRoot;
	string zkRootQueue;
	
    string zkAddrBILL;
    string serviceBILL;		
	string serviceBILLBigAcct;
	string zkRootBILL;
	string zkRootQueueBILL;
	
	string loginpwd;
	string aespwd;

	string fileServ;
	string filepath;

	string addr;
	int    delay;
	int    portocol;
	string host;

	int         printRate;
	vector<int> vdelay;

	int threadnum;
	int worker_num;
	int nTreadNum;

	string brokers;
	string groupconsumer;
	string topic;
	string examinePath;
	int    examineSwitch;	

	string sqlConf;
	int    routeByNbr;
	string breakPointPath;

	string scan_dir;
	string bak_dir;
	string detail_dir;
	string error_dir;
	string sum_brk_dir;
	string Info_dir;

	string sumType;

	int    errorCodeTime;
	int    infoTimeDelay;
	string sum_reg;
	string errorCodeList;
	string breakIP;
	string password;
	int    isMerge;
	int    arrangeThreadnum;
	string staffId;
	string logCodePre;
    string operList;

	string bakFilePath;

	int    taskmode;
	int    tasklimitnum;
	int    filterFlag;
	string filterlist;

	long   lnTimeoutRange; //超时消息在timeoutRange时间内达到timeoutCount程序退出
	long   lnTimeoutCount;

	bool   bDCFInitWait;
	long   lnDCFInitWaitTimeout;
	int    nDCFWorkNumCTPC;
	int    nDCFWorkNumBILL;

	int    nSysStateFlag; // 程序运行状态 -- 0:正常处理 1：任务暂停
	int    nContinueFailExit; // 连续处理任务失败（包括文件下载失败、数据库操作失败等）达到一定次数退出
	int    nTraceNumFlag; // 号码追踪开关。0：关闭，1：打开，默认：0
	string sBackUpFileType; // 需要写备份表的 FILE_TYPE
	int    nCheckFileLines; // 是否需要核对文件行数
	int	   iGetFileWay;
	int    iBatchTime;      // 生成批次号的时间，单位为分钟
	//灰度发布
	string Subscriber;
	string RouteCTGProcess;
	string RouteRATEProcess;
	string RouteACCTProcess;
	int nRefreshIntr;
};

typedef std::map <int, SplitInfo> MapSplitInfo;

class DCExchCfg : public ACE_Task_Base
{
public:	
	static DCExchCfg* instance();	
	int Init(const char* path);
	void InitGrayscaleRoute(DCDBManer* dbManer, std::string strSubscriber);
	int InitGrayRouteDCFClients(const std::vector<std::string>& routeProcesses);
	SExchCfg* GetConfig();
    MapSplitInfo& GetMapSplitInfo() { return m_mapSplitInfo; }
    int InitSplitInfo(DCDBManer* dbManer);
	int SplitString(const char* pszStr, const char cSeparator, std::list<std::string>& vecStr);
	bool bDoubleEqualZero(double dValue);
	int run();

	// 设置和获取pairList指针的函数
	static void SetPairList(DCPairList* pairList);
	static DCPairList* GetPairList();

	virtual int svc();

private:
	DCExchCfg();

	~DCExchCfg();
	int UpdateConfig();
	
	int LoadCfg(SExchCfg *cfg);
private:	
	static DCExchCfg*	m_instance;
	static DCPairList*  m_pairList;   // 存储main函数中的pairList指针
	SExchCfg        m_exchCfg[2];
	EActiveConfig 	m_activeConfig;
	char 			m_configPath[128];
    MapSplitInfo    m_mapSplitInfo;
	time_t          m_checktime;
	DCDBManer* 		m_dbm;
};

#endif

<?xml version="1.0" encoding="ansi" ?>
<dcsql>
    <!--db 属性字段解释-->
    <!--字段name：             必选      db实例名字-->
    <!--字段category： 必选      数据库实现名字，与imp.category对应-->
    <!--字段env：              可选      数据库实现的附加参数-->
    <!--字段master：   必选      主连接串-->
    <!--字段standby：  可选      备连接串，可不配置 -->
    <!--module 属性字段解释-->
    <!--字段name：             必选      module名字 -->
    <!--字段db：               必选      module里面的sql所用的dbinst名字 -->
    <!--字段policy：   必选      SQL加载策略：must 启动时初始化sql, demand 使用时初始化sql, none 不使用 -->
    <!--sql 属性字段解释-->
    <!--字段name：             必选      sql名字，若存在sub, 则对应名字为 "name|sub" -->
    <!--字段bind:             必选      绑定参数串，每一位代表一个绑定参数类型，1-int, 2-long, 3-char*, 4-blob -->
    <!--字段sub:              可选      sql子索引，以逗号分割，针对会话表、累帐表等场景，将sqltext中的"[@]"依次替换成子索引以获取多个sql语句 -->
    <data>
        <dbimp>
            <imp category="UDBDRIVER" version="1.0.0">libdriverutil.so</imp>
        </dbimp>
        <dbinst>
            <db name="ocsmysql" category="UDBDRIVER" >                                                      
			<env>CONTYPE=1;DBTYPE=6;DBMAX=100;DRV=libmydrv.so</env> 
            <master>odbc:mysql://192.168.161.124:8892/DCBP_DEV?user=dcbp;password=dcbp123!@#</master>
		     </db>
        </dbinst>
    </data>
    <app>
        <module name="ocsmysql" db="ocsmysql" policy="must">
            <sql name="RouteServiceCfg" bind="" sub="">SELECT ID, ParentId, PRIORITY, Subscriber, RouteProcess, RouteCode, RouteValueA, RouteValueB, ServiceName, RouteStatus FROM route_service_cfg ORDER BY ParentId, PRIORITY DESC</sql>
            <sql name="GreyUserGroupCfg" bind="" sub="">SELECT GroupId, Value, RouteStatus FROM grey_user_group_cfg</sql>
            <sql name="RouteSceneHis" bind="3" sub="">SELECT scene_id, scene_name, version, center_id, app_type, status, approval_status, create_date FROM dcf_route_scene_his WHERE app_type='dcf' and center_id in(30001,30002) and status=1 and approval_status=2 ORDER BY scene_id, version</sql>
            <sql name="RouteRuleHis" bind="3" sub="">SELECT rule_id, rule_name, scene_id, env_code, dispatch_path, client_code, topology_name, priority, version FROM dcf_route_rule_his WHERE client_code=?</sql>
            <sql name="RouteConditionHis" bind="" sub="">SELECT cond_id, rule_id, group_id, route_type_id, operator, value, version FROM dcf_route_condition_his</sql>
            <sql name="RouteType" bind="3" sub="">SELECT route_type_id, route_type_code, route_type_name FROM dcf_route_type WHERE center_id IN(30001,30002)</sql>
            <sql name="RouteEnv" bind="3" sub="">SELECT env_code, env_addr, env_path, user_name, password FROM dcf_route_env WHERE center_id IN(30001,30002) AND app_type='dcf'</sql>
            <sql name="LogRefreshResult" bind="333333" sub="">INSERT INTO dcf_route_refresh_log(refresh_time, center_id, module_name, pid, change_info, success) VALUES(?, ?, ?, ?, ?, ?)</sql>
        </module>
    </app>
</dcsql>

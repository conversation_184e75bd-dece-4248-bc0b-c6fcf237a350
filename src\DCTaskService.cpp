﻿#include "DCTaskService.h"
#include "DCLogMacro.h"
#include "DCParseXml.h"
#include "UHead.h"

#include <ace/Guard_T.h>
#include <string>
#include <utility>
#include <string.h>
#include <unistd.h>
#include <time.h>
#include <sys/time.h>
#include <sched.h>
#include <iostream>
#include <fstream>
#include <netdb.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include "DCMon.h"
#include "DCPerfLogStatistic.h"
#include <set>

using namespace std;

int DCTaskService::sm_nDone = 0;
int DCTaskService::m_nQueueState = 0;
DCTQueue<STTask> DCTaskService::sm_qTask;
ACE_Thread_Mutex DCTaskService::sm_aceMutex;
time_t DCTaskService::sm_checktime = time(NULL);

DCTaskService::DCTaskService():m_en(ESeriaBinString)
{
	m_bexit = true;
	memset(&m_lograte, 0x0, sizeof(m_lograte));
	m_task     = NULL;
	m_pairList = NULL;
	m_exchdb   = NULL;
	m_client   = NULL;
	m_clientbill = NULL;
	m_file     = NULL;
	m_tseq     = time(NULL);
	//m_sTask = new DCTQueue<STTask>();
}

DCTaskService::~DCTaskService()
{
	//SAFE_DELETE_PTR(m_dmdb);
	if(m_file)
	{
		//m_file->Destroy();
		SAFE_DELETE_PTR(m_file);
	}

	SAFE_DELETE_PTR(m_exchdb);
}

void getCurrentTime(char* v_date)
{
	tm *p;
	time_t timep;
	time(&timep);
	p=localtime(&timep);
	sprintf(v_date,"%d%02d%02d%02d%02d",(1900+p->tm_year) , (1+p->tm_mon) ,p->tm_mday,p->tm_hour,p->tm_min);

	return;
}

int DCTaskService::GetTableId(long lnOperListId, int &iTableId)
{
	while (true)
	{
		UDBSQL* pQuery = m_dmdb->GetSQL("QueryTableId");
		if (!pQuery)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "not find sqlcode[QueryTableId]");
			return -1;
		}

		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "query table id, operlistid[%ld], tableid[%d]", lnOperListId, iTableId);
		try
		{
			pQuery->UnBindParam();
			pQuery->BindParam(1, lnOperListId);
			pQuery->Execute();
			if (pQuery->Next())
			{
				pQuery->GetValue(1, iTableId);
			}
		}
		catch(UDBException& e)
		{
			std::string sql;
			pQuery->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "SQL[%s]", sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "query execption[%s]", e.ToString());
			int iRet = DCExchDB::CheckDBState(m_dmdb, sm_checktime);
			if (iRet != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", iRet);
				continue;
			}
			else
			{
				return -1;
			}
		}

		break;
	}

	return 0;
}

string DCTaskService::GetBatchId(long lnOperListId)
{
	int iTableId = 0;
	int iRet = GetTableId(lnOperListId, iTableId);
	if (iRet < 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "get tableid failed, use default tableid", iTableId);
		iTableId = 99;
	}
	
	char szBatchId[128] = {0};
	char szBatchTime[16] = {0};
	unsigned long long  cur = 0;	
	time_t curTime =  time(NULL);
	struct tm *pTm = localtime(&curTime);
	sprintf(szBatchTime, "%04d%02d%02d%02d%02d", 
		(1900 + pTm->tm_year), (1 + pTm->tm_mon), pTm->tm_mday, pTm->tm_hour, pTm->tm_min/m_cfg->iBatchTime*m_cfg->iBatchTime);
	sprintf(szBatchId, "%d%s", iTableId, szBatchTime);
	return szBatchId;
}

const char* DCTaskService::GenUnifiledSeq(long sourceid,int lineno)
{
	char szDate[15]={0};
	getCurrentTime(szDate);

	m_tseq++;
	if(m_tseq >= 10000000000L) m_tseq = 1;
	sprintf(m_sseq, "%c%011ld%07d%s%010ld", 'A',sourceid,lineno,szDate,m_tseq);
	return m_sseq;
}

void DCTaskService::GenUnifiledLogSeq(char *v_date)
{
	tm *p;
	time_t timep;
	time(&timep);
	p=localtime(&timep);
	struct timeval current;
	gettimeofday(&current, NULL);
	m_logSeq++;
	if(m_logSeq >= 1000000000000L) m_logSeq = 1;
	sprintf(v_date,"%d%02d%02d%02d%02d%02d%03d%012ld",(1900+p->tm_year) , (1+p->tm_mon) ,p->tm_mday,p->tm_hour,p->tm_min,p->tm_sec,current.tv_usec / 1000,m_logSeq);
	return ;
}

void DCTaskService::state(int done)
{
	sm_nDone = done;
}

void DCTaskService::queueState(int flag)
{
	m_nQueueState = flag;
}

bool DCTaskService::exit()
{
	return m_bexit;
}

int DCTaskService::run()
{
	int ret = 0;
	ret = activate(THR_NEW_LWP | THR_JOINABLE | THR_INHERIT_SCHED | THR_SCOPE_SYSTEM, 1);
	if(ret < 0)
	{
		return -1;
	}
	return 0;
}

int DCTaskService::init(DCDBManer* dmdb,dcf_new::DCFLocalClient *client,dcf_new::DCFLocalClient *clientbill, DCPairList *pairlist, DCMon *screen[],int threadNo)
{
	int ret = 0;

	m_dmdb = dmdb;
	m_exchdb = new DCExchDB(dmdb);
	if(!m_exchdb)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","new DCExchDB() faild.");
		return -1;
	}

	m_client = client;
	m_clientbill = clientbill;
	m_pairList = pairlist;

	//分布式文件系统初始化
	m_file = new DCFile();
	if(!m_file)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","new DCFile() faild.");
		return -1;
	}

	SExchCfg* pcfg = DCExchCfg::instance()->GetConfig();
	m_cfg = pcfg;
	// 初始化特殊 fileType 写备份表
	m_vecFileType.clear();
	if (0 < m_cfg->sBackUpFileType.length())
	{
		SplitString(m_cfg->sBackUpFileType.c_str(), ',', m_vecFileType);
	}

	std::string sfileServ = pcfg->fileServ;
	size_t pos2 = sfileServ.rfind('/');
	if(std::string::npos==pos2)//没找到
	{
		char *szDFS =getenv("DFS_HOME");
		if(NULL==szDFS)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","read env DFS_HOME failed");
			return -1;
		}

		std::string subPath = szDFS;
		if(szDFS[strlen(szDFS)-1] != '/')
		{
			subPath += "/";
			subPath += pcfg->fileServ;
		}
		else
		{
			subPath += pcfg->fileServ;
		}
		sfileServ = subPath;

	}
	else
	{
		sfileServ = pcfg->fileServ;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","local file path %s",sfileServ.c_str());

	ret = m_file->Init(sfileServ.c_str());//, host, port);
	if(ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","FS client init failure,error info[%s]",m_file->GetErrorMsg());
		return -1;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Init DFS success!");

	char *szconfig =getenv("BILLING_CONFIG");
	if(NULL==szconfig)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","env BILLING_CONFIG not config");
		return -1;
	}
	std::string localfile =  pcfg->filepath;
	pos2 = localfile.rfind('/');
	if(std::string::npos==pos2)//没找到
	{
		std::string strLibPath=szconfig;
		int pos = strLibPath.rfind('/');
		std::string subPath=strLibPath.substr(0,pos+1);
		subPath += pcfg->filepath;
		m_szLocalPath = subPath;
	}
	else
	{
		m_szLocalPath = pcfg->filepath;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","local file path %s",m_szLocalPath.c_str());

    // 获取ip地址
    char szHostName[128] = { 0 };
    struct hostent *hent = NULL;
    gethostname(szHostName, sizeof(szHostName));
    hent = gethostbyname(szHostName);
    m_strHostIp = inet_ntoa(*(struct in_addr*)(hent->h_addr_list[0]));
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "hostIp[%s]", m_strHostIp.c_str());

	//初始化指标采集接口
	m_latnlist.clear();
	m_screen 	= screen[0];
	m_gth 		= screen[1];
	m_cdr 		= screen[2];
	m_statFiles = screen[3];

	m_tseq = time(NULL);
	m_nThreadNO = threadNo;
	m_logSeq = 0;

	m_oprStruct = new DCOprStruct();
	m_oprStruct->InitParam(dmdb);

	return 0;
}

int DCTaskService::svc()
{
	int ret = 0;
	STTask tasks;
	ACE_Time_Value expired;
	m_bexit = false;
	m_checktime = time(NULL);
	map<int, long>::iterator iter;
	int sendFailedTime = 0;
	int nPauseCnt(0);
	while(1)	// 可运行
	{
		SExchCfg* pcfg = DCExchCfg::instance()->GetConfig();
		if(m_cfg!=pcfg)
		{
			m_cfg = pcfg;
			// 初始化特殊 fileType 写备份表
			m_vecFileType.clear();
			if (0 < m_cfg->sBackUpFileType.length())
			{
				SplitString(m_cfg->sBackUpFileType.c_str(), ',', m_vecFileType);
			}
		}

		if (DCExchDB::CheckDBState(m_dmdb, m_checktime) < 0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "connected to database error!");
			continue;
		}

		DCPerfLogStatistic::instance()->output_timeing();

		if (m_cfg->nSysStateFlag == 1) // 暂停状态不读取任务不处理
		{
			sleep(1);
			if(sm_nDone == 2)
			{
				ret = ClearTask(m_dmdb);
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","getTask thread exit");
				break;
			}
			else
			{
				if ( 0 == (nPauseCnt++ % 600) )
				{
					DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "process state was set to 1, pausing");
					nPauseCnt = 0;
				}
				continue;
			}
		}

		if(0 == m_nThreadNO)
		{
			if(sm_nDone == 2)
			{
				ret = ClearTask(m_dmdb);
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","getTask thread exit");
				break;
			}
			if(sm_qTask.size() == 0)
			{
				DCPerfTimeVCollect collet(&m_tstat, true);
				collet.start();
				switch(m_cfg->taskmode )
				{
				case 1:
					{
						ret = GetTaskQFirst(m_dmdb,m_cfg->tasklimitnum);	//从数据库获取
						break;
					}
				default:
					{
						ret = GetTask(m_dmdb,tasks);	//从数据库获取
					}
				}
				collet.stop();

				// 输出统计信息
				if(DCLOG_GETLEVEL(DCLOG_CLASS_PERF) > 0 )
				{
					DCPERFLOG(DCLOG_CLASS_PERF, "msgexch.gettask=%ld",collet.m_usec);
				}
				if(sm_qTask.size() == 0)
				{
					sleep(1);
				}
			}
			else
			{
				sleep(1);
			}
			continue;
		}
		ret = sm_qTask.try_dequeue(tasks);	//从任务队列获取
		if(ret < 0)
		{
			expired.set(time(NULL)+5, 0);
			if(sm_qTask.dequeue(tasks, &expired) < 0)	//超时等待获取
			{
				if(sm_nDone == 2)		// 退出执行
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","thread exit");
					break;
				}

				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","task continue");
				continue;
			}
		}
		if(sm_nDone == 2)		// 退出执行
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","task exit");
			break;
		}
		ret = m_exchdb->MarkTaskToDeal(tasks.task_id);
		if(ret == 0)
		{
			DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "sourId[%ld] is dealing,continue", tasks.source_id);
			continue;
		}
		DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "start deal sourId=[%ld]", tasks.source_id);


		iter = m_latnlist.find(tasks.latn_id);
		char servkpi[50] = {0};
		if(iter == m_latnlist.end())
		{
			char group[50] = {0};
			char latn[50] = {0};
			m_latnlist.insert(pair<int, long>(tasks.latn_id,0));
			sprintf(group, "ServKPI|%d", tasks.latn_id);
			sprintf(latn,"%d",tasks.latn_id);
			m_gth->group_set(group, "latnid", latn);
			m_gth->group_set(group, "host", pcfg->host.c_str());
		}
		sprintf(servkpi, "ServKPI|%d",tasks.latn_id);
		//m_gth->state_array_set(servkpi,0,"k22",NULL,(long)m_sTask.size());

		//写任务前移
		m_lSuccessNum = 0;
		ret = m_exchdb->RecordUidAll(tasks.latn_id,m_lSuccessNum,tasks.source_id, tasks.task_id);
		//主键冲突
		if (4001 == ret)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Duplicate entry by SourceId [%ld], TaskId [%ld]", tasks.source_id, tasks.task_id);
			// 处理失败，异常状态
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "source_id[%ld], deal failed, mark to exception, times[%d]", tasks.source_id, ++sendFailedTime);
			ret = m_exchdb->MarkTaskToDup(tasks.task_id, "4");
			m_lograte.source_id = tasks.source_id;
			continue;
		}

		strcpy(tasks.batch_id, GetBatchId(tasks.oper_list_id).c_str());
		m_task = &tasks;
		DCPerfTimeVCollect collet1(&m_tstat, true);
		collet1.start();
		ret = DoTask(tasks);
		collet1.stop();

		// 输出统计信息
		if(DCLOG_GETLEVEL(DCLOG_CLASS_PERF) > 0 )
		{
			DCPERFLOG(DCLOG_CLASS_PERF, "msgexch.dotask=%ld",collet1.m_usec);
		}
		if(ret < 0)
		{
			// 处理失败，异常状态
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "source_id[%ld], deal failed, mark to exception, times[%d]", tasks.source_id, ++sendFailedTime);
			// 文件行数不等于记录行数
			if (-4001 == ret)
			{
				ret = m_exchdb->MarkTaskToDup(tasks.task_id, "4");
			}
			else
			{
				ret = m_exchdb->MarkTaskToException(tasks.task_id);
			}
			m_lograte.source_id = tasks.source_id;
			m_exchdb->UpdateUidAll(tasks.latn_id, m_lSuccessNum, tasks.source_id, tasks.task_id, 9);
			//记录文件稽核
			/*
			if(m_lograte.send_nr)
			{
			m_exchdb->RecordUidAll(tasks.latn_id, tasks.lines,tasks.source_id);
			}
			*/
			if(0<m_cfg->nContinueFailExit && sendFailedTime>m_cfg->nContinueFailExit)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "%d continuous failures, ready to exit", m_cfg->nContinueFailExit);
				DCTaskService::state(2);
			}
		}
		else
		{
			sendFailedTime = 0;
			// 发送成功，更新状态为M0A；接收到应答之后，接收到所有的应答消息，才将source_files状态更新为M0F，同时删除task_manager表
			DCBIZLOG(DCLOG_LEVEL_INFO,0,""," source_id[%ld], source_name[%s], deal success.", tasks.source_id, tasks.source_name);
			DCPerfTimeVCollect collet2(&m_tstat, true);
			collet2.start();
			ret = m_exchdb->MarkFileState(tasks.source_id, "M0A");
			collet2.stop();
			DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "MarkFileState expend[%ld]", collet2.m_usec);
			m_statFiles->cycle_inc("rtsm","k20");
			vector<string>::iterator itrSpecialBackUp = m_vecFileType.begin();
			for (; itrSpecialBackUp != m_vecFileType.end(); itrSpecialBackUp++)
			{
				if (0 == strcmp(tasks.fileType, (*itrSpecialBackUp).c_str()))
				{
					DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "FileType [%s] need special back up", tasks.fileType);
					// 增加写特殊稽核表
					m_exchdb->SpecialBackUp(&tasks);
					break;
				}
			}
			m_exchdb->MarkTaskToDone(tasks.task_id);
			m_lograte.source_id = tasks.source_id;
			//记录文件稽核
			DCPerfTimeVCollect collet3(&m_tstat, true);
			if(!m_lograte.is_timeout)
			{
				collet3.start();
				m_exchdb->UpdateUidAll(tasks.latn_id,m_lSuccessNum,tasks.source_id,tasks.task_id,1);
				collet3.stop();
				DCBIZLOG(DCLOG_LEVEL_INFO,0,"","RecordUidAll expend[%ld]",collet3.m_usec);
			}
		}

		DCBIZLOG(DCLOG_LEVEL_INFO,0,""," source_id[%ld], deal done",tasks.source_id);
		m_task = NULL;
	}
	m_pairList->SetQuit();
	m_bexit = true;
	return 0;
}

int DCTaskService::SplitString(const char* pszStr, const char cSeparator, std::vector<std::string>& vecStr)
{
    if (!pszStr)
    {
        return 0;
    }

    string strField;
    strField.clear();
    for (const char* p = pszStr; *p; p++)
    {
        if ((*p) != cSeparator)
        {
            strField.push_back(*p);
            continue;
        }

        vecStr.push_back(strField);
        strField.clear();
    }

    vecStr.push_back(strField);

    return 0;
}

int DCTaskService::GetBreakPointFile(string strRemoteIp, long lnTaskId, long lnSourceId)
{
    if (!strcmp(m_strHostIp.c_str(), strRemoteIp.c_str()))
    {
        return 0;
    }

	DCPerfTimeVCollect collet_brk(DCPerfLogStatistic::instance()->GetPerfTimeStats("APP", "DoTask"), true);

    string strBreakIp = m_cfg->breakIP;  // 多台主机信息以,分隔
    vector<string> vecBreakIp;
    SplitString(strBreakIp.c_str(), ',', vecBreakIp);
    // 找到断点文件所在主机对应的远程信息
    vector<string>::iterator itBreakIp = vecBreakIp.begin();
    for (; itBreakIp != vecBreakIp.end(); itBreakIp++)
    {   // breakIp格式为 root@127.0.0.1
        size_t iIpPos = itBreakIp->find("@");
        if (iIpPos != itBreakIp->npos)
        {
            if (strRemoteIp == itBreakIp->substr(iIpPos + 1))
            {
                break;
            }
        }
    }

    if (itBreakIp == vecBreakIp.end())
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "not find breakpoint ip, config[%s], need[%s]", strBreakIp.c_str(), strRemoteIp.c_str());
        return -1;
    }

    // 下载远端断点文件到本地
    char szCmd[256] = { 0 };
    sprintf(szCmd, "sshpass -p '%s' scp -o StrictHostKeyChecking=no %s:%s/%ld %s",
        m_cfg->password.c_str(), itBreakIp->c_str(), m_cfg->breakPointPath.c_str(), lnSourceId, m_cfg->breakPointPath.c_str());
    if (system(szCmd))
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "system[%s] failed", szCmd);
        return -1;
    }

    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "system[%s]", szCmd);

    // 更新breakPointIp为本机Ip
    if (UpdateBreakPointIp(lnTaskId, lnSourceId) < 0)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "update break point ip failed, source_id[%ld]", lnSourceId);
        return -1;
    }

    // 更新Ip成功再删除远端的beakPoint文件
    sprintf(szCmd, "sshpass -p '%s' ssh -o StrictHostKeyChecking=no %s 'rm -f %s/%ld'", m_cfg->password.c_str(), itBreakIp->c_str(), m_cfg->breakPointPath.c_str(), lnSourceId);
	int _ret = system(szCmd);

	collet_brk.stop();

    if (_ret)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "system[%s] failed", szCmd);
        return 0;   // 删除远端断点文件
    }

    return 0;
}

int DCTaskService::UpdateBreakPointIp(long lnTaskId, long lnSourceId)
{
    char szSqlCode[256] = { 0 };
    sprintf(szSqlCode, "UpdateBreakPointIp");

    UDBSQL* stmt = m_dmdb->GetSQL(szSqlCode);
    if (stmt == NULL)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "GetSQL[UpdateBreakPointIp] failed.");
        return -1;
    }

    while (true)
    {
        try
        {
            stmt->UnBindParam();
            stmt->BindParam(1, m_strHostIp.c_str());
            stmt->BindParam(2, lnTaskId);
            stmt->BindParam(3, lnSourceId);
            stmt->Execute();
            stmt->Connection()->Commit();
        }
        catch (UDBException& e)
        {
            std::string strSql;
            stmt->GetSqlString(strSql);
            DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "UpdateBreakPointIp sql[%s],execption[%s]", strSql.c_str(), e.ToString());
            if (stmt->Connection()->State() == UDBS_DB_UNLINK)
            {
                DCExchDB::CheckDBState(m_dmdb, m_checktime);
                continue;
            }
            
            return -1;
        }

        break;
    }

    return 0;
}

int DCTaskService::DoTask(const STTask& task)
{
	DCPerfTimeVCollect collet(DCPerfLogStatistic::instance()->GetPerfTimeStats("APP", "DoTask", m_dmdb), true);

	DCBIZLOG(DCLOG_LEVEL_INFO,0,"","start to deal file[%ld]",task.source_id);
	memset(&m_lograte, 0, sizeof(m_lograte));


	ocs::UHead uhd;
	ocs::UFmtBody body;
	ocs::UFmtHead head;

	head.collectid = task.collect_id;
	head.latnid = task.latn_id;
	head.operlistid = task.oper_list_id;
	head.switchid = task.switch_id;
	head.opertype = task.oper_type;
	head.sourceid = task.source_id;
	head.sourcefile = task.source_name;
	head.msgtype = "1";
	head.batchid = task.batch_id;

	unsigned int hashMod = 0;
	char sHashMod[15] = { 0 };
	m_lograte.latn_id = task.latn_id;
	m_lograte.source_id = task.source_id;
	string filename = task.source_name;

	bool exist = false;
	int ret = m_file->IsExistFile(filename.c_str(),exist);
	if(!exist || ret !=0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","file  %s is not exist",filename.c_str());
		return -1;
	}

    std::size_t found = filename.find_last_of("/");
    std::string file = filename.substr(found + 1);

    std::string strLocalFile = m_szLocalPath + file;

    if (access(strLocalFile.c_str(), F_OK) == 0)
    {
        remove(strLocalFile.c_str());
    }

	DCPerfTimeVCollect collet_dfs(DCPerfLogStatistic::instance()->GetPerfTimeStats("SVR", "MsgHBfiledown"), true);
	ret = m_file->DownloadToFile(filename.c_str(), m_szLocalPath.c_str());
	if(ret != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","read file content fail,fileName[%s],error info[%s]",filename.c_str(),m_file->GetErrorMsg());
		collet_dfs.stop();
		return -1;
	}
	collet_dfs.stop();

	DCBIZLOG(DCLOG_LEVEL_INFO,0,"","parse local file[%s]",strLocalFile.c_str());
	//ifstream ifs(strLocalFile.c_str());
	FILE* _files = fopen(strLocalFile.c_str(), "rb");
	if (!_files)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","parse file error");
		return -1;
	}

	struct stat fileStat;
	stat(strLocalFile.c_str(),&fileStat);
	int iFileSize = fileStat.st_size;

	string sline;
	int line = 0;
	// 	if(!ifs)
	// 	{
	// 		DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","parse file error");
	// 		return -1;
	// 	}
	bool timeoutfile = false;
	bool timeout_recovery_file=false;
	if("CALLMISS_"==file.substr(0,9))
	{
		timeout_recovery_file = true;
		timeoutfile = true;
	}
	if("AUTOMISS_"==file.substr(0,9) || "REALMISS_"==file.substr(0,9))
	{
		timeoutfile = true;
	}

    // string 区分大小写，rollback_用小写
    if ("rollback_" == file.substr(0, 9))
    {
        uhd.flag = 1;
    }
    else if (0 == strncmp(task.proc_id, "B", 1)) // 需要透传到B环节的消息
    {
        uhd.flag = 2;
    }
    else if("5grollback_" == file.substr(0,11))
    {
        uhd.flag = 10;
    }
    else if("5GCALLBACK_" == file.substr(0,11))
    {
        uhd.flag = 11;
    }
	// 高额话单拦截 审核通过后的回收话单  
	else if ("HCG_reclaim" == file.substr(0, 11))
	{
		uhd.flag = 9;
	}
	else if("CALLBACK_" == file.substr(0,9))
	{
		uhd.flag=12;
	}
	else if ("HCG_5G_FEE_RECLAIM" == file.substr(0,18))
	{
		uhd.flag=14;
	}
	else if ("HCG_5G_DUR_RECLAIM" == file.substr(0,18))
	{
		uhd.flag=13;
	}
	
	m_lograte.is_timeout = timeout_recovery_file;
	char servkpi[50] = {0};
	sprintf(servkpi, "ServKPI|%d",task.latn_id);
	long lsendMsgCount = m_cfg->lsendMsgCount;
	int  sleeptime = m_cfg->sleeptime;
	string sendmsg="";
	SMsgPair sMsg;
	int count = 0;
	//int screenCount = 0;

	ofstream fExam;
	ofstream fBreakPoint;
    int breakLine = 0;
    char bakPath[128] = { 0 };
    sprintf(bakPath, "%s/%ld", m_cfg->breakPointPath.c_str(), task.source_id);
    if (task.breakIp[0] != '\0')
    {
        // 下载远端breakpoint文件到本地breakPointPath
        if (GetBreakPointFile(task.breakIp, task.task_id, task.source_id) < 0)
        {
            // 获取断点文件失败,更新任务状态为失败
            DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Get Break Point File Failed, source_id[%ld]", task.source_id);
			fclose(_files);
            return -1;
        }

        // 加载本地断点文件
        if (access(bakPath, F_OK) == 0)
        {
            ifstream ifBreakPoint(bakPath);
            if (ifBreakPoint.is_open())
            {
                ifBreakPoint >> breakLine;
                DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "read break file[%s],line:%d", bakPath, breakLine);
            }
            ifBreakPoint.close();
        }

        fBreakPoint.open(bakPath);
    }
    else
    {
        fBreakPoint.open(bakPath);  // 先创建文件再更新ip
        // 更新任务表的break_ip为本机ip
        if (UpdateBreakPointIp(task.task_id, task.source_id) < 0)
        {
            DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "update break point ip failed, source_id[%ld]", task.source_id);
            fBreakPoint.close();
            remove(bakPath);
			fclose(_files);
            return -1;
        }
    }

	int isRoute = 0;
	int fileType = 0;

    char sOperListid[10] = { 0 };
    sprintf(sOperListid, "%d", task.oper_list_id);
    size_t npos;
    if ((npos = m_cfg->operList.find(sOperListid)) != string::npos)
    {
        isRoute = 1;
    }

    // C环节都是OPR的文件，需要根据oprListId解析  |
    if ((SetSplitInfo(task.switch_id) < 0) || (0 == strncmp(task.proc_id, "C", 1)))
    { 
        SetOperListId(task.oper_list_id);
        fileType = 1;
    }
	// 透传B环节的消息也需要解析opr取计费号码
	else if (0 == strncmp(task.proc_id, "B", 1))
	{
        SetOperListId(task.oper_list_id);
        fileType = 2;
	}

	if(0==strncmp(task.proc_id,"C",1))
	{
		m_client = m_clientbill;
	}
	
	char szLine[4096] = {0};

	// 文件行数审核打开
	if ((0 != m_cfg->nCheckFileLines) && ("AUTO_LINE" == file.substr(0, 9)))
	{
		int nLineCount = 0;
		while (!feof (_files))
		{
			memset(szLine, 0, sizeof(szLine));
			if (!fgets(szLine, sizeof(szLine), _files))
			{
				continue;
			}
			else
			{
				nLineCount++;
			}
		}
		// 行数不相等认为出错
		if (nLineCount != task.lines)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","read file[%s],line:[%d],task.lines[%ld],return -4001", task.source_name, nLineCount, task.lines);
			fclose(_files);
			fBreakPoint.close();
			return -4001;
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","read file[%s],line:[%d]=task.lines[%ld]", task.source_name, nLineCount, task.lines);
			rewind(_files);
		}
	}

	while (!feof (_files))
	{
		DCPerfTimeVCollect collet_cdr(DCPerfLogStatistic::instance()->GetPerfTimeStats("APP", "ToMsg"), true);

		memset(szLine, 0, sizeof(szLine));
		if (!fgets(szLine, sizeof(szLine), _files))
			continue;
		if(szLine[strlen(szLine)-1] == '\n')
		{
			szLine[strlen(szLine)-1] = '\0';
		}
		sline = szLine;
		line++;
		if(breakLine > 0)
		{
			breakLine--;
			continue;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","read file[%s],line:%d",sline.c_str(),line);
		GenUnifiledSeq(head.sourceid,line);
		if(timeout_recovery_file)
		{
			found = sline.find_last_of("|");
			uhd.uid = sline.substr(found+1);
			sline=sline.substr(0,found);
		}
		else
		{
			uhd.uid = m_sseq;
		}
		char logSeq[32] = {0};
		GenUnifiledLogSeq(logSeq);
		uhd.logCode = m_cfg->logCodePre + logSeq;
		DCLOG_SETLogCode(uhd.logCode.c_str());
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","logCode[%s]",uhd.logCode.c_str());

		int quecount = 0;
		while(1==m_nQueueState)//后端jstorm队列处于积压状态
		{
			if(0==quecount)
			{
				m_exchdb->MarkRecordState(uhd.uid,"0", task.proc_id, task.latn_id,0,"");
				m_lograte.invalid_nr++;
			}
			quecount++;
			sleep(1);
			continue;
		}

		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","msgpair size:%d",m_pairList->size());
		while(m_pairList->size()>=m_cfg->FlowControlSize)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","system overload[%d]",m_pairList->size());
			sleep(1);
			continue;
		}

		uhd.car = "5";
		if(0==strncmp(task.proc_id,"A",1) && (0!=task.parent_source_id))
		{
			uhd.car = "9";
		}
		if(timeoutfile)
		{
			uhd.car = "11";
		}
		head.recordid = line;
		body.recordBuf = sline;
		string billingNbr = "";
		string serviceBILL;
		//c环节获取计费号码
		if(0==strncmp(task.proc_id,"C",1))
		{
			billingNbr = GetBillingNbrOpr(head.opertype, sline);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "get billingNbr[%s]",billingNbr.c_str());
			serviceBILL = m_cfg->serviceBILL;
			head.billingNbr = billingNbr;
		}
        else
        {
            if (1 == fileType)
            {
                billingNbr = GetBillingNbrOpr(head.opertype, sline);
            }
			else if (2 == fileType)
			{
                billingNbr = GetBillingNbrCAN(head.opertype, sline);
			}
            else
            {
                billingNbr = GetBillingNbrSplit(head.opertype, sline);
            }

            head.billingNbr = billingNbr;
        }

		if ( m_cfg->nTraceNumFlag )
		{
			char szCER[64] = { 0 };
			char szOID[64] = { 0 };
			strncpy( szCER, m_oprStruct->GetCurrValue("CER"), 64 );
			strncpy( szOID, m_oprStruct->GetCurrValue("OID"), 64 );
			uhd.trace = m_exchdb->GetTraceNumFlag(szCER, szOID);
		}

		m_en.clear();
		m_en.encode(uhd);
		m_en.encode(head);
		m_en.encode(body);
		if(DCLOG_GETLEVEL(DCLOG_CLASS_BIZ) >= DCLOG_LEVEL_DEBUG)
		{
			//打印head消息
			m_print.clear();
			m_print.print(uhd);
			m_print.print(head);
			m_print.print(body);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "msg[%s]", m_print.data());
		}
		sendmsg = HexEncode(m_en.data(),m_en.size());

		char buf[20]={0};
		struct timeval tv;
		gettimeofday(&tv, NULL);
		sprintf(buf, "%016ld", tv.tv_sec*1000000 + tv.tv_usec);
		sendmsg.insert(0, buf);

		sMsg.begin = tv;
		sMsg.uuid = uhd.uid;
		sMsg.latnid = task.latn_id;
		sMsg.sendMsg = sendmsg;
		sMsg.sourceid = task.source_id;
		sMsg.taskId = task.task_id;
		//sMsg.servName = serviceBILL;
		sMsg.totalline = task.lines;
		sMsg.billingNbr = billingNbr;
        sMsg.procId[0] = task.proc_id[0];
        sMsg.procId[1] = '\0';
		dcf_new::DCFLocalClient *pTempClient = m_client;
		if(0==strncmp(task.proc_id,"C",1))
		{	
			GetGrayServName(head, serviceBILL, m_cfg->RouteRATEProcess, pTempClient);
			sMsg.servName = serviceBILL;
			m_pairList->in(sMsg.uuid,sMsg);
			if(m_cfg->routeByNbr)
			{
                string strRouteJson;
                strRouteJson = "{\"hashkey\":\"";
                strRouteJson += billingNbr;
                strRouteJson += "\"}";

				if(1==m_cfg->sendtype)//需要应答
				{
					ret = pTempClient->invokeAsync(serviceBILL, sMsg.uuid, sendmsg,"",0,strRouteJson);
				}
				else
				{
					ret = pTempClient->invokeAsync(serviceBILL, sMsg.uuid, sendmsg,"",1,strRouteJson);
				}
				DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "send CCR to BillingBolt,service:%s, uuid:%s, route:%s, operlistId:%ld switchId:%ld", 
                    serviceBILL.c_str(), sMsg.uuid.c_str(),billingNbr.c_str(), task.oper_list_id, task.switch_id);
			}
			else
			{
				if(1==m_cfg->sendtype)//需要应答
				{
					ret = pTempClient->invokeAsync(serviceBILL, sMsg.uuid, sendmsg,"",0);
				}
				else
				{
					ret = pTempClient->invokeAsync(serviceBILL, sMsg.uuid, sendmsg,"",1);
				}
				DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "send CCR to BillingBolt,service:%s, uuid:%s", m_cfg->serviceBILL.c_str(), sMsg.uuid.c_str());
			}
		}
		else
		{
			string strRouteJson;
			strRouteJson = "{\"hashkey\":\"";
			serviceBILL = m_cfg->serviceCTPC;
			GetGrayServName(head, serviceBILL, m_cfg->RouteCTGProcess, pTempClient);
			if (isRoute && !billingNbr.empty())
			{
				strRouteJson += billingNbr;
			}
			else
			{
				sprintf(sHashMod, "%u", hashMod++);
				strRouteJson += sHashMod;
			}
			strRouteJson += "\"}";
			sMsg.servName = serviceBILL;
			m_pairList->in(sMsg.uuid,sMsg);
			if(1==m_cfg->sendtype)//需要应答
			{
				ret = pTempClient->invokeAsync(serviceBILL, sMsg.uuid, sendmsg, "", 0, strRouteJson);
			}
			else
			{
				ret = pTempClient->invokeAsync(serviceBILL, sMsg.uuid, sendmsg, "", 1, strRouteJson);
			}
			DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "send CCR to fmtBolt,service:%s, uuid:%s, route:%s, operListId:%ld, switchId:%ld",
                serviceBILL.c_str(), sMsg.uuid.c_str(), strRouteJson.c_str(), task.oper_list_id, task.switch_id);
		}
		if(ret < 0)
		{
			// 区分灰度服务和生产服务失败的情况
			bool isGrayClient = (pTempClient != m_client);
			int errCode = isGrayClient ? -390001 : -390002;
			char errCodeStr[16] = {0};
			sprintf(errCodeStr, "%d", errCode);
			const char *errTypeStr = isGrayClient ? "grayscale" : "production";

			DCBIZLOG(DCLOG_LEVEL_ERROR, errCode, "", "send failed: %s[%s] invokeAsync failed, return=%d", 
				errTypeStr, serviceBILL.c_str(), ret);
				
			m_exchdb->MarkRecordState(uhd.uid,"0", sMsg.procId, task.latn_id,0,sMsg.billingNbr);
			m_pairList->out(sMsg.uuid,sMsg);//发送失败的不插入缓存
			
			m_lograte.total_nr = task.lines;
			m_lograte.abnormal_nr++;
			fclose(_files);
			//ifs.close();
			fBreakPoint.close();
            remove(strLocalFile.c_str());
			return -1;
		}
		else
		{
			m_lograte.send_nr++;//send success
			m_lograte.normal_nr++;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","send msg: %s",sendmsg.c_str());
		count++;
		m_lSuccessNum = line;
		fBreakPoint.seekp(0, ios::beg);
		fBreakPoint<<line;
		fBreakPoint.flush();
		m_gth->cycle_array_inc(servkpi, 0, "k1");
		m_cdr->cycle_inc("rtsm", "k33");

		collet_cdr.stop();

		if(lsendMsgCount && line%lsendMsgCount==0)
		{
			usleep(sleeptime);
		}
	}
	m_lograte.total_nr = task.lines;

	char monGroup[128] = {0};
	DCKpiMon *ptrBPMon = DCKpiSender::instance()->GetKPIHandle("BILLING", "REC", "MSGEX");
	DCKpiSender::instance()->GetFullGroup("TraceKpi", task.latn_id, monGroup);

	DCKpiSender::instance()->cycle_array_inc(ptrBPMon, monGroup, "", "MsgHBFileDownS", "", iFileSize/1024);
	DCKpiSender::instance()->cycle_array_inc(ptrBPMon, monGroup, "", "MsgHBFileDownT", "", collet_dfs.m_usec/1000);

	fclose(_files);
	fBreakPoint.close();
	//ifs.close();
	remove(strLocalFile.c_str());
	remove(bakPath);
	DCBIZLOG(DCLOG_LEVEL_INFO,0,"","msgNum: %d,taskLines:%ld,sourceid[%ld]", count, task.lines, task.source_id);

	if(m_lograte.invalid_nr)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","sourceid:%ld has %ld overloads ", task.source_id, m_lograte.invalid_nr);
		return -1;
	}

	collet.stop();

	return 0;
}

int DCTaskService::GetTask(DCDBManer *dmdb,STTask& otask)
{
	//DCBIZLOG(DCLOG_LEVEL_DEBUG "", "before try_dequeue");
	std::map<long,int> sourceMap;
	int pid = getpid();
	int threadid =gettid();
	char buf[128]={0};
	sprintf(buf,"%d-%d",pid,threadid);
	DCPerfTimeStats  tstat;         //性能统计
	DCPerfTimeVCollect collet(&tstat, true);
	int row = 0;

	while (1)
	{
		UDBSQL* update = dmdb->GetSQL("UpdateTaskToDeal");
		try
		{
			update->UnBindParam();
			update->BindParam(1,buf);
			update->Execute();
			row = update->GetRowCount();
			update->Connection()->Commit();
		}
		catch(UDBException& e)
		{
			std::string sql;
			update->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "SQL[%s]", sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "update execption[%s]", e.ToString());
			int _ret = DCExchDB::CheckDBState(dmdb, sm_checktime);
			if (_ret != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", _ret);
				continue;
			}
			else
			{
				return -2;
			}
		}

		break;
	}
	
	collet.stop();

	// 输出统计信息
	DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "UpdateTask usec[%ld]", collet.m_usec);

	if(row>0)
	{
		DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "UpdateTask rownum[%d]", row);
	}

	collet.m_usec=0;
	collet.start();

	STTask task;
	while (1)
	{
		UDBSQL* query = dmdb->GetSQL("QueryTask");
		int num = 0;
		try
		{
			query->UnBindParam();
			query->BindParam(1,buf);
			query->Execute();
			while(query->Next())
			{
				query->GetValue(1, task.task_id);
				query->GetValue(2, task.source_id);
				query->GetValue(3, task.parent_source_id);
				query->GetValue(4, task.oper_list_id);
				query->GetValue(5, task.switch_id);
				query->GetValue(6, task.collect_id);
				query->GetValue(7, task.latn_id);
				query->GetValue(8, task.oper_type);
				query->GetValue(9, task.source_name);
				query->GetValue(10, task.proc_id);
				query->GetValue(11, task.batch_id);
				query->GetValue(12, task.lines);
                memset(task.breakIp, 0, sizeof(task.breakIp));
                query->GetValue(13, task.breakIp);
				query->GetValue(14, task.fileType);
				DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "ready_en_dequeue[%u],sourceid[%ld]", sm_qTask.size(), task.source_id);
				sm_qTask.enqueue(task);
			}

			DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "getRecordNum[%d]",num);
		}
		catch(UDBException& e)
		{
			std::string sql;
			query->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "SQL[%s]", sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "query execption[%s]", e.ToString());
			int _ret = DCExchDB::CheckDBState(dmdb, sm_checktime);
			if (_ret != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", _ret);
				continue;
			}
			else
			{
				return -1;
			}
		}

		break;
	}
	
	collet.stop();
	DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "QueryTask usec[%ld]", collet.m_usec);
	return 0;
}

int DCTaskService::GetTaskQFirst(DCDBManer *dmdb,int v_ilimitnum)
{
	//DCBIZLOG(DCLOG_LEVEL_DEBUG "", "before try_dequeue");
	dmdb->FastReset();
	std::map<long,int> sourceMap;
	std::map<int,STTask> tskMap;
	std::set<string> tskFileName; // 文件名剔重
	std::map<int,STTask> dupTaskMap; // 重复文件名更新
	dupTaskMap.clear();
	std::pair<std::set<string>::iterator,bool> ret; // 文件名剔重指针
	tskMap.clear();
	tskFileName.clear();
	int pid = getpid();
	int threadid =gettid();
	char buf[128]={0};
	sprintf(buf,"%d-%d",pid,threadid);
	DCPerfTimeStats  tstat;         //性能统计
	DCPerfTimeVCollect collet(&tstat, true);
	int row = 0;
	int num = 0;
	int dupnum = 0;
	int count=0;
	int limitnum = v_ilimitnum;

	STTask task;
	while (1)
	{
		UDBSQL* qFirst = dmdb->GetSQL("QueryTaskQFirst");
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "SQLName[QueryTaskQFirst] limit[%d]",limitnum);
		try
		{
			qFirst->UnBindParam();
			qFirst->BindParam(1,limitnum);
			qFirst->Execute();
			while(qFirst->Next())
			{
				qFirst->GetValue(1, task.task_id);
				qFirst->GetValue(2, task.source_id);
				qFirst->GetValue(3, task.parent_source_id);
				qFirst->GetValue(4, task.oper_list_id);
				qFirst->GetValue(5, task.switch_id);
				qFirst->GetValue(6, task.collect_id);
				qFirst->GetValue(7, task.latn_id);
				qFirst->GetValue(8, task.oper_type);
				qFirst->GetValue(9, task.source_name);
				qFirst->GetValue(10, task.proc_id);
				qFirst->GetValue(11, task.batch_id);
				qFirst->GetValue(12, task.lines);
                memset(task.breakIp, 0, sizeof(task.breakIp));
                qFirst->GetValue(13, task.breakIp);
				qFirst->GetValue(14, task.fileType);
				// 写入容器前先判断本批次是否有重名文件
				ret = tskFileName.insert(task.source_name);
				if (true == ret.second)
				{
					num++;
					tskMap.insert(make_pair(count++,task));
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "TaskId [%ld] SourceId [%ld] FileName [%s] is dupRecord", task.task_id, task.source_id, task.source_name);
					dupnum++;
					dupTaskMap.insert(make_pair(count++,task));
				}
			}

			DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "getRecordNum to update [%d] duprecord count [%d]", num ,dupnum);
		}
		catch(UDBException& e)
		{
			std::string sql;
			qFirst->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "SQL[%s]", sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "query execption[%s]", e.ToString());
			int _ret = DCExchDB::CheckDBState(dmdb, sm_checktime);
			if (_ret != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", _ret);
				continue;
			}
			else
			{
				return -1;
			}
		}

		break;
	}
	
	collet.stop();
	DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "QueryTaskQFirst usec[%ld]", collet.m_usec);

	collet.m_usec=0;
	collet.start();
	UDBSQL* update = dmdb->GetSQL("UpdateTaskToDealQFirst");
	num = 0;
	std::map<int,STTask>::iterator itertsk;
	for(itertsk = tskMap.begin(); itertsk != tskMap.end(); itertsk++)
	{
		while (1)
		{
			try
			{
				update->UnBindParam();
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "SQLName[UpdateTaskToDealQFirst] pid[%s] source_id[%ld]", buf, itertsk->second.source_id);
				update->BindParam(1,buf);
				update->BindParam(2,itertsk->second.source_id);
				update->BindParam(3,itertsk->second.task_id);
				update->Execute();
				row = update->GetRowCount();
				update->Connection()->Commit();
				if(row >0)
				{
					num++;
					DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "ready_en_dequeue[%u],sourceid[%ld]", sm_qTask.size(), itertsk->second.source_id);
					sm_qTask.enqueue(itertsk->second);
				}	
			}
			catch(UDBException& e)
			{
				std::string sql;
				update->GetSqlString(sql);
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "SQL[%s]", sql.c_str());
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "update execption[%s]", e.ToString());
				int _ret = DCExchDB::CheckDBState(dmdb, sm_checktime);
				if (_ret != 0)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", _ret);
					continue;
				}
				else
				{
					return -2;
				}
			}

			break;
		}
		
	}

	// 重复文件更新
	update = dmdb->GetSQL("UpdateTaskToDupRecord");
	for(itertsk = dupTaskMap.begin(); itertsk != dupTaskMap.end(); itertsk++)
	{
		while (1)
		{
			try
			{
				update->UnBindParam();
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "SQLName[UpdateTaskToDupRecord] pid[%s] source_id[%ld]", buf, itertsk->second.source_id);
				update->BindParam(1,buf);
				update->BindParam(2,itertsk->second.source_id);
				update->BindParam(3,itertsk->second.task_id);
				update->Execute();
				row = update->GetRowCount();
				update->Connection()->Commit();
				if(row >0)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "sourceid[%ld] update state 10", itertsk->second.source_id);
				}	
			}
			catch(UDBException& e)
			{
				std::string sql;
				update->GetSqlString(sql);
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "SQL[%s]", sql.c_str());
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "update execption[%s]", e.ToString());
				int _ret = DCExchDB::CheckDBState(dmdb, sm_checktime);
				if (_ret != 0)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", _ret);
					continue;
				}
				else
				{
					return -2;
				}
			}

			break;
		}
		
	}
	collet.stop();

	// 输出统计信息
	DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "UpdateTaskToDealQFirst usec[%ld], rownum[%d]", collet.m_usec, num);
	return 0;
}

int DCTaskService::ClearTask(DCDBManer *dmdb)
{
	int pid = getpid();
	int threadid =gettid();
	dmdb->FastReset();
	char buf[128]={0};
	sprintf(buf,"%d-%d",pid,threadid);
	int row = 0;

	while (1)
	{
		UDBSQL* pUpdate = dmdb->GetSQL("ClearTask");
		if(pUpdate == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "getSQL[ClearTask] error");
		}
		try
		{
			pUpdate->UnBindParam();
			pUpdate->BindParam(1,buf);
			pUpdate->Execute();
			row = pUpdate->GetRowCount();
			pUpdate->Connection()->Commit();
			DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "clear task,update row[%d]",row);
		}
		catch(UDBException& e)
		{
			std::string sql;
			pUpdate->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "SQL[%s]", sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "update execption[%s]", e.ToString());
			int _ret = DCExchDB::CheckDBState(dmdb, m_checktime);
			if (_ret != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", _ret);
				continue;
			}
			else
			{
				return -1;
			}
		}

		break;
	}
	
	return 0;
}

int DCTaskService::SetSplitInfo(int switchId)
{
	map<int, SplitInfo>::iterator iter;
	iter = DCExchCfg::instance()->GetMapSplitInfo().find(switchId);
	if (iter != DCExchCfg::instance()->GetMapSplitInfo().end())
	{
		m_info = iter->second;
		return 0;
	}
	return -1;
}
std::string DCTaskService::GetBillingNbrSplit(string operType, string buff)
{
	string billingNbr = "";
	int index = -1;
	size_t npos = 0;
	size_t nposEnd = -1;
	int recordIdx = 0;

    int iIndexSize = sizeof(m_info.index) / sizeof(m_info.index[0]);
    int i;
    for (i = 0; i < iIndexSize; i++)
    {
        if (m_info.index[i] != -1)
        {
            index = m_info.index[i];
            break;
        }
    }

    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "operType[%s], indexIndex[%d], indexValue[%d]", operType.c_str(), i, index);
	
	while (recordIdx < index + 1)
	{
		npos = nposEnd + 1;
		if (m_info.splitChar >= 'A' && m_info.splitChar <= 'Z')
		{
			nposEnd = buff.find(" ", npos);
			if (nposEnd == string::npos)
			{
				nposEnd = buff.find('\t', npos);
				if (nposEnd == string::npos)
				{
					return billingNbr;
				}
			}
		}
		else
		{
			nposEnd = buff.find(m_info.splitChar, npos);
		}
		recordIdx++;
	}

    billingNbr = (index != -1) ? buff.substr(npos, nposEnd - npos) : "";
	
	return billingNbr;
}
int DCTaskService::SetOperListId(long oper_list_id)
{
	m_dmdb->FastReset();
	m_oprStruct->SetOperListId(oper_list_id);
	return 0;
}

std::string DCTaskService::GetBillingNbrOpr(string operType, string buff)
{
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "operType[%s]", operType.c_str());
	m_oprStruct->SetBuff(buff);
	string billingNbr = "";
	if (operType.substr(0, 2) == "DA")
	{
		billingNbr = m_oprStruct->GetCurrValue("OID");
	}
	else if (operType.substr(0, 2) == "3G")
	{
		billingNbr = m_oprStruct->GetCurrValue("CER");
	}
	else
	{
		billingNbr = m_oprStruct->GetCurrValue("CAN");
	}

	return billingNbr;

}

std::string DCTaskService::GetBillingNbrCAN(string operType, string buff)
{
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "operType[%s]", operType.c_str());
	m_oprStruct->SetBuff(buff);
	string billingNbr = "";
	if (operType.substr(0, 2) == "DA")
	{
		billingNbr = m_oprStruct->GetCurrValue("OID");
	}
	else if (operType.substr(0, 2) == "3G")
	{
		billingNbr = m_oprStruct->GetCurrValue("CAN");
	}
	else
	{
		billingNbr = m_oprStruct->GetCurrValue("CAN");
	}

	return billingNbr;

}
//根据灰度路由规则表获取服务名
int DCTaskService::GetGrayServName(ocs::UFmtHead& head, string& serviceBILL, string sRouteProcess, dcf_new::DCFLocalClient *pClient)
{
	//路由规则获取服务名称
	string sServiceName = "";
	char szTmp[128] = {0};
	map<string,string> inMapParam;
	if(!strncmp(head.billingNbr.c_str(), "460", strlen("460")))
	{
		inMapParam.insert(map<string,string>::value_type("Imsi",head.billingNbr));
	}
	else
	{
		inMapParam.insert(map<string,string>::value_type("BillingNum", head.billingNbr));
	}
	sprintf(szTmp,"%d",head.latnid);
	inMapParam.insert(map<string,string>::value_type("LatnID", szTmp));
	inMapParam.insert(map<string,string>::value_type("OperType", head.opertype));
	
	// 通过出参pDCFClient获取路由决定的DCFClient
	dcf_new::DCFLocalClient *pDCFClient = NULL;
	int ret = DCGrayscaleRoute::instance()->GetRouteServiceNameDCFClient(sRouteProcess.c_str(), inMapParam, sServiceName, pDCFClient);
	if (ret < 0 || 0 == sServiceName.length())
	{
		DCBIZLOG(DCLOG_LEVEL_INFO, -1, "", "Get RouteServiceName failed.");
		return -1;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Get RouteServiceName[%s]", sServiceName.c_str());
		// 如果获取到有效的DCFClient，替换传入的指针
		if (pDCFClient != NULL && pClient != NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Got valid DCFClient from GetRouteServiceNameDCFClient");
			pClient = pDCFClient;
		}
	}
	serviceBILL = sServiceName;
	return 0;
}


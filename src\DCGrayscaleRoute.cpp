#include "DCGrayscaleRoute.h"
#include "DCLogMacro.h"
#include <unistd.h>
#include <algorithm>
#include <sstream>
#include <set>
#include <cctype>

//using namespace std;

static pthread_mutex_t  s_mutex;

// Base64解码表
static const std::string base64_chars =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    "abcdefghijklmnopqrstuvwxyz"
    "0123456789+/";

// Base64解码函数
std::string base64_decode(const std::string &encoded_string)
{
    int in_len = encoded_string.size();
    int i = 0;
    int in = 0;
    unsigned char char_array_4[4], char_array_3[3];
    std::string ret;

    while (in_len-- && (encoded_string[in] != '=') &&
           (isalnum(encoded_string[in]) || (encoded_string[in] == '+') || (encoded_string[in] == '/')))
    {
        char_array_4[i++] = encoded_string[in];
        in++;
        if (i == 4)
        {
            for (i = 0; i < 4; i++)
                char_array_4[i] = base64_chars.find(char_array_4[i]);

            char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
            char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
            char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];

            for (i = 0; (i < 3); i++)
                ret += char_array_3[i];
            i = 0;
        }
    }

    if (i)
    {
        for (int j = 0; j < i; j++)
            char_array_4[j] = base64_chars.find(char_array_4[j]);

        char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
        char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);

        for (int j = 0; (j < i - 1); j++)
            ret += char_array_3[j];
    }

    return ret;
}

// URL解码函数
std::string url_decode(const std::string &str)
{
    std::string result;
    for (size_t i = 0; i < str.length(); ++i)
    {
        if (str[i] == '%')
        {
            if (i + 2 < str.length())
            {
                std::string hex = str.substr(i + 1, 2);
                char c = static_cast<char>(std::stoi(hex, nullptr, 16));
                result += c;
                i += 2;
            }
        }
        else if (str[i] == '+')
        {
            result += ' ';
        }
        else
        {
            result += str[i];
        }
    }
    return result;
}

// 解密函数
std::string decrypt_password(const std::string &str)
{
    if (str.empty())
    {
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "decrypt_password: Input password is empty");
        return str;
    }

    int str_length = str.length();

    // 提取最后一位数字（分割位置）
    char last_char = str[str_length - 1];

    // 检查最后一位是否为数字，如果不是数字则认为是明文
    if (!isdigit(last_char))
    {
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "decrypt_password: Last char is not digit, treat as plaintext, password_length=%d,Return plaintext password: %s", str_length, str.c_str());
        return str; // 返回原始明文
    }

    int r = last_char - '0'; // 转换为数字

    // 验证分割位置是否有效
    if (r < 1 || r > 9)
    {
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "decrypt_password: Invalid split position %d, treat as plaintext,Return plaintext password: %s", r, str.c_str());
        return str; // 返回原始字符串，可能是明文
    }

    // 检查字符串长度是否足够进行解密操作
    if (str_length < r + 26 + 1)
    {
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "decrypt_password: String length %d insufficient for decryption (need at least %d), treat as plaintext,Return plaintext password: %s", str_length, r + 26 + 1, str.c_str());
        return str; // 返回原始字符串，可能是明文
    }

    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "decrypt_password: Begin decrypt, password_length=%d, split_position=%d", str_length, r);

    // 提取第一部分（0到r位置）
    std::string first_str = str.substr(0, r);

    // 提取第二部分（跳过26个随机字符）
    int end_start = r + 26;
    std::string end_str = str.substr(end_start, str_length - 1 - end_start);

    // 重新组合Base64字符串
    std::string combined = first_str + end_str;

    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "decrypt_password: Combined base64 string length=%d", combined.length());

    // Base64解码
    std::string decoded_str;
    try
    {
        decoded_str = base64_decode(combined);
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "decrypt_password: Base64 decode success, decoded_length=%d", decoded_str.length());
    }
    catch (const std::exception &e)
    {
        // Base64解码失败，返回原始字符串
        DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "decrypt_password: Base64 decode failed: %s, treat as plaintext,Return plaintext password: %s", e.what(), str.c_str());
        return str;
    }

    // URL解码
    std::string final_str = url_decode(decoded_str);

    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "decrypt_password: Decrypt success, final_password_length=%d,Return decrypted password: %s", final_str.length(), final_str.c_str());

    return final_str;
}

struct DCPerfGuard
{
	pthread_mutex_t* m_mutex;
	DCPerfGuard(pthread_mutex_t* mutex)
		:m_mutex(mutex)
	{
        pthread_mutex_lock(m_mutex);
	}
	~DCPerfGuard()
	{
		pthread_mutex_unlock(m_mutex);
	}
};


DCGrayscaleRoute* DCGrayscaleRoute::m_pGrayscaleRoute = NULL; 


bool St_grayscale_route_node::InsertNode(St_grayscale_route_node &v_Node)
{
    list <St_grayscale_route_node> ::iterator itLst;
    //如果是自己的孩子
    if (v_Node.tRoute.ParentId == tRoute.Id && v_Node.tRoute.RouteProcess == tRoute.RouteProcess && v_Node.tRoute.Subscriber == tRoute.Subscriber)
    {
        for(itLst = listChild.begin(); itLst != listChild.end(); ++itLst)
        {
            if ((*itLst).tRoute.Priority < v_Node.tRoute.Priority)
                break;
        }
        listChild.insert(itLst,v_Node);
        v_Node.pParent = this;
        return true;
    }

    for(itLst = listChild.begin(); itLst != listChild.end(); ++itLst)
    {
        if ((*itLst).InsertNode(v_Node) ) return true;
    }
    return false;
}

DCGrayRoute::DCGrayRoute()
{

}

DCGrayRoute::~DCGrayRoute()
{

}


DCGrayscaleRoute::DCGrayscaleRoute()
{
    pthread_mutex_init(&s_mutex, NULL);
	DCPerfGuard guard(&s_mutex);

    m_sSubscriber = "";
	m_sRouteProcess = "";
    m_Master_Data.clear();
    m_Slave_Data.clear();
	m_Master_GreyUserGroupCfg.clear();
	m_Slave_GreyUserGroupCfg.clear();
    m_activeData = ACTIVE_DATA_NULL;
    
    m_Master_Scenes.clear();
    m_Master_Rules.clear();
    m_Master_Conditions.clear();
    m_Master_Types.clear();
    m_Master_Envs.clear();
    
    m_Slave_Scenes.clear();
    m_Slave_Rules.clear();
    m_Slave_Conditions.clear();
    m_Slave_Types.clear();
    m_Slave_Envs.clear();

    m_sSceneChangeInfo = "";
    
}

DCGrayscaleRoute::~DCGrayscaleRoute()
{
    // 释放DCF客户端资源
    map<string, St_route_env>::iterator it;
    
    std::set<dcf_new::DCFLocalClient*> master_clients;  // 用于记录已处理的客户端指针
    for (it = m_Master_DcfClients.begin(); it != m_Master_DcfClients.end(); it++)
    {
        if (it->second.pDCFClient != NULL && master_clients.find(it->second.pDCFClient) == master_clients.end())
        {
            // 记录已处理的客户端指针，避免重复释放
            master_clients.insert(it->second.pDCFClient);
            
            it->second.pDCFClient->shutdown(); // 先关闭客户端
            delete it->second.pDCFClient;
            it->second.pDCFClient = NULL;
        }
    }

    std::set<dcf_new::DCFLocalClient*> slave_clients;  // 用于记录已处理的客户端指针
    for (it = m_Slave_DcfClients.begin(); it != m_Slave_DcfClients.end(); it++)
    {
        if (it->second.pDCFClient != NULL && slave_clients.find(it->second.pDCFClient) == slave_clients.end())
        {
            // 记录已处理的客户端指针，避免重复释放
            slave_clients.insert(it->second.pDCFClient);
            
            it->second.pDCFClient->shutdown(); // 先关闭客户端
            delete it->second.pDCFClient;
            it->second.pDCFClient = NULL;
        }
    }
    
    pthread_mutex_destroy(&s_mutex);
}

DCGrayscaleRoute* DCGrayscaleRoute::instance()
{
    if(m_pGrayscaleRoute == NULL)
    {
        m_pGrayscaleRoute = new DCGrayscaleRoute();	
    }
	return m_pGrayscaleRoute;
}


int DCGrayscaleRoute::SetData(map<string,string> &inMapParam)
{
	map<string,string>::iterator iter = inMapParam.find(ROUTE_CODE_BILLING_NUM);
	if(iter != inMapParam.end())
	{
		m_sAccnumber = iter->second;
	}
	iter = inMapParam.find(ROUTE_CODE_SECTION_NUM);
	if(iter != inMapParam.end())
	{
		m_sSectionNum = iter->second;
	}
    else if(m_sAccnumber != "")
    {
        m_sSectionNum = m_sAccnumber;//没输入号段时，号段取自电话号码
    }
	iter = inMapParam.find(ROUTE_CODE_IMSI);
	if(iter != inMapParam.end())
	{
		m_sImsi = iter->second;
	}
	iter = inMapParam.find(ROUTE_CODE_LATN_ID);
	if(iter != inMapParam.end())
	{
		m_nLatnid = atoi(iter->second.c_str());
	}
	iter = inMapParam.find(ROUTE_CODE_OPER_TYPE);
	if(iter != inMapParam.end())
	{
		m_sOperType = iter->second;
	}
	iter = inMapParam.find(ROUTE_CODE_ACCT_ID);
	if(iter != inMapParam.end())
	{
		m_lAcctid = atol(iter->second.c_str());
	}
	iter = inMapParam.find(ROUTE_CODE_CUST_ID);
	if(iter != inMapParam.end())
	{
		m_lCustid = atol(iter->second.c_str());
	}
	iter = inMapParam.find(ROUTE_CODE_OFFERINST_ID);
	if(iter != inMapParam.end())
	{
		m_lOfferinstid = atol(iter->second.c_str());
	}
	iter = inMapParam.find(ROUTE_CODE_OFFER_ID);
	if(iter != inMapParam.end())
	{
		m_lOfferid = atol(iter->second.c_str());
	}
	return 0;
}

int DCGrayscaleRoute::clearData()
{
	m_sAccnumber = "";
	m_sSectionNum = "";
	m_sOperType = "";
	m_sImsi = "";
	m_lAcctid = 0;
	m_lCustid = 0;
	m_lOfferid = 0;
	m_lOfferinstid = 0;
	m_nLatnid = 0;
	
	return 0;
}

int DCGrayscaleRoute::init(DCDBManer* pdbm, const char * pSubscriber)
{
	if (pdbm == NULL || pSubscriber == NULL || strlen(pSubscriber) == 0 )
	{
	    DCBIZLOG(DCLOG_LEVEL_INFO,0,"","Error! inParam is null");
		return ERR_NOT_FIND_INMAPPARAM;
	}
	m_sSubscriber = pSubscriber;
    
    int ret = 0;
    
    ret = LoadData(pdbm, pSubscriber);
    if (ret >= 0)
    {
        ChangeActiveStatus();
    }
    
	return ret;
}

int DCGrayscaleRoute::LoadData(DCDBManer* pdbm, const char * pSubscriber)
{
    St_grayscale_route_tree *pData = NULL;
	multimap<long, St_grey_user_group_cfg> *pGreyUserGroupCfg = NULL;
    EActiveData tmp_activeData = m_activeData;
	St_grayscale_route_tree tmp_Data;
	multimap<long, St_grey_user_group_cfg> tmp_GreyUserGroupCfg;

	if(ACTIVE_DATA_NULL == m_activeData) 
	{
		pData = &m_Master_Data;
		pGreyUserGroupCfg = &m_Master_GreyUserGroupCfg;
		
        DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Load master data");
	}
	else if(ACTIVE_DATA_MASTER == m_activeData)
	{
	    pData = &m_Slave_Data;
		pGreyUserGroupCfg = &m_Slave_GreyUserGroupCfg;
		tmp_Data = m_Slave_Data;
		tmp_GreyUserGroupCfg = m_Slave_GreyUserGroupCfg;
        DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Load slave data"); 
	}
	else
	{
		pData = &m_Master_Data;
		pGreyUserGroupCfg = &m_Master_GreyUserGroupCfg;
		tmp_Data = m_Master_Data;
		tmp_GreyUserGroupCfg = m_Slave_GreyUserGroupCfg;
        DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Load master data");
	}

    int ret = 0;

    // 加载跨集群数据
    ret = LoadCrossClusterData(pdbm, pSubscriber);

    /*// 加载原灰度路由数据
    ret = InitRouteServiceCfgTree(pdbm, pSubscriber,pData);

	if (ret >= 0)
	{
		ret = InitGreyUserGroupCfg(pdbm, pGreyUserGroupCfg);
	}
    */

	if (ret < 0) //刷新失败,还原数据
	{
	    m_activeData = tmp_activeData;
		if (ACTIVE_DATA_MASTER == m_activeData)
		{
			m_Slave_Data.clear();
			m_Slave_GreyUserGroupCfg.clear();
			m_Slave_Data = tmp_Data;
			m_Slave_GreyUserGroupCfg = tmp_GreyUserGroupCfg;
		}
		else if(ACTIVE_DATA_SLAVE == m_activeData)
	    {
	        m_Master_Data.clear();
			m_Master_GreyUserGroupCfg.clear();
			m_Master_Data = tmp_Data;
			m_Master_GreyUserGroupCfg = tmp_GreyUserGroupCfg;
		}
		else
		{
		    m_Master_Data.clear();
			m_Slave_Data.clear();
			m_Master_GreyUserGroupCfg.clear();
			m_Slave_GreyUserGroupCfg.clear();

		}
	}

    return ret;

}

int DCGrayscaleRoute::ChangeActiveStatus()
{
    if(ACTIVE_DATA_NULL == m_activeData) 
	{
		m_activeData = ACTIVE_DATA_MASTER;
	}
	else if(ACTIVE_DATA_MASTER == m_activeData)
	{
        m_activeData = ACTIVE_DATA_SLAVE;
	}
	else
	{
        m_activeData = ACTIVE_DATA_MASTER;
	}

	return 0;
}



int DCGrayscaleRoute::Set(map<string,string> &inMapParam)
{


   return 0;

}


int DCGrayscaleRoute::GetRouteServiceName(const char * pRouteProcess,map<string,string> &inMapParam, string &sServiceName)
{
    DCPerfGuard guard(&s_mutex);

	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Begin GetRouteServiceName");

	if (inMapParam.size() == 0 || pRouteProcess == NULL || strlen(pRouteProcess) == 0)
	{
	    DCBIZLOG(DCLOG_LEVEL_INFO,0,"","Error! inParam is null");
		return ERR_NOT_FIND_INMAPPARAM;
	}
    m_sRouteProcess = pRouteProcess;
    clearData();
    SetData(inMapParam);
	
    St_grayscale_route_tree *pData = NULL;
	multimap<long, St_grey_user_group_cfg> *pGreyUserGroupCfg = NULL;
	if (ACTIVE_DATA_NULL == m_activeData)
	{
	    DCBIZLOG(DCLOG_LEVEL_INFO,0,"","Error! LoadData is null");
		return ERR_NOT_FIND_RECORD;
	}
	else if(ACTIVE_DATA_MASTER == m_activeData)
	{
		pData = &m_Master_Data;
		pGreyUserGroupCfg = &m_Master_GreyUserGroupCfg;

        DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Use master data");
	}
	else
	{
		pData = &m_Slave_Data;
		pGreyUserGroupCfg = &m_Slave_GreyUserGroupCfg;

        DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Use slave data");
	}

    int ret = 0;
	ret = AnalyzeRouteServiceCfgTree(pData,pGreyUserGroupCfg,sServiceName);
	if (ret < 0)
	{
	    DCBIZLOG(DCLOG_LEVEL_INFO,-1,"","Get ServiceName fail");
		return ret;
	}

    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","End GetServiceName");
    return 0;

}


int DCGrayscaleRoute::InitRouteServiceCfgTree(DCDBManer* pdbm, const char * pSubscriber, St_grayscale_route_tree *p_Data)
{

    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Begin load RouteServiceCfg");
    UDBSQL*pQuery = pdbm->GetSQL("RouteServiceCfg");
    if(pQuery == NULL)
    {
        DCBIZLOG(DCLOG_LEVEL_INFO,-1,"","GetSQL RouteServiceCfg failed.");
        return ERR_NOT_FIND_SQL;
    }
	int ret = 0;

	while(1)
	{
	    try
	    {
			m_TreeData.clear();
	        pQuery->UnBindParam();
	        pQuery->Execute();
			break;
	        
	    }
	    catch(UDBException &e)
	    {
	        DCBIZLOG(DCLOG_LEVEL_INFO,-1,"","InitRouteServiceCfgTree error[%s]",e.ToString());

			bool isDisCon = (UDBS_DB_UNLINK == pQuery->Connection()->State());
			
			if(isDisCon)
			{
				int inCnt = 0;
				int nReconnectInterval = 1;
				bool isSucces = false;
				
				while(inCnt < 3)
				{
					inCnt++;
					ret = pdbm->CheckReset();	//CheckReset函数内部有对错误码进行判断
					if(ret < 0)
					{
						DCBIZLOG(DCLOG_LEVEL_INFO,ret,"","ReConnect failed.");
						DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","sleep %d seconds",nReconnectInterval);
						sleep(nReconnectInterval);
						continue;
					}
					else
					{			
						DCBIZLOG(DCLOG_LEVEL_INFO,0,"","ReConnect success!\n");
						isSucces = true;
						break;
					}
				}
				if(isSucces)
				{
					continue;
				}
			}
	        return e.GetErrorCode();
	    }
	}

	p_Data->clear();
	multimap <long , St_grayscale_route_node>::iterator itMmp,itMap,itUpper,itLower;
	char szTmp[128] = {0};
	while(pQuery->Next())
    {
        St_grayscale_route_node Node;
        pQuery->GetValue(1, Node.tRoute.Id);
        pQuery->GetValue(2, Node.tRoute.ParentId);
		pQuery->GetValue(3, Node.tRoute.Priority);
		pQuery->GetValue(4, szTmp);Node.tRoute.Subscriber = szTmp;
		pQuery->GetValue(5, szTmp);Node.tRoute.RouteProcess = szTmp;
		pQuery->GetValue(6, szTmp);Node.tRoute.RouteCode = szTmp;
        pQuery->GetValue(7, szTmp);Node.tRoute.RouteValueA = szTmp;
        pQuery->GetValue(8, szTmp);Node.tRoute.RouteValueB = szTmp;
        pQuery->GetValue(9, szTmp);Node.tRoute.ServiceName = szTmp;
        pQuery->GetValue(10, Node.tRoute.RouteStatus);

        if(Node.tRoute.RouteValueB.compare(" ") == 0)
        {
            Node.tRoute.RouteValueB ="";
        }
		if(Node.tRoute.RouteValueB.compare("NULL") == 0)
        {
            Node.tRoute.RouteValueB ="";
        }

		//状态0为无效, 同时需要匹配订购路由规则的模块, 才能形成树结构
		if(/*Node.tRoute.RouteStatus != 0 && */strcmp(Node.tRoute.Subscriber.c_str(),pSubscriber) == 0 && strlen(pSubscriber) > 0) 
		{
		   m_TreeData.push_back(Node);
		}
			
		
    }
	list<St_grayscale_route_node>::iterator itl = m_TreeData.begin();
	for(;itl!=m_TreeData.end();itl++)
	{
	    itLower = p_Data->m_mapNode.lower_bound((*itl).tRoute.ParentId);
        itUpper = p_Data->m_mapNode.upper_bound((*itl).tRoute.ParentId);
		if (itLower == itUpper)
		{
		    p_Data->m_mmpRoot.insert(pair<long , St_grayscale_route_node>((*itl).tRoute.ParentId,*itl));
		}
        for(itMap = itLower; itMap != itUpper; ++itMap)
        {
           itMap->second.InsertNode(*itl);
		   break;
        }
		
		itLower = p_Data->m_mapNode.lower_bound((*itl).tRoute.Id);
        itUpper = p_Data->m_mapNode.upper_bound((*itl).tRoute.Id);
		if (itLower == itUpper)
		{
		    p_Data->m_mapNode.insert(pair<long , St_grayscale_route_node>((*itl).tRoute.Id,*itl));
		}

    }
    pQuery->Close();

	if (m_TreeData.size() == 0)
	{
	    DCBIZLOG(DCLOG_LEVEL_INFO,-1,"","RouteServiceCfg not find record.");
		return ERR_NOT_FIND_RECORD;
	}
    return 0;

}

int DCGrayscaleRoute::InitGreyUserGroupCfg(DCDBManer* pdbm, multimap<long, St_grey_user_group_cfg> *p_GreyUserGroupCfg)
{
    
    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Begin load GreyUserGroupCfg");
	int ret = 0;
    UDBSQL*pQuery = pdbm->GetSQL("GreyUserGroupCfg");
    if(pQuery == NULL)
    {
        DCBIZLOG(DCLOG_LEVEL_INFO,-1,"","GetSQL GreyUserGroupCfg failed.");
        return ERR_NOT_FIND_SQL;
    }

	while(1)
	{
	    try
	    {
	        pQuery->UnBindParam();
	        pQuery->Execute();
			break;
	        
	    }
	    catch(UDBException &e)
	    {
	        DCBIZLOG(DCLOG_LEVEL_INFO,-1,"","InitGreyUserGroupCfg error[%s]",e.ToString());

			bool isDisCon = (UDBS_DB_UNLINK == pQuery->Connection()->State());
				
			if(isDisCon)
			{
				int inCnt = 0;
				int nReconnectInterval = 1;
				bool isSucces = false;
				
				while(inCnt < 3)
				{
					inCnt++;
					ret = pdbm->CheckReset();	//CheckReset函数内部有对错误码进行判断
					if(ret < 0)
					{
						DCBIZLOG(DCLOG_LEVEL_INFO,ret,"","ReConnect failed.");
						DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","sleep %d seconds",nReconnectInterval);
						sleep(nReconnectInterval);
						continue;
					}
					else
					{			
						DCBIZLOG(DCLOG_LEVEL_INFO,0,"","ReConnect success!\n");
						isSucces = true;
						break;
					}
				}
				if(isSucces)
				{
					continue;
				}
			}
	        return e.GetErrorCode();
	    }
	}
	p_GreyUserGroupCfg->clear();
	char szTmp[128] = {0};
	while(pQuery->Next())
    {
		St_grey_user_group_cfg Stg;
        pQuery->GetValue(1, Stg.GroupId);
        //pQuery->GetValue(2, szTmp);Stg.Imsi = szTmp;
		//pQuery->GetValue(3, szTmp);Stg.Accnumber = szTmp;
		//pQuery->GetValue(4, Stg.Acctid);
		pQuery->GetValue(2, szTmp);Stg.sValue = szTmp;
		pQuery->GetValue(3, Stg.RouteStatus);

		p_GreyUserGroupCfg->insert(multimap<long,St_grey_user_group_cfg>::value_type(Stg.GroupId, Stg));
    }

    pQuery->Close();
    return 0;

}

int DCGrayscaleRoute::AnalyzeRouteServiceCfgTree(St_grayscale_route_tree * pData, multimap<long, St_grey_user_group_cfg> *pGreyUserGroupCfg, string &sServiceName)
{
    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Begin AnalyzeRouteServiceCfgTree Subscriber [%s] and RouteProcess [%s] to find",m_sSubscriber.c_str(), m_sRouteProcess.c_str());
	int ret = 0;

	multimap <long, St_grayscale_route_node>::iterator itmmp;
	for (itmmp = pData->m_mmpRoot.begin(); itmmp != pData->m_mmpRoot.end(); itmmp++)
	{
		if (itmmp->second.tRoute.Subscriber == m_sSubscriber && itmmp->second.tRoute.RouteProcess == m_sRouteProcess)
		{
			ret = AnalyzeRouteServiceCfgTree(pData,pGreyUserGroupCfg,itmmp->second,sServiceName);
			if (ret == ERR_NOT_FIND_SERVICE_NAME)
			{
				continue;
			}
			return ret;
		}
		
	}

    DCBIZLOG(DCLOG_LEVEL_INFO,ERR_NOT_FIND_SERVICE_NAME,"","Subscriber [%s] and RouteProcess [%s] Can not find ServiceName\n",m_sSubscriber.c_str(), m_sRouteProcess.c_str());
	return ERR_NOT_FIND_SERVICE_NAME;
	
}

int DCGrayscaleRoute::AnalyzeRouteServiceCfgTree(St_grayscale_route_tree * pData, multimap<long, St_grey_user_group_cfg> *pGreyUserGroupCfg, St_grayscale_route_node &v_Node, string &sServiceName)
{
    bool    bl = false;  //当前节点是否匹配
    char 	ServiceName[128]={0};
	char 	RouteCode[128] = {0};
	char 	RouteValueA[128] = {0};
	char 	RouteValueB[128] = {0};

    multimap <long , St_grayscale_route_node>::iterator itMap,itUpper,itLower;
	itLower = pData->m_mapNode.lower_bound(v_Node.tRoute.Id);
    itUpper = pData->m_mapNode.upper_bound(v_Node.tRoute.Id);
    for(itMap = itLower; itMap != itUpper; ++itMap)
    {
       v_Node.pParent = itMap->second.pParent;
	   v_Node.listChild = itMap->second.listChild;
	   break;
    }

	long lId = v_Node.tRoute.Id;
	long lParentId = v_Node.tRoute.ParentId;
	int  nRouteStatus = v_Node.tRoute.RouteStatus;
	//防止配置超长
	strncpy(RouteCode,v_Node.tRoute.RouteCode.c_str(),v_Node.tRoute.RouteCode.length()) ;
	RouteCode[sizeof(RouteCode)-1] = 0;
	strncpy(RouteValueA,v_Node.tRoute.RouteValueA.c_str(),v_Node.tRoute.RouteValueA.length()) ;
	RouteValueA[sizeof(RouteValueA)-1] = 0;
	strncpy(RouteValueB,v_Node.tRoute.RouteValueB.c_str(),v_Node.tRoute.RouteValueB.length()) ;
	RouteValueB[sizeof(RouteValueB)-1] = 0;

	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Current Node Id[%ld], RouteCode[%s], RouteValueA[%s], RouteValueB[%s], RouteStatus[%d]",
       lId,RouteCode, RouteValueA, RouteValueB,nRouteStatus);

	if (lParentId != -1 && v_Node.pParent == NULL) //非完整树结构
	{
	   DCBIZLOG(DCLOG_LEVEL_INFO,0,"","Error! Imperfect tree!");
       return ERR_NOT_FIND_SERVICE_NAME;
	}
	if (v_Node.tRoute.RouteStatus != 1) //当前树节点无效
	{
	   DCBIZLOG(DCLOG_LEVEL_INFO,0,"","Error! Current Node invalid!");
       return ERR_NOT_FIND_SERVICE_NAME;
	}
	if (lParentId != -1)  //非根节点做判断
	{

        multimap<long,St_grey_user_group_cfg>::iterator iter;
		multimap<long,St_grey_user_group_cfg>::iterator itBeg;
		multimap<long,St_grey_user_group_cfg>::iterator itEnd;

		if (strcmp(RouteCode,ROUTE_CODE_BILLING_NUM) == 0 && strlen(RouteCode) > 0)
		{  
		    if(strncmp(RouteValueA,"*",1) == 0)  //*代表全通配
			{
			    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Compare success!  BillingNum=[%s],RouteValueA=[*].",m_sAccnumber.c_str());
				bl = true;
			}

			if (!bl)
			{
			    itBeg = pGreyUserGroupCfg->lower_bound(atol(RouteValueA));
		        itEnd = pGreyUserGroupCfg->upper_bound(atol(RouteValueA));
				for(iter = itBeg; iter != itEnd; ++iter) 
				{
				    if(iter->second.sValue == m_sAccnumber && m_sAccnumber.size() > 0 && iter->second.sValue.size() > 0 && iter->second.RouteStatus == 1)
					{
						DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Compare success!  BillingNum=[%s], GreyUserGroupCfg GroupId=[%ld], AccNum=[%s].",m_sAccnumber.c_str(), iter->second.GroupId, iter->second.sValue.c_str());
						bl = true;
						break;
					}
				}
			}

			if (!bl)
			{
			    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Compare fail!  BillingNum=[%s] not match GreyUserGroupCfg.",m_sAccnumber.c_str());
			}
		
		}
		else if (strcmp(RouteCode,ROUTE_CODE_IMSI) == 0 && strlen(RouteCode) > 0)
		{  
		    if(strncmp(RouteValueA,"*",1) == 0)  //*代表全通配
			{
			    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Compare success!  Imsi=[%s],RouteValueA=[*].",m_sImsi.c_str());
				bl = true;
			}

			if (!bl)
			{
			    itBeg = pGreyUserGroupCfg->lower_bound(atol(RouteValueA));
		        itEnd = pGreyUserGroupCfg->upper_bound(atol(RouteValueA));
				for(iter = itBeg; iter != itEnd; ++iter) 
				{
				    if(iter->second.sValue == m_sImsi && m_sImsi.size() > 0 && iter->second.sValue.size() > 0 && iter->second.RouteStatus == 1)
					{
						DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Compare success!  Imsi=[%s], GreyUserGroupCfg GroupId=[%ld], Imsi=[%s].",m_sImsi.c_str(), iter->second.GroupId, iter->second.sValue.c_str());
						bl = true;
						break;
					}
				}
			}

			if (!bl)
			{
			    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Compare fail!  Imsi=[%s] not match GreyUserGroupCfg.",m_sImsi.c_str());
			}
		
		}
		else if (strcmp(RouteCode,ROUTE_CODE_SECTION_NUM) == 0 && strlen(RouteCode) > 0)
		{
		    if(strncmp(m_sSectionNum.c_str(),RouteValueA, strlen(RouteValueA)) >= 0 && m_sSectionNum.size() > 0 && strlen(RouteValueA) > 0 && m_sSectionNum.size() == strlen(RouteValueA) &&
				strncmp(m_sSectionNum.c_str(),RouteValueB, strlen(RouteValueB)) <= 0 && strlen(RouteValueB) > 0 && m_sSectionNum.size() == strlen(RouteValueB))
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Compare success!  BillingNum=[%s] in RouteValueA=[%s] and RouteValueB=[%s] Section.",m_sSectionNum.c_str(), RouteValueA, RouteValueB);
				bl = true;
			}
			else if(strncmp(RouteValueA,"*",1) == 0)  //*代表全通配
			{
			    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Compare success!  BillingNum=[%s],RouteValueA=[*].",m_sSectionNum.c_str());
				bl = true;
			}
			else
			{
			    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Compare fail!  BillingNum=[%s] not in RouteValueA=[%s] and RouteValueB=[%s] Section.",m_sSectionNum.c_str(), RouteValueA, RouteValueB);
			}
		
		}
		else if (strcmp(RouteCode,ROUTE_CODE_LATN_ID) == 0 && strlen(RouteCode) > 0)
		{
		    if(m_nLatnid == atoi(RouteValueA))
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Compare success!  Latnid=[%d], RouteValueA=[%s].",m_nLatnid, RouteValueA);
				bl = true;
			}
			else if(strncmp(RouteValueA,"*",1) == 0)  //*代表全通配
			{
			    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Compare success!  Latnid=[%d],RouteValueA=[*].",m_nLatnid);
				bl = true;
			}
			else
			{
			    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Compare fail!  Latnid=[%d], RouteValueA=[%s].",m_nLatnid, RouteValueA);
			}
		
		}
		else if (strcmp(RouteCode,ROUTE_CODE_OPER_TYPE) == 0 && strlen(RouteCode) > 0)
		{
		    if(strcmp(m_sOperType.c_str(),RouteValueA) == 0 && m_sOperType.size() > 0 && strlen(RouteValueA) > 0)
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Compare success!  OperType=[%s],RouteValueA=[%s].",m_sOperType.c_str(), RouteValueA);
				bl = true;
			}
			else if(strncmp(RouteValueA,"*",1) == 0)  //*代表全通配
			{
			    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Compare success!  OperType=[%s],RouteValueA=[*].",m_sOperType.c_str());
				bl = true;
			}
			else
			{
			    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Compare fail!  OperType=[%s],RouteValueA=[%s].",m_sOperType.c_str(), RouteValueA);
			}
		
		}
		else if (strcmp(RouteCode,ROUTE_CODE_ACCT_ID) == 0 && strlen(RouteCode) > 0)
		{

		    if(strncmp(RouteValueA,"*",1) == 0)  //*代表全通配
			{
			    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Compare success!  Acctid=[%ld],RouteValueA=[*].",m_lAcctid);
				bl = true;
			}

			if (!bl)
			{
			    itBeg = pGreyUserGroupCfg->lower_bound(atol(RouteValueA));
		        itEnd = pGreyUserGroupCfg->upper_bound(atol(RouteValueA));
				for(iter = itBeg; iter != itEnd; ++iter) 
				{
				    if(atol(iter->second.sValue.c_str()) == m_lAcctid && m_lAcctid > 0 && atol(iter->second.sValue.c_str()) > 0 && iter->second.RouteStatus == 1)
					{
						DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Compare success!  Acctid=[%ld], GreyUserGroupCfg GroupId=[%ld], AcctID=[%ld].",m_lAcctid, iter->second.GroupId, atol(iter->second.sValue.c_str()));
						bl = true;
						break;
					}
				}
			}

			if (!bl)
			{
			    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Compare fail!  Acctid=[%ld] not match GreyUserGroupCfg.",m_lAcctid);
			}

		
		}
		else if (strcmp(RouteCode,ROUTE_CODE_CUST_ID) == 0 && strlen(RouteCode) > 0)
		{
		    if(m_lCustid == atoi(RouteValueA))
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Compare success!  Custid=[%ld], RouteValueA=[%s].",m_lCustid, RouteValueA);
				bl = true;
			}
			else if(strncmp(RouteValueA,"*",1) == 0)  //*代表全通配
			{
			    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Compare success!  Custid=[%ld],RouteValueA=[*].",m_lCustid);
				bl = true;
			}
			else
			{
			    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Compare fail!  Custid=[%ld], RouteValueA=[%s].",m_lCustid, RouteValueA);
			}
		}
		else if (strcmp(RouteCode,ROUTE_CODE_OFFERINST_ID) == 0 && strlen(RouteCode) > 0)
		{
		    if(m_lOfferinstid == atoi(RouteValueA))
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Compare success!  Offerinstid=[%ld], RouteValueA=[%s].",m_lOfferinstid, RouteValueA);
				bl = true;
			}
			else if(strncmp(RouteValueA,"*",1) == 0)  //*代表全通配
			{
			    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Compare success!  Offerinstid=[%ld],RouteValueA=[*].",m_lOfferinstid);
				bl = true;
			}
			else
			{
			    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Compare fail!  Offerinstid=[%ld], RouteValueA=[%s].",m_lOfferinstid, RouteValueA);
			}
		}
		else if (strcmp(RouteCode,ROUTE_CODE_OFFER_ID) == 0 && strlen(RouteCode) > 0)
		{
		    if(m_lOfferid == atoi(RouteValueA))
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Compare success!  Offerid=[%ld], RouteValueA=[%s].",m_lOfferid, RouteValueA);
				bl = true;
			}
			else if(strncmp(RouteValueA,"*",1) == 0)  //*代表全通配
			{
			    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Compare success!  Offerid=[%ld],RouteValueA=[*].",m_lOfferid);
				bl = true;
			}
			else
			{
			    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Compare fail!  Offerid=[%ld], RouteValueA=[%s].",m_lOfferid, RouteValueA);
			}
		}
	
	}

    bool leafbl = false; //叶子节点匹配
	if (!bl && lParentId != -1)  //只做中间和叶子节点的匹配, 根节点默认匹配找下一级
	{
	    DCBIZLOG(DCLOG_LEVEL_INFO,0,"","Compare failed, Now compare with next brother node.");
		return ERR_NOT_FIND_SERVICE_NAME;
	}
	else
	{
	    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Compare succeeded, Now compare with its son node.");
		list <St_grayscale_route_node >::iterator itLst;
		int nRet = 0;
		for (itLst = v_Node.listChild.begin(); itLst != v_Node.listChild.end(); ++itLst)
		{
			nRet = AnalyzeRouteServiceCfgTree(pData,pGreyUserGroupCfg,(*itLst),sServiceName);
			if (nRet == ERR_NOT_FIND_SERVICE_NAME) 
			{
			    continue;
			}
			else if(nRet == 1)//成功匹配叶子节点则结束
			{
			    leafbl = true;
			    break;
			}
			
		}
		
		if (leafbl)
		{
		    return nRet;
		}
	}

    if(lParentId == -1)  //只有根节点
	{
	   DCBIZLOG(DCLOG_LEVEL_INFO,0,"","Only find root node.");
       return ERR_NOT_FIND_SERVICE_NAME;
	}
	else if (v_Node.listChild.empty() && v_Node.tRoute.ServiceName.length() == 0)
	{
	   DCBIZLOG(DCLOG_LEVEL_INFO,0,"","Current Node is leaf node and ServiceName is Null.");
	   return ERR_NOT_FIND_SERVICE_NAME;
	}
	else if(v_Node.listChild.empty()) //只增强叶子节点服务名
	{
	   DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Current Node is leaf node.");
	   strncpy(ServiceName, v_Node.tRoute.ServiceName.c_str(), v_Node.tRoute.ServiceName.length() );
	   ServiceName[sizeof(ServiceName)-1] = 0;   
       sServiceName = ServiceName;

	   return 1;
	}
		
	return 0;
}

int DCGrayscaleRoute::LoadSceneData(DCDBManer *pdbm, const char *pSubscriber)
{
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Begin load dcf_route_scene_his");
    UDBSQL *pQuery = pdbm->GetSQL("RouteSceneHis");
    if (pQuery == NULL)
    {
        DCBIZLOG(DCLOG_LEVEL_INFO, -1, "", "GetSQL RouteSceneHis failed.");
        return ERR_NOT_FIND_SQL;
    }

    int ret = 0;

    while (1)
    {
        try
        {
            pQuery->UnBindParam();
            // 查询条件：app_type='dcf' and center_id in(30001,30002) and status=1 and approval_status=2
            pQuery->Execute();
            break;
        }
        catch (UDBException &e)
        {
            DCBIZLOG(DCLOG_LEVEL_INFO, -1, "", "LoadSceneData error[%s]", e.ToString());

            bool isDisCon = (UDBS_DB_UNLINK == pQuery->Connection()->State());

            if (isDisCon)
            {
                int inCnt = 0;
                int nReconnectInterval = 1;
                bool isSucces = false;

                while (inCnt < 3)
                {
                    inCnt++;
                    ret = pdbm->CheckReset();
                    if (ret < 0)
                    {
                        DCBIZLOG(DCLOG_LEVEL_INFO, ret, "", "ReConnect failed.");
                        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "sleep %d seconds", nReconnectInterval);
                        sleep(nReconnectInterval);
                        continue;
                    }
                    else
                    {
                        DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "ReConnect success!");
                        isSucces = true;
                        break;
                    }
                }
                if (isSucces)
                {
                    continue;
                }
            }
            return e.GetErrorCode();
        }
    }

    // 备份旧场景数据，用于比较版本变化
    map<int, St_route_scene> oldScenes;
    if (ACTIVE_DATA_MASTER == m_activeData)
    {
        oldScenes = m_Slave_Scenes;
        m_Slave_Scenes.clear();
    }
    else
    {
        oldScenes = m_Master_Scenes;
        m_Master_Scenes.clear();
    }

    char szTmp[1024] = {0};

    while (pQuery->Next())
    {
        St_route_scene scene;
        pQuery->GetValue(1, scene.scene_id);
        pQuery->GetValue(2, szTmp);
        scene.scene_name = szTmp;
        pQuery->GetValue(3, szTmp);
        scene.version = szTmp;
        pQuery->GetValue(4, scene.center_id);
        pQuery->GetValue(5, szTmp);
        scene.app_type = szTmp;
        pQuery->GetValue(6, scene.status);
        pQuery->GetValue(7, scene.approval_status);
        pQuery->GetValue(8, szTmp);
        scene.create_time = szTmp;

        // 根据当前主备数据状态，将场景数据存入对应的映射中
        if (ACTIVE_DATA_MASTER == m_activeData)
        {
            m_Slave_Scenes[scene.scene_id] = scene;
            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LoadSceneData: Load scene to slave, scene_id=%d, scene_name=%s, version=%s, create_date=%s",
                     scene.scene_id, scene.scene_name.c_str(), scene.version.c_str(), scene.create_time.c_str());
        }
        else
        {
            m_Master_Scenes[scene.scene_id] = scene;
            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LoadSceneData: Load scene to master, scene_id=%d, scene_name=%s, version=%s, create_date=%s",
                     scene.scene_id, scene.scene_name.c_str(), scene.version.c_str(), scene.create_time.c_str());
        }
    }

    pQuery->Close();

    // 比较场景版本变化
    if (ACTIVE_DATA_MASTER == m_activeData)
    {
        ret = CompareSceneVersion(m_Slave_Scenes, oldScenes, m_sSceneChangeInfo);
    }
    else
    {
        ret = CompareSceneVersion(m_Master_Scenes, oldScenes, m_sSceneChangeInfo);
    }

    // 打印场景变化信息
    DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "LoadSceneData: Scene change info: %s", m_sSceneChangeInfo.c_str());

    return ret;
}

int DCGrayscaleRoute::CompareSceneVersion(map<int, St_route_scene> &newScenes, map<int, St_route_scene> &oldScenes, string &changeInfo)
{
    changeInfo = "";

    // 遍历新加载的场景
    map<int, St_route_scene>::iterator it;
    for (it = newScenes.begin(); it != newScenes.end(); it++)
    {
        int scene_id = it->first;
        St_route_scene &newScene = it->second;

        map<int, St_route_scene>::iterator oldIt = oldScenes.find(scene_id);
        if (oldIt == oldScenes.end())
        {
            // 新增场景
            if (!changeInfo.empty())
            {
                changeInfo += ",";
            }
            changeInfo += to_string(scene_id) + "|场景新增";
            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "CompareSceneVersion: New scene, scene_id=%d, scene_name=%s, version=%s",
                     scene_id, newScene.scene_name.c_str(), newScene.version.c_str());
        }
        else
        {
            // 已存在场景，比较版本
            St_route_scene &oldScene = oldIt->second;
            if (newScene.version != oldScene.version)
            {
                if (newScene.version > oldScene.version)
                {
                    // 版本升级
                    if (!changeInfo.empty())
                    {
                        changeInfo += ",";
                    }
                    changeInfo += to_string(scene_id) + "|升级";
                    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "CompareSceneVersion: Upgrade scene, scene_id=%d, old_version=%s, new_version=%s",
                             scene_id, oldScene.version.c_str(), newScene.version.c_str());
                }
                else
                {
                    // 版本回退
                    if (!changeInfo.empty())
                    {
                        changeInfo += ",";
                    }
                    changeInfo += to_string(scene_id) + "|回退";
                    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "CompareSceneVersion: Rollback scene, scene_id=%d, old_version=%s, new_version=%s",
                             scene_id, oldScene.version.c_str(), newScene.version.c_str());
                }
            }
        }
    }

    // 遍历旧场景，找出已下线的场景
    for (it = oldScenes.begin(); it != oldScenes.end(); it++)
    {
        int scene_id = it->first;
        St_route_scene &oldScene = it->second;

        if (newScenes.find(scene_id) == newScenes.end())
        {
            // 场景下线
            if (!changeInfo.empty())
            {
                changeInfo += ",";
            }
            changeInfo += to_string(scene_id) + "|场景下线";
            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "CompareSceneVersion: Scene offline, scene_id=%d, scene_name=%s, version=%s",
                     scene_id, oldScene.scene_name.c_str(), oldScene.version.c_str());
        }
    }

    return 0;
}

int DCGrayscaleRoute::LoadRuleData(DCDBManer *pdbm, const char *pSubscriber)
{
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Begin load dcf_route_rule_his");
    UDBSQL *pQuery = pdbm->GetSQL("RouteRuleHis");
    if (pQuery == NULL)
    {
        DCBIZLOG(DCLOG_LEVEL_INFO, -1, "", "GetSQL RouteRuleHis failed.");
        return ERR_NOT_FIND_SQL;
    }

    int ret = 0;

    while (1)
    {
        try
        {
            pQuery->UnBindParam();
            // 查询条件：client_code=? and scene_id in (...)
            pQuery->BindParam(1, pSubscriber);
            pQuery->Execute();
            break;
        }
        catch (UDBException &e)
        {
            DCBIZLOG(DCLOG_LEVEL_INFO, -1, "", "LoadRuleData error[%s]", e.ToString());

            bool isDisCon = (UDBS_DB_UNLINK == pQuery->Connection()->State());

            if (isDisCon)
            {
                int inCnt = 0;
                int nReconnectInterval = 1;
                bool isSucces = false;

                while (inCnt < 3)
                {
                    inCnt++;
                    ret = pdbm->CheckReset();
                    if (ret < 0)
                    {
                        DCBIZLOG(DCLOG_LEVEL_INFO, ret, "", "ReConnect failed.");
                        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "sleep %d seconds", nReconnectInterval);
                        sleep(nReconnectInterval);
                        continue;
                    }
                    else
                    {
                        DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "ReConnect success!");
                        isSucces = true;
                        break;
                    }
                }
                if (isSucces)
                {
                    continue;
                }
            }
            return e.GetErrorCode();
        }
    }

    // 清空当前规则数据
    if (ACTIVE_DATA_MASTER == m_activeData)
    {
        m_Slave_Rules.clear();
    }
    else
    {
        m_Master_Rules.clear();
    }

    // 获取有效场景ID列表
    map<int, St_route_scene> *pScenes = NULL;
    if (ACTIVE_DATA_MASTER == m_activeData)
    {
        pScenes = &m_Slave_Scenes;
    }
    else
    {
        pScenes = &m_Master_Scenes;
    }

    char szTmp[1024] = {0};

    while (pQuery->Next())
    {
        St_route_rule rule;
        pQuery->GetValue(1, rule.rule_id);
        pQuery->GetValue(2, szTmp);
        rule.rule_name = szTmp;
        pQuery->GetValue(3, rule.scene_id);
        pQuery->GetValue(4, szTmp);
        rule.env_code = szTmp;
        pQuery->GetValue(5, szTmp);
        rule.dispatch_path = szTmp;
        pQuery->GetValue(6, szTmp);
        rule.client_code = szTmp;
        pQuery->GetValue(7, szTmp);
        rule.topology_name = szTmp;
        pQuery->GetValue(8, rule.priority);
        pQuery->GetValue(9, szTmp);
        rule.version = szTmp;

        // 检查场景ID是否有效，并且规则版本号是否与场景版本号一致
        map<int, St_route_scene>::iterator sceneIt = pScenes->find(rule.scene_id);
        if (sceneIt == pScenes->end())
        {
            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LoadRuleData: Skip rule, scene_id=%d not valid, rule_id=%d",
                     rule.scene_id, rule.rule_id);
            continue;
        }

        St_route_scene &scene = sceneIt->second;
        if (rule.version != scene.version)
        {
            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LoadRuleData: Skip rule due to version mismatch, rule_id=%d, rule_version=%s, scene_version=%s, scene_id=%d",
                     rule.rule_id, rule.version.c_str(), scene.version.c_str(), rule.scene_id);
            continue;
        }

        string mapKey = rule.client_code + "|" + rule.topology_name;
        string dcfClientKey = rule.client_code + "|" + rule.topology_name + "|" + rule.env_code;

        // 根据当前主备数据状态，将规则数据存入对应的映射中
        if (ACTIVE_DATA_MASTER == m_activeData)
        {
            m_Slave_Rules[mapKey].push_back(rule);
            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LoadRuleData: Load rule to slave, rule_id=%d, rule_name=%s, version=%s, scene_id=%d, env_code=%s, dispatch_path=%s, client_code=%s, topology_name=%s, priority=%d, key=%s",
                     rule.rule_id, rule.rule_name.c_str(), rule.version.c_str(), rule.scene_id, rule.env_code.c_str(), rule.dispatch_path.c_str(), rule.client_code.c_str(), rule.topology_name.c_str(), rule.priority, mapKey.c_str());
        }
        else
        {
            m_Master_Rules[mapKey].push_back(rule);
            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LoadRuleData: Load rule to master, rule_id=%d, rule_name=%s, version=%s, scene_id=%d, env_code=%s, dispatch_path=%s, client_code=%s, topology_name=%s, priority=%d, key=%s",
                     rule.rule_id, rule.rule_name.c_str(), rule.version.c_str(), rule.scene_id, rule.env_code.c_str(), rule.dispatch_path.c_str(), rule.client_code.c_str(), rule.topology_name.c_str(), rule.priority, mapKey.c_str());
        }
    }

    pQuery->Close();

    // 规则按优先级降序排序
    map<string, vector<St_route_rule>> *pRules = NULL;
    if (ACTIVE_DATA_MASTER == m_activeData)
    {
        pRules = &m_Slave_Rules;
    }
    else
    {
        pRules = &m_Master_Rules;
    }

    map<string, vector<St_route_rule>>::iterator ruleIt;
    for (ruleIt = pRules->begin(); ruleIt != pRules->end(); ruleIt++)
    {
        vector<St_route_rule> &rules = ruleIt->second;
        // 按优先级降序排序
        sort(rules.begin(), rules.end(), [](const St_route_rule &a, const St_route_rule &b)
             { return a.priority > b.priority; });
    }

    return 0;
}

int DCGrayscaleRoute::LoadConditionData(DCDBManer *pdbm)
{
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Begin load dcf_route_condition_his");
    UDBSQL *pQuery = pdbm->GetSQL("RouteConditionHis");
    if (pQuery == NULL)
    {
        DCBIZLOG(DCLOG_LEVEL_INFO, -1, "", "GetSQL RouteConditionHis failed.");
        return ERR_NOT_FIND_SQL;
    }

    int ret = 0;

    while (1)
    {
        try
        {
            pQuery->UnBindParam();
            pQuery->Execute();
            break;
        }
        catch (UDBException &e)
        {
            DCBIZLOG(DCLOG_LEVEL_INFO, -1, "", "LoadConditionData error[%s]", e.ToString());

            bool isDisCon = (UDBS_DB_UNLINK == pQuery->Connection()->State());

            if (isDisCon)
            {
                int inCnt = 0;
                int nReconnectInterval = 1;
                bool isSucces = false;

                while (inCnt < 3)
                {
                    inCnt++;
                    ret = pdbm->CheckReset();
                    if (ret < 0)
                    {
                        DCBIZLOG(DCLOG_LEVEL_INFO, ret, "", "ReConnect failed.");
                        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "sleep %d seconds", nReconnectInterval);
                        sleep(nReconnectInterval);
                        continue;
                    }
                    else
                    {
                        DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "ReConnect success!");
                        isSucces = true;
                        break;
                    }
                }
                if (isSucces)
                {
                    continue;
                }
            }
            return e.GetErrorCode();
        }
    }

    // 清空当前条件数据
    if (ACTIVE_DATA_MASTER == m_activeData)
    {
        m_Slave_Conditions.clear();
    }
    else
    {
        m_Master_Conditions.clear();
    }

    // 获取有效规则映射（规则ID -> 规则对象）
    map<int, St_route_rule> validRules;
    map<string, vector<St_route_rule>> *pRules = NULL;
    if (ACTIVE_DATA_MASTER == m_activeData)
    {
        pRules = &m_Slave_Rules;
    }
    else
    {
        pRules = &m_Master_Rules;
    }

    map<string, vector<St_route_rule>>::iterator ruleIt;
    for (ruleIt = pRules->begin(); ruleIt != pRules->end(); ruleIt++)
    {
        vector<St_route_rule> &rules = ruleIt->second;
        for (size_t i = 0; i < rules.size(); i++)
        {
            // 避免重复添加相同的规则ID，只保留第一个
            if (validRules.find(rules[i].rule_id) == validRules.end())
            {
                validRules[rules[i].rule_id] = rules[i];
            }
        }
    }

    char szTmp[1024] = {0};

    while (pQuery->Next())
    {
        St_route_condition cond;
        pQuery->GetValue(1, cond.cond_id);
        pQuery->GetValue(2, cond.rule_id);
        pQuery->GetValue(3, cond.group_id);
        pQuery->GetValue(4, szTmp);
        cond.route_type_id = szTmp;
        pQuery->GetValue(5, szTmp);
        cond.operator_str = szTmp;
        pQuery->GetValue(6, szTmp);
        cond.value = szTmp;
        pQuery->GetValue(7, szTmp);
        cond.version = szTmp;

        // 检查规则ID是否有效，并且条件版本号是否与规则版本号一致
        map<int, St_route_rule>::iterator validRuleIt = validRules.find(cond.rule_id);
        if (validRuleIt == validRules.end())
        {
            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LoadConditionData: Skip condition, rule_id=%d not valid, cond_id=%d",
                     cond.rule_id, cond.cond_id);
            continue;
        }

        St_route_rule &rule = validRuleIt->second;
        if (cond.version != rule.version)
        {
            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LoadConditionData: Skip condition due to version mismatch, cond_id=%d, cond_version=%s, rule_version=%s, rule_id=%d",
                     cond.cond_id, cond.version.c_str(), rule.version.c_str(), cond.rule_id);
            continue;
        }

        // 根据当前主备数据状态，将条件数据存入对应的映射中
        if (ACTIVE_DATA_MASTER == m_activeData)
        {
            m_Slave_Conditions[cond.rule_id].push_back(cond);
            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LoadConditionData: Load condition to slave, cond_id=%d, rule_id=%d, version=%s, group_id=%d, route_type_id=%s, operator=%s, value=%s",
                     cond.cond_id, cond.rule_id, cond.version.c_str(), cond.group_id, cond.route_type_id.c_str(), cond.operator_str.c_str(), cond.value.c_str());
        }
        else
        {
            m_Master_Conditions[cond.rule_id].push_back(cond);
            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LoadConditionData: Load condition to master, cond_id=%d, rule_id=%d, version=%s, group_id=%d, route_type_id=%s, operator=%s, value=%s",
                     cond.cond_id, cond.rule_id, cond.version.c_str(), cond.group_id, cond.route_type_id.c_str(), cond.operator_str.c_str(), cond.value.c_str());
        }
    }

    pQuery->Close();

    return 0;
}

int DCGrayscaleRoute::LoadTypeData(DCDBManer *pdbm)
{
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Begin load dcf_route_type");
    UDBSQL *pQuery = pdbm->GetSQL("RouteType");
    if (pQuery == NULL)
    {
        DCBIZLOG(DCLOG_LEVEL_INFO, -1, "", "GetSQL RouteType failed.");
        return ERR_NOT_FIND_SQL;
    }

    int ret = 0;

    while (1)
    {
        try
        {
            pQuery->UnBindParam();
            // 查询条件：center_id in(30001,30002)
            pQuery->Execute();
            break;
        }
        catch (UDBException &e)
        {
            DCBIZLOG(DCLOG_LEVEL_INFO, -1, "", "LoadTypeData error[%s]", e.ToString());

            bool isDisCon = (UDBS_DB_UNLINK == pQuery->Connection()->State());

            if (isDisCon)
            {
                int inCnt = 0;
                int nReconnectInterval = 1;
                bool isSucces = false;

                while (inCnt < 3)
                {
                    inCnt++;
                    ret = pdbm->CheckReset();
                    if (ret < 0)
                    {
                        DCBIZLOG(DCLOG_LEVEL_INFO, ret, "", "ReConnect failed.");
                        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "sleep %d seconds", nReconnectInterval);
                        sleep(nReconnectInterval);
                        continue;
                    }
                    else
                    {
                        DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "ReConnect success!");
                        isSucces = true;
                        break;
                    }
                }
                if (isSucces)
                {
                    continue;
                }
            }
            return e.GetErrorCode();
        }
    }

    // 清空当前类型数据
    if (ACTIVE_DATA_MASTER == m_activeData)
    {
        m_Slave_Types.clear();
    }
    else
    {
        m_Master_Types.clear();
    }

    char szTmp[1024] = {0};

    while (pQuery->Next())
    {
        St_route_type type;
        pQuery->GetValue(1, type.route_type_id);
        pQuery->GetValue(2, szTmp);
        type.route_type_code = szTmp;
        pQuery->GetValue(3, szTmp);
        type.route_type_name = szTmp;

        // 根据当前主备数据状态，将类型数据存入对应的映射中
        if (ACTIVE_DATA_MASTER == m_activeData)
        {
            char route_type_id_str[32];
            sprintf(route_type_id_str, "%d", type.route_type_id);
            m_Slave_Types[route_type_id_str] = type;
            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LoadTypeData: Load type to slave, route_type_id=%d, route_type_code=%s, route_type_name=%s",
                     type.route_type_id, type.route_type_code.c_str(), type.route_type_name.c_str());
        }
        else
        {
            char route_type_id_str[32];
            sprintf(route_type_id_str, "%d", type.route_type_id);
            m_Master_Types[route_type_id_str] = type;
            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LoadTypeData: Load type to master, route_type_id=%d, route_type_code=%s, route_type_name=%s",
                     type.route_type_id, type.route_type_code.c_str(), type.route_type_name.c_str());
        }
    }

    pQuery->Close();

    return 0;
}

int DCGrayscaleRoute::LoadEnvData(DCDBManer *pdbm)
{
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Begin load dcf_route_env");
    UDBSQL *pQuery = pdbm->GetSQL("RouteEnv");
    if (pQuery == NULL)
    {
        DCBIZLOG(DCLOG_LEVEL_INFO, -1, "", "GetSQL RouteEnv failed.");
        return ERR_NOT_FIND_SQL;
    }

    int ret = 0;

    while (1)
    {
        try
        {
            pQuery->UnBindParam();
            // 查询条件：center_id in(30001,30002)
            pQuery->Execute();
            break;
        }
        catch (UDBException &e)
        {
            DCBIZLOG(DCLOG_LEVEL_INFO, -1, "", "LoadEnvData error[%s]", e.ToString());

            bool isDisCon = (UDBS_DB_UNLINK == pQuery->Connection()->State());

            if (isDisCon)
            {
                int inCnt = 0;
                int nReconnectInterval = 1;
                bool isSucces = false;

                while (inCnt < 3)
                {
                    inCnt++;
                    ret = pdbm->CheckReset();
                    if (ret < 0)
                    {
                        DCBIZLOG(DCLOG_LEVEL_INFO, ret, "", "ReConnect failed.");
                        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "sleep %d seconds", nReconnectInterval);
                        sleep(nReconnectInterval);
                        continue;
                    }
                    else
                    {
                        DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "ReConnect success!");
                        isSucces = true;
                        break;
                    }
                }
                if (isSucces)
                {
                    continue;
                }
            }
            return e.GetErrorCode();
        }
    }

    // 备份旧环境数据
    map<string, St_route_env> oldEnvs;
    map<string, St_route_env> oldDcfClients;
    if (ACTIVE_DATA_MASTER == m_activeData)
    {
        oldEnvs = m_Slave_Envs;
        oldDcfClients = m_Slave_DcfClients;
        m_Slave_Envs.clear();
        m_Slave_DcfClients.clear();
    }
    else
    {
        oldEnvs = m_Master_Envs;
        oldDcfClients = m_Master_DcfClients;
        m_Master_Envs.clear();
        m_Master_DcfClients.clear();
    }

    char szTmp[1024] = {0};

    while (pQuery->Next())
    {
        St_route_env env;
        pQuery->GetValue(1, szTmp);
        env.env_code = szTmp;
        pQuery->GetValue(2, szTmp);
        env.env_addr = szTmp;
        pQuery->GetValue(3, szTmp);
        env.env_path = szTmp;
        pQuery->GetValue(4, szTmp);
        env.user_name = szTmp;
        pQuery->GetValue(5, szTmp);
        env.password = decrypt_password(szTmp);
        env.pDCFClient = NULL;
        env.need_init = false; // 默认不需要初始化

        // 根据当前主备数据状态，将环境数据存入对应的映射中
        if (ACTIVE_DATA_MASTER == m_activeData)
        {
            // 检查是否已存在相同的环境配置
            map<string, St_route_env>::iterator oldIt = oldEnvs.find(env.env_code);
            if (oldIt != oldEnvs.end())
            {
                St_route_env &oldEnv = oldIt->second;
                // 检查配置是否有变化
                if (env.env_addr == oldEnv.env_addr &&
                    env.env_path == oldEnv.env_path &&
                    env.user_name == oldEnv.user_name &&
                    env.password == oldEnv.password)
                {
                    // 配置无变化，复用旧的DCF客户端
                    //env.pDCFClient = oldEnv.pDCFClient;
                    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LoadEnvData: Reuse DCF client, env_code=%s", env.env_code.c_str());
                }
                else
                {
                    // 标记需要由业务模块初始化
                    env.need_init = true;
                    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LoadEnvData: Config changed, need to init, env_code=%s", env.env_code.c_str());
                }
            }
            else
            {
                // 新环境，标记为需要由业务模块初始化
                env.need_init = true;
                DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LoadEnvData: New env, need to init, env_code=%s", env.env_code.c_str());
            }

            m_Slave_Envs[env.env_code] = env;
            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LoadEnvData: Load env to slave, env_code=%s, env_addr=%s",
                     env.env_code.c_str(), env.env_addr.c_str());
        }
        else
        {
            // 检查是否已存在相同的环境配置
            map<string, St_route_env>::iterator oldIt = oldEnvs.find(env.env_code);
            if (oldIt != oldEnvs.end())
            {
                St_route_env &oldEnv = oldIt->second;
                // 检查配置是否有变化
                if (env.env_addr == oldEnv.env_addr &&
                    env.env_path == oldEnv.env_path &&
                    env.user_name == oldEnv.user_name &&
                    env.password == oldEnv.password)
                {
                    // 配置无变化，复用旧的DCF客户端
                    //env.pDCFClient = oldEnv.pDCFClient;
                    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LoadEnvData: Reuse DCF client, env_code=%s", env.env_code.c_str());
                }
                else
                {
                    // 标记需要由业务模块初始化
                    env.need_init = true;
                    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LoadEnvData: Config changed, need to init, env_code=%s", env.env_code.c_str());
                }
            }
            else
            {
                // 新环境，标记为需要由业务模块初始化
                env.need_init = true;
                DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LoadEnvData: New env, need to init, env_code=%s", env.env_code.c_str());
            }

            m_Master_Envs[env.env_code] = env;
            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LoadEnvData: Load env to master, env_code=%s, env_addr=%s",
                     env.env_code.c_str(), env.env_addr.c_str());
        }
    }

    pQuery->Close();

    // 初始化DcfClients映射
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Begin init DcfClients");
    
    map<string, vector<St_route_rule>> *pRules = NULL;
    map<string, St_route_env> *pEnvs = NULL;
    map<string, St_route_env> *pDcfClients = NULL;
    
    if (ACTIVE_DATA_MASTER == m_activeData)
    {
        pRules = &m_Slave_Rules;
        pEnvs = &m_Slave_Envs;
        pDcfClients = &m_Slave_DcfClients;
    }
    else
    {
        pRules = &m_Master_Rules;
        pEnvs = &m_Master_Envs;
        pDcfClients = &m_Master_DcfClients;
    }

    // 遍历所有规则做DcfClients映射
    for (map<string, vector<St_route_rule>>::iterator ruleIt = pRules->begin(); ruleIt != pRules->end(); ++ruleIt)
    {
        for (size_t i = 0; i < ruleIt->second.size(); i++)
        {
            const St_route_rule &rule = ruleIt->second[i];

            // 如果规则有环境编码，则添加映射
            if (!rule.env_code.empty())
            {
                // 查找对应的环境配置
                map<string, St_route_env>::iterator envIt = pEnvs->find(rule.env_code);
                if (envIt != pEnvs->end())
                {
                    // key: client_code|topology_name|env_code
                    string key = rule.client_code + "|" + rule.topology_name + "|" + rule.env_code;

                    St_route_env envCopy = envIt->second;

                    // 检查环境是否需要初始化
                    if (!envCopy.need_init)
                    {
                        // 不需要初始化，尝试从旧的DcfClients映射中获取值
                        map<string, St_route_env>::iterator oldDcfIt = oldDcfClients.find(key);
                        if (oldDcfIt != oldDcfClients.end())
                        {
                            envCopy.pDCFClient = oldDcfIt->second.pDCFClient;
                            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LoadEnvData: Reuse DCF client from old DcfClients, key=%s", key.c_str());
                        }
                    }
                    else
                    {
                        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LoadEnvData: Env needs init, will use new DCF client, key=%s", key.c_str());
                    }

                    (*pDcfClients)[key] = envCopy;

                    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LoadEnvData: Added DcfClient mapping, key=%s, env_code=%s",
                             key.c_str(), rule.env_code.c_str());
                }
                else
                {
                    DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "LoadEnvData: Cannot find env for env_code=%s", rule.env_code.c_str());
                }
            }
        }
    }

    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "End init DcfClients, count=%d", pDcfClients->size());

    return 0;
}

int DCGrayscaleRoute::LogRefreshResult(DCDBManer *pdbm, int result)
{
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Begin log refresh result");
    UDBSQL *pInsert = pdbm->GetSQL("LogRefreshResult");
    if (pInsert == NULL)
    {
        DCBIZLOG(DCLOG_LEVEL_INFO, -1, "", "GetSQL LogRefreshResult failed.");
        // 记录日志失败不影响主流程
        return 0;
    }

    int ret = 0;

    while (1)
    {
        try
        {
            pInsert->UnBindParam();

            // 获取当前时间
            time_t now = time(NULL);
            struct tm *tm_now = localtime(&now);
            char szTime[64] = {0};
            strftime(szTime, sizeof(szTime), "%Y-%m-%d %H:%M:%S", tm_now);
            // 获取进程ID
            pid_t pid = getpid();

            // 根据当前数据源获取中心ID列表
            stringstream ss;
            map<int, St_route_scene> *pScenes = NULL;
            if (ACTIVE_DATA_MASTER == m_activeData)
            {
                pScenes = &m_Slave_Scenes;
            }
            else
            {
                pScenes = &m_Master_Scenes;
            }
            // 从场景数据中提取中心ID并且去重
            set<int> uniqueCenterIds;
            for (map<int, St_route_scene>::iterator it = pScenes->begin(); it != pScenes->end(); ++it)
            {
                uniqueCenterIds.insert(it->second.center_id);
            }

            // 拼接成字符串
            bool firstCenter = true;
            for (set<int>::iterator it = uniqueCenterIds.begin(); it != uniqueCenterIds.end(); ++it)
            {
                if (!firstCenter)
                {
                    ss << ",";
                }
                ss << *it;
                firstCenter = false;
            }

            // 根据result参数判断成功或失败，result >= 0 表示成功，否则失败
            string refreshStatus = (result >= 0) ? "success" : "fail";

            // 绑定参数
            pInsert->BindParam(1, szTime);
            pInsert->BindParam(2, ss.str().c_str());
            pInsert->BindParam(3, m_sSubscriber.c_str());
            pInsert->BindParam(4, to_string(pid).c_str());
            pInsert->BindParam(5, m_sSceneChangeInfo.c_str());
            pInsert->BindParam(6, refreshStatus.c_str());

            std::string strSql;
            pInsert->GetSqlString(strSql);
            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "sql name:[%s], [%s]", "LogRefreshResult", strSql.c_str());
            pInsert->Execute();
            pInsert->Connection()->Commit();
            DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "LogRefreshResult: Success, time=%s, pid=%d, refreshStatus=%s, result=%d",
                     szTime, pid, refreshStatus.c_str(), result);

            break;
        }
        catch (UDBException &e)
        {
            DCBIZLOG(DCLOG_LEVEL_INFO, -1, "", "LogRefreshResult error[%s]", e.ToString());

            pInsert->Connection()->Rollback();

            bool isDisCon = (UDBS_DB_UNLINK == pInsert->Connection()->State());

            if (isDisCon)
            {
                int inCnt = 0;
                int nReconnectInterval = 1;
                bool isSucces = false;

                while (inCnt < 3)
                {
                    inCnt++;
                    ret = pdbm->CheckReset();
                    if (ret < 0)
                    {
                        DCBIZLOG(DCLOG_LEVEL_INFO, ret, "", "ReConnect failed.");
                        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "sleep %d seconds", nReconnectInterval);
                        sleep(nReconnectInterval);
                        continue;
                    }
                    else
                    {
                        DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "ReConnect success!");
                        isSucces = true;
                        break;
                    }
                }
                if (isSucces)
                {
                    continue;
                }
            }

            DCBIZLOG(DCLOG_LEVEL_INFO, -1, "", "LogRefreshResult failed, but continue.");
            break; // 记录失败不影响主流程
        }
    }

    return 0;
}

bool DCGrayscaleRoute::CompareCondition(const string &route_type_code, const string &operator_str, const string &cond_value, const string &input_value)
{
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "CompareCondition: route_type_code=%s, operator=%s, cond_value=%s, input_value=%s",
             route_type_code.c_str(), operator_str.c_str(), cond_value.c_str(), input_value.c_str());

    // 如果条件为*，表示通配，直接返回匹配成功
    if (cond_value == "*")
    {
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "CompareCondition: Wildcard match");
        return true;
    }

    // 如果输入值为空，返回不匹配
    if (input_value.empty())
    {
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "CompareCondition: Input value is empty");
        return false;
    }

    bool is_number = true;

    // 检查是否为数字类型
    for (size_t i = 0; i < input_value.length(); i++)
    {
        if (!isdigit(input_value[i]) && input_value[i] != '.' && input_value[i] != '-')
        {
            is_number = false;
            break;
        }
    }

    if (is_number)
    {
        // 如果是数字类型，进行数字比较
        double input_num = atof(input_value.c_str());

        if (operator_str == "=")
        {
            double cond_num = atof(cond_value.c_str());
            return (input_num == cond_num);
        }
        else if (operator_str == "<>")
        {
            double cond_num = atof(cond_value.c_str());
            return (input_num != cond_num);
        }
        else if (operator_str == ">")
        {
            double cond_num = atof(cond_value.c_str());
            return (input_num > cond_num);
        }
        else if (operator_str == "<")
        {
            double cond_num = atof(cond_value.c_str());
            return (input_num < cond_num);
        }
        else if (operator_str == ">=")
        {
            double cond_num = atof(cond_value.c_str());
            return (input_num >= cond_num);
        }
        else if (operator_str == "<=")
        {
            double cond_num = atof(cond_value.c_str());
            return (input_num <= cond_num);
        }
        else if (operator_str == "between")
        {
            // between值格式：最小值,最大值
            size_t pos = cond_value.find(",");
            if (pos != string::npos)
            {
                double min_val = atof(cond_value.substr(0, pos).c_str());
                double max_val = atof(cond_value.substr(pos + 1).c_str());
                return (input_num >= min_val && input_num <= max_val);
            }
        }
        else if (operator_str == "in")
        {
            // in值格式：值1,值2,值3,...
            size_t pos = 0;
            string token;
            bool found = false;
            string values = cond_value;

            while ((pos = values.find(",")) != string::npos)
            {
                token = values.substr(0, pos);
                double cond_num = atof(token.c_str());
                if (input_num == cond_num)
                {
                    found = true;
                    break;
                }
                values.erase(0, pos + 1);
            }

            // 检查最后一个值
            if (!found && !values.empty())
            {
                double cond_num = atof(values.c_str());
                if (input_num == cond_num)
                {
                    found = true;
                }
            }

            return found;
        }
        else if (operator_str == "not in")
        {
            // not in值格式：值1,值2,值3,...
            size_t pos = 0;
            string token;
            bool found = false;
            string values = cond_value;

            while ((pos = values.find(",")) != string::npos)
            {
                token = values.substr(0, pos);
                double cond_num = atof(token.c_str());
                if (input_num == cond_num)
                {
                    found = true;
                    break;
                }
                values.erase(0, pos + 1);
            }

            // 检查最后一个值
            if (!found && !values.empty())
            {
                double cond_num = atof(values.c_str());
                if (input_num == cond_num)
                {
                    found = true;
                }
            }

            return !found;
        }
    }
    else
    {
        // 如果是字符串类型，进行字符串比较
        if (operator_str == "=")
        {
            return (input_value == cond_value);
        }
        else if (operator_str == "<>")
        {
            return (input_value != cond_value);
        }
        else if (operator_str == ">")
        {
            return (input_value > cond_value);
        }
        else if (operator_str == "<")
        {
            return (input_value < cond_value);
        }
        else if (operator_str == ">=")
        {
            return (input_value >= cond_value);
        }
        else if (operator_str == "<=")
        {
            return (input_value <= cond_value);
        }
        else if (operator_str == "like")
        {
            // 处理like操作符，支持%通配符
            // 示例：like 18988575%
            string pattern = cond_value;
            size_t pos = pattern.find("%");

            if (pos == string::npos)
            {
                // 没有通配符，精确匹配
                return (input_value == pattern);
            }
            else if (pos == 0)
            {
                // 前缀通配符：%123
                string suffix = pattern.substr(1);
                return (input_value.length() >= suffix.length() &&
                        input_value.substr(input_value.length() - suffix.length()) == suffix);
            }
            else if (pos == pattern.length() - 1)
            {
                // 后缀通配符：123%
                string prefix = pattern.substr(0, pos);
                return (input_value.length() >= prefix.length() &&
                        input_value.substr(0, prefix.length()) == prefix);
            }
            else
            {
                // 中间通配符：12%34
                string prefix = pattern.substr(0, pos);
                string suffix = pattern.substr(pos + 1);
                return (input_value.length() >= prefix.length() + suffix.length() &&
                        input_value.substr(0, prefix.length()) == prefix &&
                        input_value.substr(input_value.length() - suffix.length()) == suffix);
            }
        }
        else if (operator_str == "in")
        {
            // in值格式：值1,值2,值3,...
            size_t pos = 0;
            string token;
            bool found = false;
            string values = cond_value;

            while ((pos = values.find(",")) != string::npos)
            {
                token = values.substr(0, pos);
                if (input_value == token)
                {
                    found = true;
                    break;
                }
                values.erase(0, pos + 1);
            }

            // 检查最后一个值
            if (!found && !values.empty())
            {
                if (input_value == values)
                {
                    found = true;
                }
            }

            return found;
        }
        else if (operator_str == "not in")
        {
            // not in值格式：值1,值2,值3,...
            size_t pos = 0;
            string token;
            bool found = false;
            string values = cond_value;

            while ((pos = values.find(",")) != string::npos)
            {
                token = values.substr(0, pos);
                if (input_value == token)
                {
                    found = true;
                    break;
                }
                values.erase(0, pos + 1);
            }

            // 检查最后一个值
            if (!found && !values.empty())
            {
                if (input_value == values)
                {
                    found = true;
                }
            }

            return !found;
        }
    }

    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "CompareCondition: Not supported operator %s", operator_str.c_str());
    return false;
}

int DCGrayscaleRoute::AnalyzeRouteRules(const char *pRouteProcess, map<string, string> &inMapParam, string &sServiceName, string &env_code)
{
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Begin AnalyzeRouteRules");

    clearData();
    SetData(inMapParam);

    string mapKey = m_sSubscriber + "|" + pRouteProcess;

    // 获取当前主备的数据
    map<string, vector<St_route_rule>> *pRules = NULL;
    map<int, vector<St_route_condition>> *pConditions = NULL;
    map<string, St_route_type> *pTypes = NULL;

    if (ACTIVE_DATA_MASTER == m_activeData)
    {
        pRules = &m_Master_Rules;
        pConditions = &m_Master_Conditions;
        pTypes = &m_Master_Types;
    }
    else if (ACTIVE_DATA_SLAVE == m_activeData)
    {
        pRules = &m_Slave_Rules;
        pConditions = &m_Slave_Conditions;
        pTypes = &m_Slave_Types;
    }
    else
    {
        DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "AnalyzeRouteRules: No active data");
        return ERR_NOT_FIND_RECORD;
    }

    // 查找规则
    map<string, vector<St_route_rule>>::iterator ruleIt = pRules->find(mapKey);
    if (ruleIt == pRules->end())
    {
        DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "AnalyzeRouteRules: No rule found for key=%s", mapKey.c_str());
        return ERR_NOT_FIND_RECORD;
    }

    // 按优先级遍历规则
    vector<St_route_rule> &rules = ruleIt->second;
    for (size_t i = 0; i < rules.size(); i++)
    {
        St_route_rule &rule = rules[i];

        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "AnalyzeRouteRules: Check rule, rule_id=%d, rule_name=%s, priority=%d, version=%s",
                 rule.rule_id, rule.rule_name.c_str(), rule.priority, rule.version.c_str());

        // 查找规则对应的条件
        map<int, vector<St_route_condition>>::iterator condIt = pConditions->find(rule.rule_id);
        if (condIt == pConditions->end())
        {
            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "AnalyzeRouteRules: No condition found for rule_id=%d", rule.rule_id);
            continue;
        }

        vector<St_route_condition> &conditions = condIt->second;

        // 按分组ID对条件进行分组
        map<int, vector<St_route_condition>> groupConditions;
        for (size_t j = 0; j < conditions.size(); j++)
        {
            St_route_condition &cond = conditions[j];
            groupConditions[cond.group_id].push_back(cond);
        }

        // 检查各组条件
        bool rule_match = false;

        // 不同条件组为或的关系，任意一组满足条件则匹配成功
        map<int, vector<St_route_condition>>::iterator groupIt;
        for (groupIt = groupConditions.begin(); groupIt != groupConditions.end(); groupIt++)
        {
            int group_id = groupIt->first;
            vector<St_route_condition> &group_conditions = groupIt->second;

            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "AnalyzeRouteRules: Check condition group, group_id=%d, condition_count=%d",
                     group_id, group_conditions.size());

            // 同组条件为且的关系，所有条件都满足才匹配成功
            bool group_match = true;

            for (size_t k = 0; k < group_conditions.size(); k++)
            {
                St_route_condition &cond = group_conditions[k];

                // 获取路由类型编码
                map<string, St_route_type>::iterator typeIt = pTypes->find(cond.route_type_id);
                if (typeIt == pTypes->end())
                {
                    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "AnalyzeRouteRules: No type found for route_type_id=%s",
                             cond.route_type_id.c_str());
                    group_match = false;
                    break;
                }

                St_route_type &type = typeIt->second;

                // 获取输入参数中对应的值
                map<string, string>::iterator paramIt = inMapParam.find(type.route_type_code);
                if (paramIt == inMapParam.end())
                {
                    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "AnalyzeRouteRules: No input param found for route_type_code=%s",
                             type.route_type_code.c_str());
                    group_match = false;
                    break;
                }

                string input_value = paramIt->second;

                // 比较条件
                bool cond_match = CompareCondition(type.route_type_code, cond.operator_str, cond.value, input_value);

                DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "AnalyzeRouteRules: Condition match result: %d, route_type_code=%s, operator=%s, cond_value=%s, input_value=%s, version=%s",
                         cond_match, type.route_type_code.c_str(), cond.operator_str.c_str(), cond.value.c_str(), input_value.c_str(), cond.version.c_str());

                if (!cond_match)
                {
                    group_match = false;
                    break;
                }
            }

            if (group_match)
            {
                rule_match = true;
                break;
            }
        }

        if (rule_match)
        {
            // 规则匹配成功，返回服务名称和环境编码
            sServiceName = rule.dispatch_path;
            env_code = rule.env_code;

            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "AnalyzeRouteRules: Rule match success, rule_id=%d, service_name=%s, env_code=%s, version=%s",
                     rule.rule_id, sServiceName.c_str(), env_code.c_str(), rule.version.c_str());

            return 0;
        }
    }

    DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "AnalyzeRouteRules: No rule match");
    return ERR_NOT_FIND_SERVICE_NAME;
}

int DCGrayscaleRoute::GetRouteServiceNameDCFClient(const char *pRouteProcess, map<string, string> &inMapParam, string &sServiceName, dcf_new::DCFLocalClient *&pDCFClient)
{
    DCPerfGuard guard(&s_mutex);

    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Begin GetRouteServiceNameDCFClient");

    if (inMapParam.size() == 0 || pRouteProcess == NULL || strlen(pRouteProcess) == 0)
    {
        DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "Error! inParam is null");
        return ERR_NOT_FIND_INMAPPARAM;
    }

    m_sRouteProcess = pRouteProcess;

    // 查找匹配的规则，获取服务名称和环境编码
    string env_code;
    int ret = AnalyzeRouteRules(pRouteProcess, inMapParam, sServiceName, env_code);
    if (ret != 0)
    {
        DCBIZLOG(DCLOG_LEVEL_INFO, ret, "", "GetRouteServiceNameDCFClient: AnalyzeRouteRules failed");
        return ERR_NOT_FIND_INMAPPARAM;
    }

    string dcfClientKey = m_sSubscriber + "|" + pRouteProcess + "|" + env_code;
    
    // 获取环境对应的DCF客户端
    map<string, St_route_env> *pDcfClients = NULL;
    if (ACTIVE_DATA_MASTER == m_activeData)
    {
        pDcfClients = &m_Master_DcfClients;
    }
    else if (ACTIVE_DATA_SLAVE == m_activeData)
    {
        pDcfClients = &m_Slave_DcfClients;
    }
    else
    {
        DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "GetRouteServiceNameDCFClient: No active data");
        return ERR_NOT_FIND_RECORD;
    }
    
    // 查找对应的环境配置
    map<string, St_route_env>::iterator envIt = pDcfClients->find(dcfClientKey);
    if (envIt == pDcfClients->end())
    {
        DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "GetRouteServiceNameDCFClient: No env found for key=%s", dcfClientKey.c_str());
        return ERR_NOT_FIND_RECORD;
    }

    // 返回DCF客户端
    pDCFClient = envIt->second.pDCFClient;
    if (pDCFClient == NULL)
    {
        DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "GetRouteServiceNameDCFClient: DCF client is NULL for key=%s", dcfClientKey.c_str());
        return ERR_NOT_FIND_RECORD;
    }

    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "GetRouteServiceNameDCFClient: Success, service_name=%s, env_code=%s, key=%s",
             sServiceName.c_str(), env_code.c_str(), dcfClientKey.c_str());

    return 0;
}

int DCGrayscaleRoute::InitDCFClient(const char *pRouteProcess, const char *pEnvCode, dcf_new::DCFLocalClient *pDCFClient)
{
    if (pRouteProcess == NULL || strlen(pRouteProcess) == 0 || pEnvCode == NULL || strlen(pEnvCode) == 0 || pDCFClient == NULL)
    {
        DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "InitDCFClient: Invalid parameters");
        return ERR_NOT_FIND_INMAPPARAM;
    }

    DCPerfGuard guard(&s_mutex);
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Begin InitDCFClient: route_process=%s, env_code=%s", pRouteProcess, pEnvCode);

    string dcfClientKey = m_sSubscriber + "|" + pRouteProcess + "|" + pEnvCode;
    
    // 根据当前主备数据选择主备映射
    map<string, St_route_env> *pDcfClients = NULL;
    if (ACTIVE_DATA_MASTER == m_activeData)
    {
        pDcfClients = &m_Master_DcfClients;
    }
    else if (ACTIVE_DATA_SLAVE == m_activeData)
    {
        pDcfClients = &m_Slave_DcfClients;
    }
    else
    {
        DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "InitDCFClient: No active data");
        return ERR_NOT_FIND_RECORD;
    }

    map<string, St_route_env>::iterator it = pDcfClients->find(dcfClientKey);
    if (it == pDcfClients->end())
    {
        DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "InitDCFClient: Cannot find env for key=%s", dcfClientKey.c_str());
        return ERR_NOT_FIND_RECORD;
    }

    St_route_env &env = it->second;

    // 检查是否需要初始化
    if (!env.need_init)
    {
        DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "InitDCFClient: No need to init for key=%s", dcfClientKey.c_str());
        return 0; // 不需要初始化直接返回成功
    }

    // 更新环境配置中的DCF客户端指针
    env.pDCFClient = pDCFClient; // 标记为已初始化
    env.need_init = false;

    // 更新对应的主/备映射表
    (*pDcfClients)[dcfClientKey] = env;

    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "InitDCFClient: Successfully initialized DCF client for key=%s", dcfClientKey.c_str());

    return 0;
}

int DCGrayscaleRoute::GetRouteEnvs(const char *pRouteProcess, vector<St_route_env> &routeEnvs)
{
    if (pRouteProcess == NULL || strlen(pRouteProcess) == 0)
    {
        DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "GetRouteEnvs: RouteProcess is null");
        return ERR_NOT_FIND_INMAPPARAM;
    }

    DCPerfGuard guard(&s_mutex);
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Begin GetRouteEnvs for route_process=%s", pRouteProcess);

    routeEnvs.clear();
    map<string, St_route_env> *pDcfClients = NULL;

    // 根据当前主备状态选择DcfClients数据源
    if (ACTIVE_DATA_MASTER == m_activeData)
    {
        pDcfClients = &m_Master_DcfClients;
    }
    else if (ACTIVE_DATA_SLAVE == m_activeData)
    {
        pDcfClients = &m_Slave_DcfClients;
    }

    string routeProcessStr = string(pRouteProcess);
    string clientCodePrefix = m_sSubscriber + "|" + routeProcessStr + "|";

    // 遍历DcfClients，找出与当前路由进程关联的环境
    for (map<string, St_route_env>::iterator it = pDcfClients->begin(); it != pDcfClients->end(); ++it)
    {
        // 解析 key = client_code|topology_name|env_code
        string key = it->first;

        // 检查 key 是否以 m_sSubscriber|routeProcessStr 开头
        if (key.find(clientCodePrefix) == 0)
        {
            // 提取 env_code
            size_t lastPos = key.find_last_of("|");
            if (lastPos != string::npos)
            {
                string envCode = key.substr(lastPos + 1);
                
                routeEnvs.push_back(it->second);
                DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "GetRouteEnvs: Found env for key=%s, env_code=%s, env_addr=%s, env_path=%s",
                         key.c_str(), envCode.c_str(), it->second.env_addr.c_str(), it->second.env_path.c_str());
            }
        }
    }

    if (routeEnvs.empty())
    {
        DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "GetRouteEnvs: No env found for route_process=%s", pRouteProcess);
        return ERR_NOT_FIND_RECORD;
    }

    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "GetRouteEnvs: Success, found %d envs for route_process=%s",
             routeEnvs.size(), pRouteProcess);

    return 0;
}

int DCGrayscaleRoute::LoadCrossClusterData(DCDBManer *pdbm, const char *pSubscriber)
{
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Begin load cross cluster data");

    int ret = 0;

    // 加载场景数据
    ret = LoadSceneData(pdbm, pSubscriber);
    if (ret >= 0)
    {
        // 加载规则数据
        ret = LoadRuleData(pdbm, pSubscriber);
    }

    if (ret >= 0)
    {
        // 加载条件数据
        ret = LoadConditionData(pdbm);
    }

    if (ret >= 0)
    {
        // 加载类型数据
        ret = LoadTypeData(pdbm);
    }

    if (ret >= 0)
    {
        // 加载环境数据
        ret = LoadEnvData(pdbm);
    }

    // 记录刷新结果
    LogRefreshResult(pdbm, ret);

    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "End load cross cluster data, ret = %d", ret);

    return ret;
}

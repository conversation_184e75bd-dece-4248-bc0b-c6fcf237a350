# 灰度路由系统测试数据使用说明

## 文件说明

本目录包含以下测试数据文件：

1. `GrayscaleRoute_test_data.sql` - 根据V25.0.0.5_ratedb_hjb.sql中定义的新表结构创建的测试数据
2. `GrayscaleRoute_legacy_test_data.sql` - 兼容旧版程序中使用的表结构的测试数据
3. `import_test_data.bat` - 用于导入测试数据的批处理脚本
4. `TEST_DATA_README.md` - 本说明文件

## 测试数据内容

### 新版表结构测试数据

1. **路由类型(dcf_route_type)**: 5条记录，包括计费号码、本地网、业务类型等路由类型
2. **环境定义(dcf_route_env)**: 3条记录，分别对应生产、测试和灰度环境
3. **路由场景(dcf_route_scene_his)**: 3条记录，描述不同的灰度测试场景
4. **路由规则(dcf_route_rule_his)**: 6条记录，定义了不同场景下的路由规则
5. **路由条件(dcf_route_condition_his)**: 11条记录，定义了路由规则的匹配条件
6. **路由刷新日志(dcf_route_refresh_log)**: 3条记录，记录了灰度路由规则的刷新历史

### 旧版表结构测试数据

1. **路由服务配置(route_service_cfg)**: 8条记录，定义了路由树结构和服务名称
2. **灰度用户组配置(grey_user_group_cfg)**: 6条记录，定义了特定灰度组中的用户

## 使用方法

### 导入测试数据

1. 编辑`import_test_data.bat`文件，根据实际情况修改MySQL连接信息
2. 双击运行`import_test_data.bat`，导入测试数据
3. 确认无错误信息后，测试数据导入完成

### 测试灰度路由系统

1. 使用以下命令运行测试程序：
   ```
   demo.exe GrayscaleRoute.sql.xml 127.0.0.1:9081 SUBSCRIBER ToRate
   ```

2. 根据需要修改测试参数：
   - GrayscaleRoute.sql.xml: SQL配置文件路径
   - 127.0.0.1:9081: 日志地址和端口
   - SUBSCRIBER: 订阅者编码
   - ToRate: 路由环节

### 测试数据匹配说明

本测试数据中的路由规则设计为匹配以下条件：

1. 西安本地网(910)的5G用户路由到`RatingService`服务，优先级10
2. 咸阳本地网(911)的5G用户路由到`RatingService`服务，优先级8
3. 其他地区的5G用户路由到`RatingService`服务，优先级5
4. 189号段的高价值账户用户路由到灰度环境，优先级10
5. 非189号段用户路由到生产环境，优先级5
6. 融合业务类型的高价值客户路由到灰度环境，优先级10

## 注意事项

1. 测试前请确保数据库连接正常
2. 根据实际情况修改导入脚本中的数据库连接信息
3. 测试后可能需要清理测试数据，避免影响生产环境
4. 旧版表结构数据与新版表结构数据可以并存，系统会根据配置选择使用哪种模式

## 测试场景示例

1. **基本路由测试**：使用不同的本地网码测试路由到不同服务
   ```
   routeParams["LatnID"] = "910";  // 西安本地网
   routeParams["OperType"] = "DA5G"; // 5G业务
   ```

2. **号段路由测试**：使用不同的号码前缀测试路由效果
   ```
   routeParams["BillingNum"] = "18900000001"; // 189号段
   ```

3. **账户条件测试**：测试不同账户值的路由效果
   ```
   routeParams["ACCTID"] = "1500000"; // 大额账户
   ```

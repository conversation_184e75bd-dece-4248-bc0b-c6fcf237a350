# DCGrayscaleRoute 密码解密功能改造

## 概述

为 `DCGrayscaleRoute::LoadEnvData` 函数添加了密码解密功能，支持明文和密文的自动识别与处理。

## 功能特性

### 1. 自动识别
- **明文密码**: 如果密码不符合密文格式，自动识别为明文并直接使用
- **密文密码**: 如果密码符合密文格式，自动进行解密处理

### 2. 密文格式识别规则
- 最后一位必须是数字（1-9）
- 字符串长度必须满足：`长度 >= 分割位置 + 26 + 1`
- 如果不满足以上条件，则认为是明文

### 3. 解密算法
1. 提取最后一位数字作为分割位置 `r`
2. 提取第一部分：`str.substr(0, r)`
3. 跳过26个随机字符
4. 提取第二部分：`str.substr(r + 26, length - 1 - (r + 26))`
5. 重新组合Base64字符串：`first_part + second_part`
6. 进行Base64解码
7. 进行URL解码

### 4. 错误处理
- Base64解码失败时，返回原始字符串作为明文
- URL解码中遇到无效字符时，跳过该字符继续处理
- 任何解密步骤失败都会回退到明文处理

## 代码修改

### 主要文件
- `src/DCGrayscaleRoute.cpp`: 添加解密函数和修改LoadEnvData函数

### 新增函数
1. `base64_decode()`: Base64解码函数
2. `url_decode()`: URL解码函数
3. `decrypt_password()`: 主解密函数

### 修改的函数
- `LoadEnvData()`: 在获取密码后调用解密函数

## 日志记录

添加了详细的DEBUG级别日志记录：
- 解密过程的各个步骤
- 明文/密文识别结果
- 错误处理信息
- 最终返回的密码（用于调试）

## 测试

### 测试程序
- `test_decrypt.cpp`: 独立的测试程序
- `compile_test.sh`: 编译脚本

### 测试用例
1. 明文密码测试
2. 空密码测试
3. 用户输入密文测试

## 向后兼容性

- 完全兼容现有的明文密码
- 不会影响现有功能
- 错误容错机制确保系统稳定性

## 使用示例

```cpp
// 明文密码
string plaintext = "password123";
string result = decrypt_password(plaintext);
// result = "password123" (原样返回)

// 密文密码（示例格式）
string encrypted = "ABC...XYZ3"; // 实际密文
string result = decrypt_password(encrypted);
// result = 解密后的明文密码
```

## 注意事项

1. 密码解密在每次LoadEnvData调用时进行
2. 解密后的密码存储在内存中，用于配置比较和DCF客户端连接
3. 日志中会记录密码内容，生产环境中可能需要调整日志级别
4. 解密失败时会自动回退到明文处理，确保系统可用性

## 编译要求

- C++11 或更高版本
- 需要包含 `<cctype>` 头文件用于字符类型判断 
﻿#ifndef __DCDB_HANDER_H__
#define __DCDB_HANDER_H__

#include "DCExchDB.h"
#include "DCPairList.h"
#include "DCDBManer.h"

#include <ace/Task.h>
#include <ace/Thread_Mutex.h>


class DCDBHander : public ACE_Task_Base
{
public:
	DCDBHander()
	{
	}
	~DCDBHander()
	{}

	int init(DCDBManer* dmdb,DCPairList *pairlist, int deleteflag, int iDelCheckUidFlag, int latnId = 0, int iTimeOut = 0);

	int run();

 	virtual int svc();

 	static void state(int done);

 	bool exit();

	int getSize()
	{
		return m_pairList->size();
	}
	
private:
	DCPairList    *m_pairList;
	DCExchDB       *m_exchdb;
	static int    m_ndone;
	time_t        m_checktime;
	int           m_deleteflag;
	int			  m_iTimeOut;
	bool          m_bexit;
	DCDBManer*    m_dmdb;
	int m_latnId;
	int m_iDelCheckUidFlag;

};

#endif

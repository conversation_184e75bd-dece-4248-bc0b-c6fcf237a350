/***********************************************************************
 * Module:  DCTQueue.cpp
 * Author:  any
 * Modified: 2013年8月14日 10:39:06
 * Purpose: Implementation of the class DCTQueue
 ***********************************************************************/

#include <ace/Guard_T.h>

template <class T>
int DCTQueue<T>::enqueue(const value_type & p)
{
	ACE_Guard<ACE_Thread_Mutex> guard(m_mutex);
	int ret = m_queue.enqueue_tail(p);
	m_cond.signal();
	return ret;
}

template <class T>
int DCTQueue<T>::dequeue(value_type & p, ACE_Time_Value * abstime)
{
	ACE_Guard<ACE_Thread_Mutex> guard(m_mutex);
	if(m_queue.size() == 0)
	{
		if(m_cond.wait(abstime) < 0)
		{
			return -1;
		}
		if(m_queue.size() == 0)
		{
			return -1;
		}
	}
	return m_queue.dequeue_head(p);
}

template <class T>
int DCTQueue<T>::try_dequeue(value_type & p)
{
	ACE_Guard<ACE_Thread_Mutex> guard(m_mutex);
	if(m_queue.size() == 0)
	{
		return -1;
	}
	return m_queue.dequeue_head(p);
}

template <class T>
size_t DCTQueue<T>::size()
{
	return m_queue.size();
}

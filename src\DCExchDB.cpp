#include <stdlib.h>
#include <unistd.h>
#include "DCExchDB.h"
#include "DCLogMacro.h"

DCExchDB::DCExchDB(DCDBManer	*dmdb)
{
	m_dmdb = dmdb;
	m_checktime = time(NULL);
}

DCExchDB::~DCExchDB()
{

}
int DCExchDB::MarkFileStateSum(long charge_files_id, const char* batchId, const char* state,int latnId,const char* oriState)
{
	int row = 0;
	char sqlName[32] = {0};
	sprintf(sqlName,"UpdateFileStateSum|%d",latnId);
	while (1)
	{
		UDBSQL* stmt = m_dmdb->GetSQL(sqlName);
		
		try
		{
			stmt->UnBindParam();
			stmt->BindParam(1, state);
			stmt->BindParam(2, charge_files_id);
			stmt->BindParam(3, batchId);
			stmt->BindParam(4, oriState);
			stmt->Execute();
			row = stmt->GetRowCount();
			stmt->Connection()->Commit();

		}
		catch(UDBException& e)
		{
			int _ret = DCExchDB::CheckDBState(m_dmdb, m_checktime);
			if (_ret != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reset db connection. retry the last option.");
				continue;
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "MarkFileState,charge_files_id[%ld],batchId[%s],state[%s],execption[%s]",charge_files_id, batchId, state, e.ToString());
				try
				{
					stmt->Connection()->Rollback();
				}
				catch(...){}
				return -1;
			}
		}

		break;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "MarkFileState [%s],charge_files_id[%ld],batchId[%s],row[%d]",state,charge_files_id, batchId, row);
	return row;
}

int DCExchDB::MarkFileState(long source_id, const char* state)
{
	while (1)
	{
		UDBSQL* stmt = m_dmdb->GetSQL("UpdateFileState");
		try
		{
			stmt->UnBindParam();
			stmt->BindParam(1, state);
			stmt->BindParam(2, source_id);
			stmt->Execute();
			stmt->Connection()->Commit();
		}
		catch(UDBException& e)
		{
			int _ret = DCExchDB::CheckDBState(m_dmdb, m_checktime);
			if (_ret != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "reconnect db, ret=%d", _ret);
				continue;
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "MarkFileState,source_id[%ld],state[%s],execption[%s]", source_id, state, e.ToString());
				try
				{
					stmt->Connection()->Rollback();
				}
				catch(...){}
				return -1;
			}
		}

		break;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "MarkFileState,source_id[%ld]",source_id);
	return 0;
}

int DCExchDB::MarkRecordState(string uuid,const char *state, const char* procId, int latnid,int mod, string billingNbr)
{
    int iRet = 0;
	char sql[256]={0};
	sprintf(sql,"InSertEptRecord|%d",latnid);
	long sourceId = atol(uuid.substr(1, 11).c_str());
    char szProcId[10] = { 0 };
    szProcId[0] = procId[0];
	while (1)
	{
		UDBSQL* stmt = m_dmdb->GetSQL(sql);
		if(stmt == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","GetSQL[InSertEptRecord] failed.");
			return -1;
		}

		try
		{
			stmt->UnBindParam();
			stmt->BindParam(1, sourceId);
			stmt->BindParam(2, uuid);
            stmt->BindParam(3, szProcId);
            stmt->BindParam(4, state);
            stmt->BindParam(5, mod);
            stmt->BindParam(6, billingNbr.c_str());
			stmt->Execute();

			stmt->Connection()->Commit();
			iRet = 0;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "InSertEptRecord,uuid:%s", uuid.c_str());
		}
		catch (UDBException& e)
		{
			std::string sql;
			stmt->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "sql[%s]", sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "InSertEptRecord,execption[%s]", e.ToString());
			try { stmt->Connection()->Rollback(); }
			catch (...) {}
			DCExchDB::CheckDBState(m_dmdb, m_checktime);
			return -1;
		}

		break;
	}

	return iRet;
}

int DCExchDB::ClearKey(string uuid)
{
	int iRetryCount = 0;
	while (1)
	{
		UDBSQL* stmt = m_dmdb->GetSQL("DeleteRedisKey");
		try
		{
			stmt->UnBindParam();
			stmt->BindParam(1, uuid);
			stmt->Execute();
			stmt->Connection()->Commit();
		}
		catch(UDBException& e)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "DeleteRedisKey,uuid[%s],execption[%s]",uuid.c_str(),e.ToString());
			int _ret = DCExchDB::CheckDBState(m_dmdb, m_checktime);
			if (_ret != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "reconnect db, ret=%d", _ret);
				continue;
			}
			else
			{
				if (iRetryCount < 1)
				{
					iRetryCount++;
					continue;
				}
				return -1;
			}
		}

		break;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "DeleteRedisKey,uuid[%s]",uuid.c_str());
	return 0;
}

int DCExchDB::IsDeleteTimeoutKey(string uuid)
{
	while (1)
	{
		UDBSQL* query = m_dmdb->GetSQL("QueryRedisKey");
		string value = "";
		try
		{
			query->UnBindParam();
			query->BindParam(1, uuid.c_str());
			query->Execute();
			if(query->Next())
			{
				query->GetValue(1,value);

				DCBIZLOG(DCLOG_LEVEL_FATAL,0, "", "redis key[%s]  record:%s",uuid.c_str(),value.c_str());
				if(value[0]=='C')
				{
					return 1;
				}
				else if(value[0]=='D')
				{
					DCBIZLOG(DCLOG_LEVEL_FATAL,0, "", "redis key[%s] is not timeout",uuid.c_str());
					return 2;
				}
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_FATAL,0, "", "redis key[%s] has no record",uuid.c_str());
				return 3;
			}
		}
		catch(UDBException& e)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "DeleteRedisKey,uuid[%s],execption[%s]",uuid.c_str(),e.ToString());
			int _ret = DCExchDB::CheckDBState(m_dmdb, m_checktime);
			if (_ret != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", _ret);
				continue;
			}
			else
			{
				return -1;
			}
		}

		break;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "DeleteRedisKey,uuid[%s]",uuid.c_str());
	return 0;
}

int DCExchDB::IsDeleteKey(string uuid)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "DeleteRedisKey,uuid[%s]",uuid.c_str());
	while (1)
	{
		UDBSQL* query = m_dmdb->GetSQL("QueryRedisKey");
		string value="";
		try
		{
			query->UnBindParam();
			query->BindParam(1, uuid.c_str());
			query->Execute();
			if(query->Next())
			{
				return 1;
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_INFO,0, "", "redis key[%s] has no record",uuid.c_str());
				return -1;
			}
		}
		catch(UDBException& e)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "DeleteRedisKey,uuid[%s],execption[%s]",uuid.c_str(),e.ToString());
			int _ret = DCExchDB::CheckDBState(m_dmdb, m_checktime);
			if (_ret != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", _ret);
				continue;
			}
			else
			{
				return -1;
			}
		}

		break;
	}

	return 0;

}

int DCExchDB::RecordLogRating(const STLogRating& log)
{
	/*
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","InsertLogRating,source_id[%ld]",log.source_id);
	UDBSQL* stmt = m_dmdb->GetSQL("InsertLogRating");
    try
    {
		stmt->UnBindParam();
		stmt->BindParam(1, 	log.source_id);
		stmt->BindParam(2, 	log.parent_source_id);
		stmt->BindParam(3, 	log.latn_id);
		stmt->BindParam(4, 	log.normal_nr);
		stmt->BindParam(5, 	log.invalid_nr);
		stmt->BindParam(6, 	log.abnormal_nr);
		stmt->BindParam(7, 	log.nouser_nr);
		stmt->BindParam(8, 	log.dual_nr);
		stmt->BindParam(9, 	log.charge_nr);
		stmt->BindParam(10, log.ori_charge);
		stmt->BindParam(11, log.disct_charge);
		stmt->BindParam(12, "Z");
		stmt->BindParam(13, log.total_nr);
		stmt->Execute();
		stmt->Connection()->Commit();
    }
    catch(UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "InsertLogRating, source_id[%ld], execption[%s],",log.source_id, e.ToString());
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "LogRate:source_id[%ld],parent_source_id[%ld],latn_id[%d],"
						  "normal_nr[%ld],invalid_nr[%ld],abnormal_nr[%ld],nouser_nr[%ld],dual_nr[%ld]"
						  "charge_nr[%ld],ori_charge[%ld],disct_charge[%ld],total_nr[%ld]", \
						  log.source_id,\
						  log.parent_source_id,\
						  log.latn_id,\
						  log.normal_nr,\
						  log.invalid_nr,\
						  log.abnormal_nr,\
						  log.nouser_nr,\
						  log.dual_nr,\
						  log.charge_nr,\
						  log.ori_charge,\
						  log.disct_charge,\
						  log.total_nr);
		stmt->Connection()->Rollback();
		return -1;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "LogRate:source_id[%ld],parent_source_id[%ld],latn_id[%d],"
						  "normal_nr[%ld],invalid_nr[%ld],abnormal_nr[%ld],nouser_nr[%ld],dual_nr[%ld]"
						  "charge_nr[%ld],ori_charge[%ld],disct_charge[%ld],total_nr[%ld]", \
						  log.source_id,\
						  log.parent_source_id,\
						  log.latn_id,\
						  log.normal_nr,\
						  log.invalid_nr,\
						  log.abnormal_nr,\
						  log.nouser_nr,\
						  log.dual_nr,\
						  log.charge_nr,\
						  log.ori_charge,\
						  log.disct_charge,\
						  log.total_nr);
	*/
	return 0;
}

int DCExchDB::MarkTaskToDeal(long taskid)
{
	int row = 0;
	while (1)
	{
		UDBSQL* stmt = m_dmdb->GetSQL("UpdateTaskState");
		
		try
		{
			stmt->UnBindParam();
			stmt->BindParam(1, taskid);
			stmt->Execute();
			row = stmt->GetRowCount();
			stmt->Connection()->Commit();
		}
		catch(UDBException& e)
		{
			std::string sql;
			stmt->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "sql[%s]",sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "MarkTaskToDeal,task_id[%ld],execption[%s]",taskid, e.ToString());
			int _ret = DCExchDB::CheckDBState(m_dmdb, m_checktime);
			if (_ret != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", _ret);
				continue;
			}
			else
			{
				try
				{
					stmt->Connection()->Rollback();
				}
				catch(...){}
				return -1;
			}
		}

		break;
	}
	
	return row;
}

int DCExchDB::MarkTaskDealingEdc(long taskid)
{
	int row = 0;
	while (1)
	{
		UDBSQL* stmt = m_dmdb->GetSQL("UpdateTaskStateEdc");
		
		try
		{
			stmt->UnBindParam();
			stmt->BindParam(1, taskid);
			stmt->Execute();
			row = stmt->GetRowCount();
			stmt->Connection()->Commit();
		}
		catch(UDBException& e)
		{
			std::string sql;
			stmt->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "sql[%s]",sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "UpdateTaskStateEdc,task_id[%ld],execption[%s]",taskid, e.ToString());
			int _ret = DCExchDB::CheckDBState(m_dmdb, m_checktime);
			if (_ret != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", _ret);
				continue;
			}
			else
			{
				try
				{
					stmt->Connection()->Rollback();
				}
				catch(...){}
				return -1;
			}
		}

		break;
	}
	
	return row;
}

int DCExchDB::MarkTaskToException(long taskid)
{
	while (1)
	{
		UDBSQL* stmt = m_dmdb->GetSQL("UpdateTaskToExp");
		try
		{
			stmt->UnBindParam();
			stmt->BindParam(1, taskid);
			stmt->Execute();

			stmt->Connection()->Commit();
		}
		catch(UDBException& e)
		{
			std::string sql;
			stmt->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "sql[%s]",sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "MarkTaskToException,task_id[%ld],execption[%s]", taskid, e.ToString());
			int _ret = DCExchDB::CheckDBState(m_dmdb, m_checktime);
			if (_ret != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", _ret);
				continue;
			}
			else
			{
				try
				{
					stmt->Connection()->Rollback();
				}
				catch(...){}
				return -1;
			}
		}

		break;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "MarkTaskToException,task_id[%ld]",taskid);
	return 0;
}

int DCExchDB::MarkTaskToExceptionEdc(long taskid)
{
	while (1)
	{
		UDBSQL* stmt = m_dmdb->GetSQL("UpdateTaskToExpEdc");
		try
		{
			stmt->UnBindParam();
			stmt->BindParam(1, taskid);
			stmt->Execute();

			stmt->Connection()->Commit();
		}
		catch(UDBException& e)
		{
			std::string sql;
			stmt->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "sql[%s]",sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "MarkTaskToExceptionEdc, task_id[%ld],execption[%s]",taskid, e.ToString());
			int _ret = DCExchDB::CheckDBState(m_dmdb, m_checktime);
			if (_ret != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", _ret);
				continue;
			}
			else
			{
				try
				{
					stmt->Connection()->Rollback();
				}
				catch(...){}
				return -1;
			}
		}

		break;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "MarkTaskToExceptionEdc, task_id[%ld]",taskid);
	return 0;
}

int DCExchDB::MarkTaskToDone(long taskid)
{
	while (1)
	{
		UDBSQL* stmt = m_dmdb->GetSQL("DeleteTask");
		try
		{
			stmt->UnBindParam();
			stmt->BindParam(1, taskid);
			stmt->Execute();
			stmt->Connection()->Commit();
		}
		catch(UDBException& e)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "MarkTaskToDone,task_id[%ld],execption[%s]",taskid, e.ToString());
			int _ret = DCExchDB::CheckDBState(m_dmdb, m_checktime);
			if (_ret != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", _ret);
				continue;
			}
			else
			{
				try
				{
					stmt->Connection()->Rollback();
				}
				catch(...){}
				return -1;
			}
		}

		break;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "MarkTaskToDone,task_id[%ld]",taskid);
	return 0;
}

int DCExchDB::MarkTaskToDoneSum(long taskid, const char* batchId, int latnId)
{
	char sqlname[32] = {0};
	sprintf(sqlname,"DeleteTaskSum|%d",latnId);
	while (1)
	{
		UDBSQL* stmt = m_dmdb->GetSQL(sqlname);
		try
		{
			stmt->UnBindParam();
			stmt->BindParam(1, taskid);
			stmt->BindParam(2, batchId);
			stmt->Execute();
			stmt->Connection()->Commit();
		}
		catch(UDBException& e)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "MarkTaskToDoneSum,charge_files_details_id[%ld], batchId[%s], execption[%s]",taskid, batchId, e.ToString());
			int _ret = DCExchDB::CheckDBState(m_dmdb, m_checktime);
			if (_ret != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", _ret);
				continue;
			}
			else
			{
				try
				{
					stmt->Connection()->Rollback();
				}
				catch(...){}
				return -1;
			}
		}

		break;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "MarkTaskToDoneSum,charge_files_details_id[%ld], batchId[%s]",taskid, batchId);
	return 0;
}

int DCExchDB::MarkTaskToDoneEdc(long taskid)
{
	while (1)
	{
		UDBSQL* stmt = m_dmdb->GetSQL("DeleteTaskEdc");
		try
		{
			stmt->UnBindParam();
			stmt->BindParam(1, taskid);
			stmt->Execute();
			stmt->Connection()->Commit();
		}
		catch(UDBException& e)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "MarkTaskToDoneEdc, task_id[%ld],execption[%s]",taskid, e.ToString());
			int _ret = DCExchDB::CheckDBState(m_dmdb, m_checktime);
			if (_ret != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", _ret);
				continue;
			}
			else
			{
				try
				{
					stmt->Connection()->Rollback();
				}
				catch(...){}
				return -1;
			}
		}

		break;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "MarkTaskToDoneEdc, task_id[%ld]",taskid);
	return 0;
}

int DCExchDB::RecordUidAll(int latnId, long lines, long sourceId, long taskId)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","insert tsk_bill_filerecord_audit,sourceId[%ld]",sourceId);
	char sql[256]={0};
	sprintf(sql,"InSertUidAll");

	while (1)
	{
		UDBSQL* stmt = m_dmdb->GetSQL(sql);
		if(stmt == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","GetSQL[InSertUidAll] failed.");
			return -1;
		}

		try
		{
			stmt->UnBindParam();
			stmt->BindParam(1, lines);
			stmt->BindParam(2, sourceId);
			stmt->BindParam(3, taskId);
			stmt->Execute();
			stmt->Connection()->Commit();
		}
		catch(UDBException& e)
		{
			std::string sql;
			stmt->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR,0, "", "sql[%s]",sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "InSertUidAll,execption[%s]" ,e.ToString());
			// 捕获主键冲突异常
			int iErrCode = e.GetErrorCode();
			if (4001 == iErrCode)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "InSertUidAll,Duplicate entry [%d]", iErrCode);
				return iErrCode;
			}
			try { stmt->Connection()->Rollback(); }
			catch(...){}
			DCExchDB::CheckDBState(m_dmdb, m_checktime);
			return -1;
		}

		break;
	}
	
	return 0;
}

int DCExchDB::CheckDBState(DCDBManer* const _db, time_t& tLastResetTime, bool bForceReset)
{
	bool bReconnect = ( UDBS_DB_UNLINK == _db->GetConnection("ORA_Main")->State() ); //初始状态为发生数据库操作异常时的状态，
	int nRet        = -1;
	int nRetryTime  = 0;
	time_t cursec = time(NULL);//每五分钟自动重连
	if((tLastResetTime+300) < cursec  || bReconnect )
	{
		while (++nRetryTime <= 12000) //尝试重连20小时
		{
			nRet = _db->CheckReset();
			if (0 != nRet)
			{
				bReconnect = true;
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "try to reconnect db, ret=%d" , nRet);
				sleep(30);
			}
			else
			{
				break;
			}
		}
		tLastResetTime = cursec;
	}
	else
	{
		nRet = _db->FastReset();
	}

	if (bReconnect && nRet == 0) //尝试过重连且重连成功
	{
		return 1;
	}
	return nRet;
}

int DCExchDB::GetTraceNumFlag(const char* szCER, const char* szOID)
{
	char sc_num[64] = {0};
	strcpy(sc_num, szCER);
	if ('\0' == *sc_num)
	{
		strcpy(sc_num, szOID);
		if ( '\0' == *sc_num )
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "traced number [CER/OID] was empty, GetTraceNumFlag failed.");
			return 0;
		}
		DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "OID=%s", sc_num);
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "CER=%s", sc_num);
	}

	//简单号码整理
	char* ph = (char*)sc_num;
	while(*ph && (*ph < '0' || *ph > '9')){ ph++; }
	if(ph[0]=='8' && ph[1]=='6') ph += 2;
	if(ph!=(char*)sc_num) memmove(sc_num, ph,strlen(ph)+1);

	std::string sNum = sc_num;
	DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "DCTaskService::GetTraceNumFlag sqlcode=ParTraceNumberQuery sNum=%s", sc_num);

	UDBSQL* pQueryTrace = m_dmdb->GetSQL("ParTraceNumberQuery");
	if(pQueryTrace == NULL)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "getSQL[ParTraceNumberQuery] error");
		return 0;
	}

	pQueryTrace->UnBindParam();
	pQueryTrace->BindParam(1, sc_num);
	try
	{
		pQueryTrace->Execute();
		if(pQueryTrace->Next())
		{
			DCBIZLOG(DCLOG_LEVEL_INFO,0,"","DCFmtComm::GetTraceNumFlag number[%s] traceFlag = 1", sc_num);
			return 1;
		}
	}
	catch (UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "UDBException error[%s]!", e.ToString());
		return 0;
	}

	DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "not found number[%s] in sqlcode=ParTraceNumberQuery, traceFlag = 0", sc_num);
	return 0;
}

int DCExchDB::UpdateUidAll(int latnId, long lines, long sourceId, long taskId, int state)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","update tsk_bill_filerecord_audit,sourceId[%ld]",sourceId);
	char sState[4] = {0};
	char sql[256]={0};
	sprintf(sql,"UpdateUidAll");

	sprintf(sState, "%d", state);

	while (1)
	{
		UDBSQL* stmt = m_dmdb->GetSQL(sql);
		if(stmt == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","GetSQL[UpdateUidAll] failed.");
			return -1;
		}

		try
		{
			stmt->UnBindParam();
			stmt->BindParam(1, sState);
			stmt->BindParam(2, lines);
			stmt->BindParam(3, sourceId);
			stmt->BindParam(4, taskId);
			stmt->Execute();
			stmt->Connection()->Commit();
		}
		catch(UDBException& e)
		{
			std::string sql;
			stmt->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR,0, "", "sql[%s]",sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "UpdateUidAll,execption[%s]" ,e.ToString());
			try { stmt->Connection()->Rollback(); }
			catch(...){}
			DCExchDB::CheckDBState(m_dmdb, m_checktime);
			return -1;
		}

		break;
	}
	
	return 0;
}

int DCExchDB::SpecialBackUp(const STTask* pTask)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","update SpecialBackUpAudit,sourceId[%ld]", pTask->source_id);
//	char sState[4] = {0};
	char sql[256]={0};

	string filename = pTask->source_name;

	std::size_t found = filename.find_last_of("/");
	string filepath = filename.substr(0, found+1);
//	string filenamenopath = filename.substr(found+1);
	memset(sql, 0x00, sizeof(sql));
	sprintf(sql,"SpecialBackUpAudit");

	while (1)
	{
		UDBSQL* stmt = m_dmdb->GetSQL(sql);
		if(stmt == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","GetSQL[SpecialBackUpAudit] failed.");
			return -1;
		}

		try
		{
			stmt->UnBindParam();
			//		stmt->BindParam(1, batchId);
//			stmt->BindParam(1, nChargeFilesID);
			stmt->BindParam(1, filepath.c_str());
			stmt->BindParam(2, filename.c_str());
			stmt->BindParam(3, pTask->source_id);
			stmt->BindParam(4, pTask->batch_id);
			stmt->BindParam(5, pTask->lines);
			stmt->BindParam(6, pTask->oper_list_id);
			stmt->Execute();
			stmt->Connection()->Commit();
		}
		catch(UDBException& e)
		{
			std::string sql;
			stmt->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR,0, "", "sql[%s]",sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "UpdateUidAll,execption[%s]" ,e.ToString());
			try { stmt->Connection()->Rollback(); }
			catch(...){}
			DCExchDB::CheckDBState(m_dmdb, m_checktime);
			return -1;
		}

		break;
	}
	
	return 0;
}

int DCExchDB::MarkTaskToDup(long taskid, const char* state)
{
	while (1)
	{
		UDBSQL* stmt = m_dmdb->GetSQL("MarkTaskToDup");
		try
		{
			stmt->UnBindParam();
			stmt->BindParam(1, state);
			stmt->BindParam(2, taskid);
			stmt->Execute();

			stmt->Connection()->Commit();
		}
		catch(UDBException& e)
		{
			std::string sql;
			stmt->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "sql[%s]",sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "MarkTaskToDup,task_id[%ld],execption[%s]", taskid, e.ToString());
			int _ret = DCExchDB::CheckDBState(m_dmdb, m_checktime);
			if (_ret != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", _ret);
				continue;
			}
			else
			{
				try
				{
					stmt->Connection()->Rollback();
				}
				catch(...){}
				return -1;
			}
		}

		break;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "MarkTaskToDup,task_id[%ld]",taskid);
	return 0;
}



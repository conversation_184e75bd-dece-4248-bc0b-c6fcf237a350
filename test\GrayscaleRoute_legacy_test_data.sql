/*
 * 灰度路由系统旧版表测试数据
 * 用于兼容旧版程序中使用的表结构
 * 生成日期: 2025-05-21
 */

-- 清除测试数据
DELETE FROM route_service_cfg WHERE ID IN (1, 2, 3, 4, 5, 6, 7, 8);
DELETE FROM grey_user_group_cfg WHERE GroupId IN (1, 2, 3);

-- 插入路由服务配置数据
INSERT INTO route_service_cfg (ID, ParentId, PRIORITY, Subscriber, RouteProcess, RouteCode, RouteValueA, RouteValueB, ServiceName, RouteStatus) VALUES 
(1, -1, 10, 'SUBSCRIBER', 'ToRate', 'BILLING_NUM', '189', '', 'RatingService1', 1),
(2, 1, 8, 'SUBSCRIBER', 'ToRate', 'LATN_ID', '910', '', 'RatingService1_910', 1),
(3, 1, 6, 'SUBSCRIBER', 'ToRate', 'LATN_ID', '911', '', 'RatingService1_911', 1),
(4, -1, 5, 'SUBSCRIBER', 'ToRate', 'BILLING_NUM', '177', '', 'RatingService2', 1),
(5, 4, 10, 'SUBSCRIBER', 'ToRate', 'OPER_TYPE', 'DA4G', '', 'RatingService2_DA4G', 1),
(6, -1, 1, 'SUBSCRIBER', 'ToRate', '', '', '', 'DefaultRatingService', 1),
(7, -1, 10, 'SUBSCRIBER', 'ToProcess', 'ACCT_ID', '123456', '', 'ProcessService1', 1),
(8, -1, 5, 'SUBSCRIBER', 'ToProcess', '', '', '', 'DefaultProcessService', 1);

-- 插入灰度用户组配置数据
INSERT INTO grey_user_group_cfg (GroupId, Value, RouteStatus) VALUES 
(1, '18900000001', 1),
(1, '18900000002', 1),
(1, '18900000003', 1),
(2, '18900000100', 1),
(2, '18900000101', 1),
(3, '123456', 1);

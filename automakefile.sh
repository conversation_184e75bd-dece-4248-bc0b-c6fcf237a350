#!/bin/sh
cd ./src
#checkpath
if [ -d "../release/bin/" ];then
	echo "release path exist"
else
	`mkdir -p ../release/bin/`
	echo "create release dir"
fi

#make&move
echo "-----------------------------"
echo "-----------------------------"
echo "----------MakeBegin----------"
echo "-----------------------------"
echo "-----------------------------"
echo "Current PWD:`pwd`"
gmake -f makefile clean
gmake -f makefile all
mv ./MsgExch ../release/bin/MsgExch

#MD5 check
MD5VAL=`md5sum ../release/bin/MsgExch`
echo "MD5-VALUE"
echo $MD5VAL

echo "-----------------------------"
echo "-----------MakeEnd-----------"
echo "-----------------------------"


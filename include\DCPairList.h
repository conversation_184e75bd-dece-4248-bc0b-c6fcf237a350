/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目�?
*All rights reserved.
*
*Filename�?
*		DCRentLoadUser.h
*Indentifier�?
*
*Description�?
*		月租用户数据加载�?
*Version�?
*		V1.0
*Author:
*		
*Finished�?
*		
*History:
********************************************/

#ifndef _DCPAIR_LIST_H_
#define _DCPAIR_LIST_H_

#include <stdio.h>
#include <string>
#include <errno.h>
#include <pthread.h>
#include <map>
#include <vector>
//#include <list>
#include <sys/time.h>
#include "DCTQueue.h"
#include "DCDBManer.h"
#include "DCLogMacro.h"
#include "DCFLocalClientNew.h"
#include "DCExchDB.h"
#include "DCPerfStatistic.h"
#include "DCExchCfg.h"
#include <fstream>

using namespace dcf_new;


using namespace std;

#define DIFFTIME(Tb, Ta) ((((Tb)>>24) - ((Ta)>>24))*1000000 + ((Tb)&0xFFFFFF) - ((Ta)&0xFFFFFF))

struct SMsgPair
{
	string 			uuid;
	string			servName;
	string 			sendMsg;
	string			billingNbr;
    string          batchId;
	int             endflag;//文件结束标识
	long            sourceid;
	int             recvcont;//单个文件接收消息�?
	int             totalline;//单个文件总行�?
	int             latnid;
	int             timeoutcount;
	long taskId;
	struct timeval 	begin;//发送CCR时间
    char            procId[10];    // 处理环节

	SMsgPair()
	{
		uuid = "";
		servName = "";
		sendMsg = "";
		billingNbr = "";
        batchId = "";
		endflag = 0;
		sourceid = 0;
		recvcont = 0;
		totalline = 0;
		latnid = 0;
		timeoutcount = 0;
		taskId = 0;
        memset(procId, 0, sizeof(procId));
	};
};

class DCPairList
{
public:
	DCPairList(DCDBManer *dmdb,dcf_new::DCFLocalClient *client,int mod = 0);
	virtual ~DCPairList();

public:

	/****************************************************************************************
	*@input

	*@output

	*@return
	队列大小

	*@description		获取队列大小
	******************************************************************************************/
	int size();



	/****************************************************************************************
	*@input
	pMsg:序列号值对

	*@output

	*@return
	0 : 入列成功

	*@description		入队�?已满则阻�?
	******************************************************************************************/
	int in(const string uuid,const SMsgPair sMsg);



	/****************************************************************************************
	*@input
	timeout : 超时时间(ms)

	*@output
	pMsg : 序列号值对

	*@return
	0 : 出列成功

	*@description		出队�?为空阻塞)
	******************************************************************************************/
	int out(const string uuid,SMsgPair &sMsg);


	int modify(int totalline,long source_id);

	/****************************************************************************************
	*@input

	*@output

	*@return

	*@description		清空超时值对
	******************************************************************************************/
	void clearTimeOut(long timeOut, vector<SMsgPair>& out);

	int get(const string uuid,SMsgPair &sMsg);

	/*int push(const string uuid);
	string &pop();
	int popfront();
	int empty();*/

	int enQueue(const string uuid,string ipErrorCode);
	int deQueue(string &uuid);
	int trydeQueue(string &uuid);
	int quesize();
	void outInfoFile();
	int dealErrorCode(string ipErrorCode);
	void SetQuit();
    int GetQuit();

protected:

	int lock();
	int unlock();

	//int lockL();
	//int unlockL();

public:
	std::map <long,SMsgPair> m_fileStatic;
	int lockM();
	int unlockM();
protected:
	pthread_mutex_t 		m_mutex;	
	pthread_mutex_t 		m_mutexlist;		
	pthread_mutex_t 		m_mutexfile;
	//list<string> m_UidList;
	map<string, SMsgPair>	m_PairMap;
	DCExchDB*               m_exchdb;
	dcf_new::DCFLocalClient*    m_client;
	DCPerfTimeStats  			m_tstat;         //性能统计
	int                     m_mod;
	DCTQueue<string>        m_Quuid;
	SExchCfg*               m_cfg;
	map<string,int>         m_iPCode;
	int                     m_codeTimes;
	string                  lastErrorCode;
	time_t                  m_checktime;
	int                     readyExit;
	long                    m_lnTimeoutOld;//记录最后一次清理非超时话单时的秒级时间�?
	long                    m_lnTimeoutCountOld;
};

#endif


/*
 * 灰度路由系统测试数据
 * 根据V25.0.0.5_ratedb_hjb.sql表结构创建
 * 生成日期: 2025-05-21
 */

-- 清除测试数据
DELETE FROM dcf_route_scene_his WHERE scene_id IN (1001, 1002, 1003);
DELETE FROM dcf_route_rule_his WHERE scene_id IN (1001, 1002, 1003);
DELETE FROM dcf_route_condition_his WHERE rule_id IN (2001, 2002, 2003, 2004, 2005, 2006);
DELETE FROM dcf_route_type WHERE route_type_id IN (101, 102, 103, 104, 105);
DELETE FROM dcf_route_env WHERE env_id IN (501, 502, 503);
DELETE FROM dcf_route_refresh_log WHERE log_id > 0;

-- 插入路由类型数据
INSERT INTO dcf_route_type (route_type_id, route_type_code, route_type_name, center_id) VALUES 
(101, "BILLING_NUM", "计费号码", 30001),
(102, "LATN_ID", "本地网", 30001),
(103, "OPER_TYPE", "业务类型", 30001),
(104, "ACCT_ID", "账户ID", 30002),
(105, "CUST_ID", "客户ID", 30002);

-- 插入环境定义数据
INSERT INTO dcf_route_env (env_id, center_id, env_code, env_type, cluster_code, env_name, app_type, env_addr, env_path, user_name, password, create_staff, create_date, update_staff, update_date) VALUES 
(501, 30001, "ENV_PROD", "physical", "CLUSTER_001", "生产环境", "dcf", "192.168.10.100:2181/zk_root", "/dcf/rating", "zkuser", "zkpassword", "ADMIN", "2025-04-01 10:00:00", "ADMIN", "2025-04-01 10:00:00"),
(502, 30001, "ENV_TEST", "cloud", "CLUSTER_002", "测试环境", "dcf", "192.168.10.200:2181/zk_root", "/dcf/rating", "zkuser", "zkpassword", "ADMIN", "2025-04-01 11:00:00", "ADMIN", "2025-04-01 11:00:00"),
(503, 30002, "ENV_GRAY", "hybrid", "CLUSTER_003", "灰度环境", "dcf", "192.168.10.300:2181/zk_root", "/dcf/rating", "zkuser", "zkpassword", "ADMIN", "2025-04-01 12:00:00", "ADMIN", "2025-04-01 12:00:00");

-- 插入路由场景历史数据
INSERT INTO dcf_route_scene_his (his_scene_id, scene_id, scene_name, version, center_id, client_code, app_type, env_type, upgrade_type, flow_json, `desc`, status, approval_status, create_staff, create_date, approver, approval_date, is_sms_notify, operator_staff, operator_date) VALUES 
(10001, 1001, "陕西5G基础套餐灰度测试", "V1.0.0", 30001, "OCS_CLIENT", "dcf", "physical", "canary", '{\"nodes\":[{\"id\":\"node1\",\"type\":\"start\"},{\"id\":\"node2\",\"type\":\"process\"},{\"id\":\"node3\",\"type\":\"end\"}],\"edges\":[{\"source\":\"node1\",\"target\":\"node2\"},{\"source\":\"node2\",\"target\":\"node3\"}]}', "陕西5G基础套餐灰度发布测试场景", 1, 2, "ADMIN", "2025-05-01 09:00:00", "MANAGER", "2025-05-01 10:00:00", 1, "OPERATOR", "2025-05-01 11:00:00"),
(10002, 1002, "陕西5G增值业务灰度测试", "V1.0.0", 30001, "OCS_CLIENT", "dcf", "cloud", "bluegreen", '{\"nodes\":[{\"id\":\"node1\",\"type\":\"start\"},{\"id\":\"node2\",\"type\":\"process\"},{\"id\":\"node3\",\"type\":\"end\"}],\"edges\":[{\"source\":\"node1\",\"target\":\"node2\"},{\"source\":\"node2\",\"target\":\"node3\"}]}', "陕西5G增值业务灰度发布测试场景", 1, 2, "ADMIN", "2025-05-02 09:00:00", "MANAGER", "2025-05-02 10:00:00", 1, "OPERATOR", "2025-05-02 11:00:00"),
(10003, 1003, "陕西融合套餐灰度测试", "V1.0.0", 30002, "OCS_CLIENT", "dcf", "hybrid", "roll", '{\"nodes\":[{\"id\":\"node1\",\"type\":\"start\"},{\"id\":\"node2\",\"type\":\"process\"},{\"id\":\"node3\",\"type\":\"end\"}],\"edges\":[{\"source\":\"node1\",\"target\":\"node2\"},{\"source\":\"node2\",\"target\":\"node3\"}]}', "陕西融合套餐灰度发布测试场景", 1, 2, "ADMIN", "2025-05-03 09:00:00", "MANAGER", "2025-05-03 10:00:00", 1, "OPERATOR", "2025-05-03 11:00:00");

-- 插入路由规则历史数据
INSERT INTO dcf_route_rule_his (his_rule_id, rule_id, rule_name, version, scene_id, env_code, dispatch_path, client_code, topology_name, priority) VALUES 
(20001, 2001, "西安地区5G基础套餐路由规则", "V1.0.0", 1001, "ENV_PROD", "RatingService", "OCS_CLIENT", "TOPOLOGY_RATING", 10),
(20002, 2002, "咸阳地区5G基础套餐路由规则", "V1.0.0", 1001, "ENV_PROD", "RatingService", "OCS_CLIENT", "TOPOLOGY_RATING", 8),
(20003, 2003, "其他地区5G基础套餐路由规则", "V1.0.0", 1001, "ENV_TEST", "RatingService", "OCS_CLIENT", "TOPOLOGY_RATING", 5),
(20004, 2004, "5G高价值用户增值业务路由规则", "V1.0.0", 1002, "ENV_GRAY", "RatingService", "OCS_CLIENT", "TOPOLOGY_RATING", 10),
(20005, 2005, "5G普通用户增值业务路由规则", "V1.0.0", 1002, "ENV_PROD", "RatingService", "OCS_CLIENT", "TOPOLOGY_RATING", 5),
(20006, 2006, "融合套餐路由规则", "V1.0.0", 1003, "ENV_GRAY", "RatingService", "OCS_CLIENT", "TOPOLOGY_RATING", 10);

-- 插入路由条件历史数据
INSERT INTO dcf_route_condition_his (his_cond_id, cond_id, rule_id, version, group_id, route_type_id, operator, value, `desc`) VALUES 
(30001, 3001, 2001, "V1.0.0", 1, "102", "=", "910", "西安本地网"),
(30002, 3002, 2001, "V1.0.0", 1, "103", "=", "DA5G", "5G业务类型"),
(30003, 3003, 2002, "V1.0.0", 1, "102", "=", "911", "咸阳本地网"),
(30004, 3004, 2002, "V1.0.0", 1, "103", "=", "DA5G", "5G业务类型"),
(30005, 3005, 2003, "V1.0.0", 1, "102", "NOT LIKE", "91%", "非西安咸阳本地网"),
(30006, 3006, 2003, "V1.0.0", 1, "103", "=", "DA5G", "5G业务类型"),
(30007, 3007, 2004, "V1.0.0", 1, "101", "LIKE", "189%", "189号段用户"),
(30008, 3008, 2004, "V1.0.0", 2, "104", ">", "1000000", "大额账户"),
(30009, 3009, 2005, "V1.0.0", 1, "101", "NOT LIKE", "189%", "非189号段用户"),
(30010, 3010, 2006, "V1.0.0", 1, "103", "=", "FUSION", "融合业务类型"),
(30011, 3011, 2006, "V1.0.0", 1, "105", ">", "50000", "高价值客户");

-- 插入路由刷新日志
INSERT INTO dcf_route_refresh_log (log_id, refresh_time, center_id, module_name, pid, change_info, success) VALUES 
(40001, "2025-05-20 08:00:00", "30001", "DCGrayscaleRoute", "12345", "刷新灰度路由规则，场景数量：3，规则数量：6", "success"),
(40002, "2025-05-20 12:00:00", "30001,30002", "DCGrayscaleRoute", "12346", "更新环境配置，环境数量：3", "success"),
(40003, "2025-05-21 08:00:00", "30001", "DCGrayscaleRoute", "12347", "更新路由条件，条件数量：11", "success");

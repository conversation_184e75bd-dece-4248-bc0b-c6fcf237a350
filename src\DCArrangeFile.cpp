#include <signal.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <signal.h>
#include "DCLogMacro.h"
#include "DCParseXml.h"
#include "DCExchCfg.h"
#include "DCTaskSum.h"
void sig_deal_func(int sig)
{
	switch(sig)
	{
		case SIGTERM:
		case SIGINT:
		case SIGQUIT:
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","get quit sig");
			// stop task
			DCTaskSum::state(2);
		}
	}
}
int main(int argc, char*argv[])
{
	if(argc < 3)
	{
		printf("Usage: %s msgexch_cfg.xml\n", argv[0]);
		return 1;
	}

	sigset(SIGPIPE, SIG_IGN);

	//配置初始化
	int ret = DCExchCfg::instance()->Init(argv[1]);
	if(ret)
	{
		printf("init cfg failed\n");
		return -1;
	}
	SExchCfg *pcfg = DCExchCfg::instance()->GetConfig();
	//日志初始化
	ret = DCLOGINIT("CLOUD","arrange",pcfg->loglevel,pcfg->logserv.c_str());
	if(ret)
	{
		printf("init log failed\n");
		return -1;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","log path:%s, level:%d",pcfg->logserv.c_str(), pcfg->loglevel);
	char *szconfig =getenv("BILLING_CONFIG");
	if(NULL==szconfig)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","env BILLING_CONFIG not config");
		return -1;
	}
	string sqlPath;
	sqlPath = szconfig;
	sqlPath = sqlPath + '/' + pcfg->sqlConf;
	//sprintf(sqlPath,"%s/msgExch.sql.xml",szconfig);

	sigset(SIGTERM, sig_deal_func);
	sigset(SIGINT, sig_deal_func);
	sigset(SIGQUIT, sig_deal_func);
	sleep(1);

	int worker_num = pcfg->arrangeThreadnum;
	DCTaskSum vTaskSum[worker_num];
    for(int i = 0; i < worker_num; i++)
	{
		//数据库的初始化
		DCDBManer *dmdb = new DCDBManer();
		if(!dmdb)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","create oracle instance  failed");
			return -1;
		}
		ret = dmdb->Init(sqlPath.c_str());
		if(ret < 0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","init  sql failed");
			return -1;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Init dmdb success!");
		int latnId = atoi(argv[2]);
		ret = vTaskSum[i].init(dmdb,i,latnId);
		if(ret < 0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","init DCTaskSum failed, worker[%d]", i);
			return 1;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","init DCTaskSum success, worker[%d]",i);
	}

	for(int i = 0; i < worker_num; i++)
	{
		ret = vTaskSum[i].run();
		if(ret < 0)
		{

			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","start vTaskSum failed, worker[%d]", i);
			return 1;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","start vTaskSum success, worker[%d]",i);
	}
	sleep(2);

	while(1)
	{
		int exit = 0;
		for(int i = 0; i < worker_num; i++)
		{
			if(vTaskSum[i].exit())
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","TaskSum[%d] is exit",i);
				exit++;
			}
		}
		if(exit == worker_num)
		{
			break;
		}
		sleep(1);
	}

	return 0;
}

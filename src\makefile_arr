include ../../../comm.mk

RELEASE_PATH:=$(LBSPUBROOT)/CTPC/release
PWD=$(shell pwd)

BASE_ROOT	:= /public/ocs_ah/src_cloud/CTPC
BASE_INC	:= -I$(BASE_ROOT)/DCMsgExch/include


BINS= ./arrangeFiles

INCS		:= -I/usr/include/ \
           $(BASE_INC) \
		       -I$(ACE_INC_PATH)  \
           -I$(AVRO)/include \
		   -I$(DFM)/include \
           -I$(ITF)/include \
           -I$(TXML)/include \
           -I$(DFM_INC_PATH) $(BILLCOM_INC) \
		       -I$(DCFILE_INC) \
		       -I$(DCFILE_INC)/fastcommon \
		       -I$(DCFILE_INC)/fastdfs \
		       -I$(DCFILE_INC)/hiredis \
           -I$(DCLOGCLI)/include \
           -I$(DCFC_INC) \
           -I$(DCF_BASE)/src/kpr \
           -I$(JSON_INC) \
           -I$(MQ)/include 
          
LIBS		:= -L$(ACE_LIB_PATH) -lACE  -L$(DCLOGCLI)/lib -ldclogcli -L$(DFM)/lib -ldfm -L$(AVRO)/lib $(TXML)/lib/libtinyxml.a  -L$(DCFC_LIB) -ldcfclient -L$(DCFILE_LIB) -ldcfile -lfastcommon -L$(MQ)/lib -ldcmq -lz


EXCH_SRCS	:= DCArrangeFile.cpp TThread.cpp DCExchCfg.cpp DCTaskSum.cpp
DCS_OBJS	:= $(addsuffix .o,$(basename ${EXCH_SRCS}))
OBJS		:= $(DCS_OBJS)
EXFLAGS = -Wall -Wno-format -DLINUX -DACE_LACKS_PRAGMA_ONCE -DACE_HAS_INLINED_OSCALLS
bintarget=$(BINS)

.PHONY:all clean dup

all: ${bintarget}

#****************************************************************************
${bintarget}: ${DCS_OBJS}
	$(CC)  $(LDFLAGS)  $(EXFLAGS) -o  ${bintarget} ${DCS_OBJS} ${LIBS}

%.o : %.cpp
	$(CC) -c $(CXXFLAGS) $(EXFLAGS) $< -o $@ ${INCS}

clean:
	-rm -f core ${OBJS} ${bintarget}
	 
dup:
	@cp -pf ${bintarget} $(PROJECT_RPATH)/dispatch/bin && echo "dup $(bintarget) to $(PROJECT_RPATH)/dispatch/bin"

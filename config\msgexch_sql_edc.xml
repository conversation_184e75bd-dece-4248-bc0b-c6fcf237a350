<?xml version="1.0" encoding="utf-8"?>

<dcsql>
  <!--db 属性字段解释-->
  <!--字段name：     必选  db实例名字-->
  <!--字段category： 必选  数据库实现名字，与imp.category对应-->
  <!--字段env：      可选  数据库实现的附加参数-->
  <!--字段master：   必选  主连接串-->
  <!--字段standby：  可选  备连接串，可不配置-->
  <!--module 属性字段解释-->
  <!--字段name：     必选  module名字 -->
  <!--字段db：       必选  module里面的sql所用的dbinst名字 -->
  <!--字段policy：   必选  SQL加载策略：must 启动时初始化sql, demand 使用时初始化sql, none 不使用 -->
  <!--sql 属性字段解释-->
  <!--字段name：     必选  sql名字，若存在sub, 则对应名字为 "name|sub" -->
  <!--字段bind:      必选  绑定参数串，每一位代表一个绑定参数类型，1-int, 2-long, 3-char*, 4-blob -->
  <!--字段sub:       可选  sql子索引，以逗号分割，针对会话表、累帐表等场景，将sqltext中的"[@]"依次替换成子索引以获取多个sql语句 -->
  <data>
    <dbimp>
      <imp category="UDBDRIVER" version="1.0.0">libdriverutil.so</imp>
    </dbimp>
    <dbinst>
      <db name="ORA_Main" category="UDBDRIVER">
        <env>CONTYPE=1;DBTYPE=6;DBMAX=100;DRV=libmydrv.so</env>
        <master>odbc:mysql//133.64.177.65:8901/DUCC_BILL_SX?user=billuser&amp;password=1VBZ^0t#xi</master>
      </db>
    </dbinst>
  </data>
  <app>
    <module name="msgexch" db="ORA_Main" policy="must">
      <sql name="QueryTaskEdc" bind="3">select task_id,TASK_NAME,a.lines,file_type from task_manager_msgexch a where task_state = '1' and pid = ?</sql>
      <sql name="UpdateTaskToDealEdc" bind="3">UPDATE task_manager_msgexch a SET a.task_state = '1' ,a.pid=?, a.handle_time=sysdate() where task_state = '0' order by task_id limit 1000</sql>
      <sql name="UpdateTaskStateEdc" bind="2">update task_manager_msgexch set task_state='2' where task_id=? and task_state='1'</sql>
      <sql name="UpdateTaskToExpEdc" bind="2">update task_manager_msgexch set task_state='9' where task_id=?</sql>
      <sql name="DeleteTaskEdc" bind="2">delete from task_manager_msgexch where task_id=?</sql>
      <sql name="GetEdcEventId" bind="3">select a.event_id from par_event_info a, par_collect_file b where b.collect_alias = ? and a.collect_id = b.collect_id and a.STATE = '1' and b.STATE = '00A'</sql>
    </module>
  </app>
</dcsql>
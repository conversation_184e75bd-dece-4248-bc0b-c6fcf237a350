#include <iostream>
#include <string>
#include <vector>
#include <sstream>

// Base64解码表
static const std::string base64_chars = 
    "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    "abcdefghijklmnopqrstuvwxyz"
    "0123456789+/";

// Base64解码函数
std::string base64_decode(const std::string& encoded_string) {
    int in_len = encoded_string.size();
    int i = 0;
    int in = 0;
    unsigned char char_array_4[4], char_array_3[3];
    std::string ret;

    while (in_len-- && (encoded_string[in] != '=') && 
           (isalnum(encoded_string[in]) || (encoded_string[in] == '+') || (encoded_string[in] == '/'))) {
        char_array_4[i++] = encoded_string[in]; in++;
        if (i == 4) {
            for (i = 0; i < 4; i++)
                char_array_4[i] = base64_chars.find(char_array_4[i]);

            char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
            char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
            char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];

            for (i = 0; (i < 3); i++)
                ret += char_array_3[i];
            i = 0;
        }
    }

    if (i) {
        for (int j = 0; j < i; j++)
            char_array_4[j] = base64_chars.find(char_array_4[j]);

        char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
        char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);

        for (int j = 0; (j < i - 1); j++) ret += char_array_3[j];
    }

    return ret;
}

// URL解码函数
std::string url_decode(const std::string& str) {
    std::string result;
    for (size_t i = 0; i < str.length(); ++i) {
        if (str[i] == '%') {
            if (i + 2 < str.length()) {
                std::string hex = str.substr(i + 1, 2);
                char c = static_cast<char>(std::stoi(hex, nullptr, 16));
                result += c;
                i += 2;
            }
        } else if (str[i] == '+') {
            result += ' ';
        } else {
            result += str[i];
        }
    }
    return result;
}

// 主解密函数
std::string decrypt(const std::string& str) {
    if (str.empty()) {
        return str;
    }

    int str_length = str.length();
    
    // 提取最后一位数字（分割位置）
    char last_char = str[str_length - 1];
    int r = last_char - '0'; // 转换为数字
    
    // 验证分割位置是否有效
    if (r < 1 || r > 9) {
        std::cerr << "Invalid split position: " << r << std::endl;
        return "";
    }

    // 提取第一部分（0到r位置）
    std::string first_str = str.substr(0, r);
    
    // 提取第二部分（跳过26个随机字符）
    int end_start = r + 26;
    if (end_start >= str_length - 1) {
        std::cerr << "Invalid string format for decryption" << std::endl;
        return "";
    }
    std::string end_str = str.substr(end_start, str_length - 1 - end_start);

    // 重新组合Base64字符串
    std::string combined = first_str + end_str;
    
    // Base64解码
    std::string decoded_str;
    try {
        decoded_str = base64_decode(combined);
    } catch (const std::exception& e) {
        std::cerr << "Base64 decode error: " << e.what() << std::endl;
        return "";
    }

    // URL解码
    std::string final_str = url_decode(decoded_str);
    
    return final_str;
}

// 测试函数
int main() {
    // 示例：假设有一个加密后的字符串
    std::string encrypted = ""; // 这里需要实际的加密字符串进行测试
    
    std::cout << "请输入要解密的字符串: ";
    std::getline(std::cin, encrypted);
    
    if (!encrypted.empty()) {
        std::string decrypted = decrypt(encrypted);
        if (!decrypted.empty()) {
            std::cout << "解密结果: " << decrypted << std::endl;
        } else {
            std::cout << "解密失败" << std::endl;
        }
    }

    return 0;
}

// 编译命令: g++ -o decrypt decrypt.cpp -std=c++11
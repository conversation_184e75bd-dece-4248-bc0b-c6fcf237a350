/*******************************************
*Copyrights   2007，深圳天源迪科计算机有限公司
*                   技术平台项目组
*All rights reserved.
*
*Filename：
*       DCPairList.cpp
*Indentifier：
*
*Description：
*       消息路由序列号对队列
*Version：
*       V1.0
*Author:
*       YF.Du
*Finished：
*       2008年10月10日
*History:
*       2008/10/10  V1.0 文件创建
********************************************/
#include "DCPairList.h"
#include "DCTaskService.h"

DCPairList::DCPairList(DCDBManer *dmdb,dcf_new::DCFLocalClient *client,int mod)
{
	pthread_mutex_init(&m_mutex, NULL);
	pthread_mutex_init(&m_mutexfile, NULL);
	m_exchdb = new DCExchDB(dmdb);
	m_client = client;
	//m_UidList.clear();
	m_fileStatic.clear();
	m_mod = mod;
	SExchCfg* pcfg = DCExchCfg::instance()->GetConfig();
	m_cfg = pcfg;

	m_lnTimeoutCountOld = 0;
	struct timeval current;
	gettimeofday(&current, NULL);
	m_lnTimeoutOld      = current.tv_sec;

	m_codeTimes         = 0;
	m_checktime         = time(NULL);
	readyExit           = 0;
}

DCPairList::~DCPairList()
{
	pthread_mutex_destroy(&m_mutex);
	pthread_mutex_destroy(&m_mutexfile);

	if(m_exchdb)
	{
		delete m_exchdb;
		m_exchdb =NULL;
	}
}

void DCPairList::SetQuit()
{
	readyExit = 1;
}

int DCPairList::GetQuit()
{
    return readyExit;
}

int DCPairList::size()
{	
	return m_PairMap.size();
}

int DCPairList::in(const string uuid,const SMsgPair sMsg)
{
	int ret = 0;
	ret = lock();
	if (ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","list in lock error[%s]", uuid.c_str());
		return -1;
	}

	pair<map<string, SMsgPair>::iterator, bool> iter;
	iter = m_PairMap.insert(pair<string, SMsgPair>(uuid, sMsg));
	if (!(iter.second))
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","list in error[%s]", uuid.c_str());
		ret = -1;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","list in [%s]", uuid.c_str());
		ret = 0;
	}

	unlock();
	return ret;
}

int DCPairList::out(const string uuid,SMsgPair &sMsg)
{
	int ret = 0;

	ret = lock();
	if (ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","list out lock error[%s]", uuid.c_str());
		return -1;
	}

	map<string, SMsgPair>::iterator iter = m_PairMap.find(uuid);
	if (iter!=m_PairMap.end())
	{
		sMsg = iter->second;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","list out[%s]", uuid.c_str());
		m_PairMap.erase(iter);
		ret = 0;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DVIEW,0,"","message deleted,uuid[%s].", uuid.c_str());
		ret = 0;//可能被超时线程清除，所以不返回错误
	}

	unlock();
	return ret;
}

int DCPairList::modify(int totalline,long source_id)
{
	int ret = 0;

	ret = lock();
	if (ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","list modify lock error");
		return -1;
	}

	for(map<string, SMsgPair>::iterator iter=m_PairMap.begin();iter!=m_PairMap.end();iter++)
	{
		if(iter->second.sourceid == source_id)
		{
			iter->second.totalline = totalline;
		}
	}

	unlock();
	return ret;
}

void DCPairList::clearTimeOut(long timeOut, vector<SMsgPair>& vecTank)
{
	int ret = 0;
	map<string, SMsgPair>::iterator iter;

	vecTank.clear();
	vector<SMsgPair>::iterator iterTank;
	struct timeval current;
	gettimeofday(&current, NULL);
	ret = lock();
	if (ret)
	{
		return;
	}
	//timeOut 秒极
	if (readyExit == 1)
	{
		timeOut = 60;
	}
	for (iter = m_PairMap.begin(); iter!=m_PairMap.end();)
	{
		if ((iter ->second.begin.tv_sec + timeOut) < current.tv_sec)
		{
			//DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","timeout uid = %s and time=%ld,%ld]", (iter->first).c_str(),iter ->second.begin.tv_sec ,timeOut);
			//if(1==iter ->second.timeoutcount)
			//{
			//超时不再发送一次
			vecTank.push_back(iter->second);
			m_PairMap.erase(iter++);
			//}
			/*
			else
			{
			iter ->second.timeoutcount ++;

			DCPerfTimeVCollect colletv(NULL, true);
			int flag = m_exchdb->IsDeleteTimeoutKey(iter->first);
			if(1==flag||3==flag)
			{				
			ret = m_client->invokeAsync(iter->second.servName, iter->first, iter->second.sendMsg,"",0);		
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "send billingmsg to service:%s, uuid:%s", iter->second.servName.c_str(), iter->first.c_str());
			if(ret < 0)
			{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "send failed");	
			vecTank.push_back(iter->second);
			m_exchdb->MarkRecordState(iter->first,"0",iter->second.latnid);
			if(1==flag)
			{
			m_exchdb->ClearKey(iter->first);
			colletv.stop();
			if(DCLOG_GETLEVEL(DCLOG_CLASS_PERF) > 0 )
			{
			DCPERFLOG(DCLOG_CLASS_PERF, "msgexch.ClearKey=%ld",colletv.m_usec);
			}
			}
			m_PairMap.erase(iter++);
			}
			else
			{
			struct timeval tv;
			gettimeofday(&tv, NULL);
			iter->second.begin = tv;
			iter++;
			}
			}
			else
			{
			vecTank.push_back(iter->second);
			m_exchdb->MarkRecordState(iter->first,"0",iter->second.latnid);
			if(2==flag)
			{

			m_exchdb->ClearKey(iter->first);
			colletv.stop();
			if(DCLOG_GETLEVEL(DCLOG_CLASS_PERF) > 0 )
			{
			DCPERFLOG(DCLOG_CLASS_PERF, "msgexch.ClearKey=%ld",colletv.m_usec);
			}
			}
			m_PairMap.erase(iter++);				
			}


			}
			*/

			m_lnTimeoutCountOld++;
		}
		else
		{
			iter++;
		}
	}

	if ( 0 == m_cfg->nRunmode && current.tv_sec - m_lnTimeoutOld >= m_cfg->lnTimeoutRange)
	{
		if (m_lnTimeoutCountOld >= m_cfg->lnTimeoutCount)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "the maximum limit of timeout message is reached (to %ld) within %ld sec, exit processing", m_lnTimeoutCountOld, current.tv_sec - m_lnTimeoutOld);
			DCTaskService::state(2);//退出程序
		}
		m_lnTimeoutOld      = current.tv_sec;
		m_lnTimeoutCountOld = 0;
	}

	unlock();
	for(iterTank = vecTank.begin();iterTank != vecTank.end();iterTank ++)
	{	
		if(m_mod)
		{
			m_exchdb->MarkRecordState(iterTank->uuid,"9", iterTank->procId, iterTank->latnid,1,iterTank->billingNbr);
			continue;
		}
		m_exchdb->MarkRecordState(iterTank->uuid,"9", iterTank->procId, iterTank->latnid,0,iterTank->billingNbr);
		/*
		DCPerfTimeVCollect collet(&m_tstat, true);
		collet.start();
		int flag = m_exchdb->IsDeleteTimeoutKey(iterTank->uuid);
		collet.stop();
		// 输出统计信息

		if(DCLOG_GETLEVEL(DCLOG_CLASS_PERF) > 0 )
		{
		DCPERFLOG(DCLOG_CLASS_PERF, "msgexch.IsDeleteTimeoutKey=%ld",collet.m_usec);
		}
		if(1==flag || 2==flag)
		{

		DCPerfTimeVCollect collet1(&m_tstat, true);
		collet1.start();
		m_exchdb->ClearKey(iterTank->uuid);
		collet1.stop();
		if(DCLOG_GETLEVEL(DCLOG_CLASS_PERF) > 0 )
		{
		DCPERFLOG(DCLOG_CLASS_PERF, "msgexch.ClearKey=%ld",collet1.m_usec);
		}
		}
		*/
	}

    return;
}

int DCPairList::lock()
{
	return pthread_mutex_lock(&m_mutex);
}

int DCPairList::unlock()
{
	return pthread_mutex_unlock(&m_mutex);
}


int DCPairList::get(const string uuid,SMsgPair &sMsg)
{
	int ret = 0;

	ret = lock();
	if (ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","list get lock error[%s].", uuid.c_str());
		return -1;
	}

	map<string, SMsgPair>::iterator iter = m_PairMap.find(uuid);
	if (iter!=m_PairMap.end())
	{
		sMsg = iter->second;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","list get [%s].", uuid.c_str());
		ret = 0;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","message deleted,uuid[%s].", uuid.c_str());
		ret = -1;
	}

	unlock();
	return ret;
}

/*int DCPairList::push(const string uuid)
{
lockL();
m_UidList.push_back(uuid);
unlockL();
return 0;
}

string &DCPairList::pop()
{
lockL();
string uuid = m_UidList.front();
unlockL();

return uuid;

}

int DCPairList::popfront()
{
lockL();

m_UidList.pop_front();	
unlockL();

return 0;

}


int DCPairList::empty()
{
return m_UidList.empty();
}


int DCPairList::lockL()
{
return pthread_mutex_lock(&m_mutexlist);
}

int DCPairList::unlockL()
{
return pthread_mutex_unlock(&m_mutexlist);
}*/

int DCPairList::lockM()
{
	return pthread_mutex_lock(&m_mutexfile);
}

int DCPairList::unlockM()
{
	return pthread_mutex_unlock(&m_mutexfile);
}


int DCPairList::enQueue(const string uuid,string ipErrorCode)
{
	if(m_Quuid.enqueue(uuid) <0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","in queue uuid[%s] failed",uuid.c_str());			
	}
	int ret = lock();
	if (ret)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","list get lock error[%s].", uuid.c_str());
		return -1;
	}
	ret = 0;
	time_t cursec = time(NULL);
	if((m_checktime + 60 * m_cfg->infoTimeDelay) < cursec)
	{
		m_checktime = cursec;
		if(m_iPCode.size()>0)
		{
			outInfoFile();
		}
	}
	if(ipErrorCode.length()> 0)
	{
		ret = dealErrorCode(ipErrorCode);
		if(ret > 0)
		{
			if(m_checktime + 60 < cursec)
			{
				m_checktime = cursec;
				outInfoFile();
			}
		}
	}
	/*
	if(m_codeTimes > m_cfg->errorCodeTime)
	{
	DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","errorCode[%s] times[%d],ready exit",lastErrorCode.c_str(),m_codeTimes);
	if(m_mod == 0)
	{
	ret = 1;
	}
	else if(m_mod == 2)
	{
	ret = 2;
	}
	outInfoFile();
	}
	*/
	unlock();

	return ret;
}

int DCPairList::deQueue(string &uuid)
{
	ACE_Time_Value expired;
	expired.set(time(NULL) + 5, 0);
	return m_Quuid.dequeue(uuid, &expired);	//超时等待获取
}

int DCPairList::trydeQueue(string &uuid)
{
	return  m_Quuid.try_dequeue(uuid);
}

int DCPairList::quesize()
{
	return m_Quuid.size();
}
void DCPairList::outInfoFile()
{
	char v_date[25] = {0};
	tm *p;
	//int ret = 0;
	time_t timep;
	time(&timep);
	p=localtime(&timep);
	sprintf(v_date,"%d%02d%02d%02d%02d.ipError",(1900+p->tm_year) , (1+p->tm_mon) ,p->tm_mday,p->tm_hour,p->tm_min);
	string infoFileName = m_cfg->Info_dir + '/' + v_date;
	ofstream ofile(infoFileName.c_str());
	m_cfg = DCExchCfg::instance()->GetConfig();
	if(ofile.is_open())
	{
		for(map<string,int>::iterator iter = m_iPCode.begin();iter != m_iPCode.end();iter++)
		{
			ofile<<iter->first<<":"<<iter->second<<endl;
		}
		ofile.close();
		m_iPCode.clear();
	}
	return;
}
int DCPairList::dealErrorCode(string ipErrorCode)
{
	int ret = 0;
	size_t npos = ipErrorCode.find("&");
	if(npos == string::npos)
	{
		return -1;
	}
	ipErrorCode = ipErrorCode.substr(0,npos);
	npos = ipErrorCode.find(";");
	if(npos == string::npos)
	{
		return -1;
	}
	string errorCode = ipErrorCode.substr(npos + 1,10);
	if(errorCode.length() > 1 && errorCode[0] != '0')
	{
		map<string,int>::iterator iter = m_iPCode.find(ipErrorCode);
		if(iter != m_iPCode.end())
		{
			iter->second++;
			if(m_cfg->errorCodeList.find(errorCode) != string::npos && iter->second > m_cfg->errorCodeTime)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","errorCode[%s] times[%d],ready exit",iter->first.c_str(),iter->second);
				if(m_mod == 0)
				{
					ret = 1;
				}
				else if(m_mod == 2)
				{
					ret = 2;
				}
			}
		}
		else
		{
			m_iPCode.insert(make_pair(ipErrorCode,1));
		}
		if(lastErrorCode != ipErrorCode)
		{
			lastErrorCode = ipErrorCode;
			m_codeTimes = 1;
		}
		else
		{
			m_codeTimes++;
		}
	}
	else
	{
		lastErrorCode = "";
		m_codeTimes = 0;
	}
	return ret;
}




#include "DCDBHander.h"

int DCDBHander::m_ndone = 0;

int DCDBHander::init(DCDBManer* dmdb,DCPairList *pairlist, int deleteflag, int iDelCheckUidFlag, int latnId, int iTimeOut)
{
	m_dmdb = dmdb;
	m_pairList = pairlist;
	m_deleteflag = deleteflag;
	m_ndone = 0;
	m_bexit = false;
	m_exchdb = new DCExchDB(dmdb);
	m_latnId = latnId;
	m_iDelCheckUidFlag = iDelCheckUidFlag;
	m_iTimeOut = iTimeOut;
	if(!m_exchdb)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","new DCExchDB failed");
		return -1;
	}
	return 0;
}

int DCDBHander::run()
{
	int ret = 0;
	ret = activate(THR_NEW_LWP | THR_JOINABLE | THR_INHERIT_SCHED | THR_SCOPE_SYSTEM, 1);
	if(ret < 0)
	{
		return -1;
	}
	return 0;
}

int DCDBHander::svc()
{
	list<SMsgPair> M0AMsgList;
	m_checktime = time(NULL);
    while(1)	// 可运行
    {
		if (DCExchDB::CheckDBState(m_dmdb, m_checktime) != 0)
			continue;

		struct timeval curTime;
		gettimeofday(&curTime, NULL);
		for (list<SMsgPair>::iterator itM0AMsg = M0AMsgList.begin(); itM0AMsg != M0AMsgList.end(); )
		{
			int iEffectRow = m_exchdb->MarkFileStateSum(itM0AMsg->sourceid, itM0AMsg->batchId.c_str(), "M0F", m_latnId, "M0A");
			if (iEffectRow > 0)
			{
				itM0AMsg = M0AMsgList.erase(itM0AMsg);
				continue;
			}
	
			if (itM0AMsg->begin.tv_sec + m_iTimeOut < curTime.tv_sec)
			{
				itM0AMsg = M0AMsgList.erase(itM0AMsg);
				continue;
			}

			itM0AMsg++;
		}

		string strUid="";
		int ret = m_pairList->trydeQueue(strUid);	//优先从任务队列获取
		if(ret < 0)
		{
			if(m_pairList->deQueue(strUid) < 0)	//超时等待获取
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, -1, "","no ans msg,try next");

				if(1==m_ndone)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","thread exit");
					break;
				}

				continue;
			}
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","pop ans msg: uuid=[%s]",strUid.c_str());

		/*if(!m_pairList->empty())
		{
			strUid = m_pairList->pop();
		}*/

		if(strUid.empty())
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","uuid is null");
			continue;
		}
		DCBIZLOG(DCLOG_LEVEL_INFO, 0, "","pop ans msg m_Quuid size:%d,PariMap size:%d",m_pairList->quesize(),m_pairList->size());
		SMsgPair msgPair;
		if(m_pairList->size()>0)
		{
			m_pairList->out(strUid,msgPair);//暂时不需要返回值

			DCPerfTimeVCollect collet(NULL, true);
			//if(m_exchdb->IsDeleteKey(strUid)>0)	//直接删除
			if (m_iDelCheckUidFlag)
			{
				if(m_latnId == 0)
				{
					m_exchdb->ClearKey(strUid);
				}
			}

			collet.stop();

			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","dca expend[%ld] usec",collet.m_usec);
		}

		struct timeval current;
		gettimeofday(&current, NULL);

		double iexpend = current.tv_sec - msgPair.begin.tv_sec + ((current.tv_usec-msgPair.begin.tv_usec)/1000)/1000.0;
		DCBIZLOG(DCLOG_LEVEL_INFO, 0, "","Get CALLBACK: uuid=[%s].and expend[%.3f] sec",strUid.c_str(),iexpend);

		if(msgPair.uuid.empty())
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","pairlist uuid is null");
			continue;
		}
		bool bFind = false;

		m_pairList->lockM();
		std::map <long,SMsgPair>::iterator iter = m_pairList->m_fileStatic.find(msgPair.sourceid);
		if (iter != m_pairList->m_fileStatic.end())
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","file[%ld] recvcont[%d],total record[%d]",msgPair.sourceid,iter->second.recvcont,msgPair.totalline);
            // iter->second.recvcont + 1 == msgPair.totalline 这个条件不能用 >= , 否则会导致部分M0A无法更新为M0F
            if (iter->second.recvcont + 1 == msgPair.totalline)
			{
				bFind = true;
				m_pairList->m_fileStatic.erase(iter);//删除map
			}
			else
			{
				iter->second.recvcont += 1;
			}
		}
		else
		{
			msgPair.recvcont = 1;     
            if (msgPair.recvcont == msgPair.totalline)     // 文件消息条数可能只有一条
            {
                bFind = true;
            }
            else
            {
                m_pairList->m_fileStatic.insert(pair<long, SMsgPair>(msgPair.sourceid, msgPair));  // 文件消息有多条
            }
            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "file[%ld] recvcont[%d],total record[%d], ListSize[%d]", 
                msgPair.sourceid, msgPair.recvcont, msgPair.totalline, m_pairList->m_fileStatic.size());
		}
		/*
		//超时消息被清除时，totalline减少但recvcont可能不会再增加，需要检测无法清除的map元素
		for(iter = m_pairList->m_fileStatic.begin(), iter != m_pairList->m_fileStatic.end(), iter++)
		{
			if(iter->second.recvcont+1 == iter->second.totalline)
			{
				m_pairList->m_fileStatic.erase(iter);//删除map
			}
		}
		*/
		m_pairList->unlockM();

		if(bFind)
		{
			DCKpiMon *ptrBPMon = DCKpiSender::instance()->GetKPIHandle("BILLING", "REC", "MSGEX");
			char monGroup[50] = {0};
			DCKpiSender::instance()->GetFullGroup("TraceKpi", m_latnId, monGroup);
			DCKpiSender::instance()->cycle_array_inc(ptrBPMon, monGroup, "", "MXAF", NULL, 1);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "kpi code[MXAF] latnid[%d] inc", m_latnId);

			DCPerfTimeVCollect collet2(NULL, true);
            string strFileState;
			//更新文件处理状态、删除任务表
			if(m_latnId == 0)
			{
				m_exchdb->MarkFileState(msgPair.sourceid, "M0F");
                strFileState = "M0F";
			}
			else
			{
				if(m_deleteflag)
				{
					m_exchdb->MarkTaskToDoneSum(msgPair.sourceid, msgPair.batchId.c_str(), m_latnId);
                    strFileState = "DONE";
				}
				else
				{
					int iEffectRow = m_exchdb->MarkFileStateSum(msgPair.sourceid, msgPair.batchId.c_str(), "M0F",m_latnId,"M0A");
					if (iEffectRow == 0)
					{
						M0AMsgList.push_back(msgPair);
					}

					strFileState = "M0F";
				}
			}
			collet2.stop();
			DCBIZLOG(DCLOG_LEVEL_INFO, 0, "","MarkFileState expend[%ld] SourceId[%ld] FileState[%s] usec",collet2.m_usec, msgPair.sourceid, strFileState.c_str());
		}
		/*
		if(DCLOG_GETLEVEL(DCLOG_CLASS_PERF) > 0 )
		{
			struct timeval	  tv;
			gettimeofday(&tv, NULL);
			//long us = (tv.tv_sec - msgPair.begin.tv_sec)*1000000 + tv.tv_usec - msgPair.begin.tv_usec;
			//DCPERFLOG(DCLOG_CLASS_PERF, "msgexch.total=%s|1|%ld",strUid.c_str(), us);

			// statics
			m_pStats->m_sendSuccessCount.fetchAndAdd(1);
			long ms = (tv.tv_sec - msgPair.begin.tv_sec)*1000+(tv.tv_usec - msgPair.begin.tv_usec)/1000;
			m_pStats->m_sendMsgSuccessTimeToal.fetchAndAdd(ms);
			m_pStats->setDistributeTime(ms);

			long preMaxRt = m_pStats->m_sendMsgMaxRT.Get();
			if (ms> preMaxRt){
				m_pStats->m_sendMsgMaxRT.CompareAndSet(preMaxRt, ms);
			}
		}
		*/

	}

	m_bexit = true;
	return 0;
}


void DCDBHander::state(int done)
{
	m_ndone = done;
}

bool DCDBHander::exit()
{
	return m_bexit;
}



#include "DCPerfLogStatistic.h"

#include "DCMCastEvtFun.h"
#include "DCMCastManer.h"
#include "DCLogMacro.h"
#include <ace/Log_Msg.h>
#include <ace/Log_Msg_Backend.h>
#include <ace/Log_Record.h>

DCPerfLogStatistic* DCPerfLogStatistic::sm_pPerfLog = new DCPerfLogStatistic();


DCPerfLogStatistic::DCPerfLogStatistic()
{
	m_tOutput = time(NULL);
	m_vecModuleList.clear();
	m_vecDBUsing.clear();
}

DCPerfLogStatistic::~DCPerfLogStatistic()
{
	m_vecModuleList.clear();
	m_pmStatistic.clear();
	m_vecDBUsing.clear();
}


DCPerfTimeStats* DCPerfLogStatistic::GetPerfTimeStats(const char* szModule, const char* szItem, DCDBManer* pdb)
{
	if (pdb)
	{
		m_vecDBUsing.insert(pdb);
	}

	std::string strTemp = "";
	strTemp.assign(szModule);
	m_vecModuleList.insert(strTemp);

	return m_pmStatistic.get_statistic(szModule)->get_position(szItem);
}

void DCPerfLogStatistic::reset()
{
	for (set<string>::iterator itr = m_vecModuleList.begin(); itr != m_vecModuleList.end(); itr++)
	{
		m_pmStatistic.get_statistic(itr->c_str())->reset();
	}
}

void DCPerfLogStatistic::output()
{
	if (!sm_pPerfLog)
		return;

	bool   bSample = true;
	string strOut  = "";

	for (set<string>::iterator itr = m_vecModuleList.begin(); itr != m_vecModuleList.end(); itr++)
	{
		m_pmStatistic.get_statistic(itr->c_str())->to_json_string(strOut, false, bSample);
	}

	for (set<DCDBManer *>::iterator itr1 = m_vecDBUsing.begin(); itr1 != m_vecDBUsing.end(); itr1++)
	{
		(*itr1)->get_statistic()->to_json_string(strOut, false, bSample);
	}

	DCPERFLOG(0,"%s", strOut.c_str());
}

void DCPerfLogStatistic::output_timeing()
{
	if (!sm_pPerfLog)
		return;

	time_t t_now = time(NULL);
	if (t_now - m_tOutput < OUTPUT_PERF_LOG_INTERVAL)
	{
		return;
	}

	bool   bSample = true;
	string strOut  = "";

	for (set<string>::iterator itr = m_vecModuleList.begin(); itr != m_vecModuleList.end(); itr++)
	{
		m_pmStatistic.get_statistic(itr->c_str())->to_json_string(strOut, false, bSample);
	}

	if ( !strOut.empty() )
	{
		for (set<DCDBManer *>::iterator itr1 = m_vecDBUsing.begin(); itr1 != m_vecDBUsing.end(); itr1++)
		{
			(*itr1)->get_statistic()->to_json_string(strOut, false, bSample);
		}

		DCPERFLOG(0,"%s", strOut.c_str());
		reset();//输出后重置性能统计数据
	}

}
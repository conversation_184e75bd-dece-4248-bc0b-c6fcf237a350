/***********************************************************************
 * Module:  DCTQueue.h
 * Author:  any
 * Modified: 2013年12月1日 17:56:00
 * Purpose: Declaration of the class DCTQueue
 * notice:  可超时等待的队列
 ***********************************************************************/
#ifndef __DCTQUEUE_H__
#define __DCTQUEUE_H__

#include <ace/Unbounded_Queue.h>
#include <ace/Thread_Mutex.h>
#include <ace/Condition_Thread_Mutex.h>
#include <ace/Time_Value.h>

template <class T>
class DCTQueue
{
public:
	typedef T	value_type;
public:

    DCTQueue()
        :  m_cond(m_mutex)
    {
    }

    ~DCTQueue()
    {
    }

	int enqueue(const value_type & p);

	int dequeue(value_type & p, ACE_Time_Value * abstime = NULL);

	int try_dequeue(value_type & p);

	size_t size();

private:
    ACE_Unbounded_Queue<value_type>  m_queue;
    ACE_Thread_Mutex m_mutex;
    ACE_Condition_Thread_Mutex m_cond;
};

#include "DCTQueue.hpp"

#endif // __DCTQUEUE_H__

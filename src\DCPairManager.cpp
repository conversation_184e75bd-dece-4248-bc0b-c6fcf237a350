/*******************************************
 *Copyrights   2007，深圳天源迪科计算机有限公司
 *                   技术平台项目组
 *All rights reserved.
 *
 *Filename：
 *       DCPairManager.cpp
 *Indentifier：
 *
 *Description：
 *       序列值对超时处理线程
 *Version：
 *       V1.0
 *Author:
 *       YF.Du
 *Finished：
 *       2008年10月10日
 *History:
 *       2008/10/10  V1.0 文件创建
 ********************************************/
#include "DCPairManager.h"

using namespace std;

DCPairManager::DCPairManager()
{
	m_pairList = NULL;
	m_timeOut = 0;
    m_bExit = false;
}

DCPairManager::~DCPairManager()
{
}

int DCPairManager::init(DCPairList *pairList, unsigned long timeOut, DCMon *screen)
{
	m_pairList = pairList;
	m_screen = screen;
	//超时时间秒级
	m_timeOut = timeOut;
	return 0;
}

bool DCPairManager::done()
{
    return m_bExit;
}

void DCPairManager::routine()
{	
	int nspsize = 0;
	
	vector<SMsgPair> vecTimeOut;
	
	while (1)
	{
		sleep(1);
		nspsize = m_pairList->size();
		
		if (nspsize)
		{
            m_pairList->clearTimeOut(m_timeOut, vecTimeOut);
		}
		
		for (unsigned int j = 0; j<vecTimeOut.size(); j++)
		{
			DealTimeOut(vecTimeOut[j]);
			//m_screen->cycle_inc("rtm","k22");
		}
		vecTimeOut.clear();
		
        nspsize = m_pairList->size();
        if ((nspsize == 0) && (m_pairList->GetQuit() > 0))
        {
            DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "timeout task exit");
            m_bExit = true;
            break;
        }
	}
}

void DCPairManager::DealTimeOut(const SMsgPair msg)
{
	//打印超时日志
	struct timeval current;
	gettimeofday(&current, NULL);
	
	long dm = (current.tv_sec-msg.begin.tv_sec)*1000000+current.tv_usec-msg.begin.tv_usec;
	DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","TimeOut:uuid[%s] , sendsec[%ld.%06ld], nowsec[%ld.%06ld], diffsec[%ld.%06ld].",
		msg.uuid.c_str(),msg.begin.tv_sec,msg.begin.tv_usec,current.tv_sec,current.tv_usec,dm/1000000,dm%1000000);
}


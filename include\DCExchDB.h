#ifndef __DCEXCH_DB_H__
#define __DCEXCH_DB_H__

#include "DCDataDef.h"
#include "DCDBManer.h"

class DCExchDB
{
public:
	DCExchDB(DCDBManer  *dmdb);

	~DCExchDB();

	int MarkRecordState(string uuid,const char* state, const char* procId, int latnid,int mod, string billingNbr);
	int MarkTaskToDone(long taskid);
	int MarkTaskToDoneSum(long taskid, const char* batchId, int latnId);
	int MarkTaskToDoneEdc(long taskid);
	int MarkTaskToException(long taskid);
	int MarkTaskToExceptionEdc(long taskid);
	int MarkTaskToDeal(long taskid);
	int MarkTaskDealingEdc(long taskid);
	int MarkFileState(long source_id, const char* state);
	int MarkFileStateSum(long charge_files_id, const char* batchId, const char* state,int latnId,const char* oriState);
	int GetTraceNumFlag(const char* szCER, const char* szOID);

	int RecordLogRating(const STLogRating& log);

	int RecordUidAll(int latnId, long lines, long sourceId, long taskId);

	int IsDeleteTimeoutKey(string uuid);
	int ClearKey(string uuid);
	int IsDeleteKey(string uuid);

	int UpdateUidAll(int latnId, long lines, long sourceId, long taskId, int state);

	int SpecialBackUp(const STTask* pTask);

	int MarkTaskToDup(long taskid, const char* state);

public:
	static int CheckDBState(DCDBManer* const _db, time_t& tLastResetTime, bool bForceReset = false);

private:
	DCDBManer  *m_dmdb;
	time_t m_checktime;
};

#endif

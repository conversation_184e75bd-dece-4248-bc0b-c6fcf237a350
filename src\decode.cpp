#include "UFmtMsg.h"
#include "UHead.h"
#include "DCSeriaOp.h"
#include <iostream>
#include <vector>
#include <string>
#include <string.h>
#include "DCFLocalClientNew.h"
#include "DCPairList.h"
#include "DCExchDB.h"
#include <ace/Task.h>
#include <ace/Thread_Mutex.h>
using namespace dcf;

#include "DCMqConsumeServer.h"
using namespace std;
int main()
{
	ocs::UHead uhead;
	ocs::UFmtHead head;
	DCSeriaDecoder * m_de;
	m_de = new DCSeriaDecoder(ESeriaBinString);
	std::vector<uint8_t> recvMsg;
	char buf[8192] = {0};
	string sendmsg = "52413030313337373232333030303132383333343230313830333231313330323135323934363933383202357f0242ce0842420844414e54628af48d8701782f6f746865722f64637265636c61696d2f43414c4c4d4953535f33335f33335f3134313637313638355f3535315f323031383033323131333434343302317f18323031383033323131333330c44935323431333033303331333333373337333233323333333033303330333133323338333233363330333233303331333833303333333233313331333333303332333133353332333933343336333933333330333830323335376630323432636530383432343230383434343134653534356538616634386438373031373832663666373436383635373232663634363337323635363336633631363936643266343334313463346334643439353335";
	try
	{	
		recvMsg = HexDecode(sendmsg.c_str(), sendmsg.length());
		m_de->set(recvMsg);
		m_de->decode(uhead);					
		m_de->decode(head);	
				
	}
	catch(exception& e)
	{
		;
	}
	cout<<"uuid:\t\t"<<uhead.uid<<endl;
	cout<<"car:\t\t"<<uhead.car<<endl;
	cout<<"tarce:\t\t"<<uhead.trace<<endl;
	cout<<"flag:\t\t"<<uhead.flag<<endl;
	cout<<"collectid:\t"<<head.collectid<<endl;
    cout<<"latnid:\t\t"<<head.latnid<<endl;
    cout<<"operlistid:\t"<<head.operlistid<<endl;
    cout<<"switchid:\t"<<head.switchid<<endl;
    cout<<"opertype:\t"<<head.opertype<<endl;
    cout<<"recordid:\t"<<head.recordid<<endl;
    cout<<"sourceid:\t"<<head.sourceid<<endl;
    cout<<"sourcefile:\t"<<head.sourcefile<<endl;
    cout<<"msgtype:\t"<<head.msgtype<<endl;
    cout<<"procName:\t"<<head.procName<<endl;
    cout<<"batchid:\t"<<head.batchid<<endl;
	
	return 0;
}
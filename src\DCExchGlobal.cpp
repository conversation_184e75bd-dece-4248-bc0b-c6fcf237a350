#include "DCExchGlobal.h"
#include "DCLogMacro.h"
#include "DCMCastEvtFun.h"
#include <netdb.h>
#include <sys/socket.h>
#include <arpa/inet.h>

DCExchGlobal* DCExchGlobal::m_pInstance = NULL;

DCExchGlobal::DCExchGlobal()
{
	pthread_mutex_init(&m_GmtxCout, NULL);
}

DCExchGlobal::~DCExchGlobal()
{

}

DCExchGlobal* DCExchGlobal::instance()
{
    if (m_pInstance == NULL)
    {
        m_pInstance = new DCExchGlobal();
    }

    return m_pInstance;
}

void DCExchGlobal::SetCount(int iKey, long lnCount)
{
	pthread_mutex_lock(&m_GmtxCout);
	m_GmapCout[iKey] = lnCount;
	pthread_mutex_unlock(&m_GmtxCout);
}

long DCExchGlobal::AddCount(int iKey, int lnCount)
{
	long lnResultCount = 0;
	pthread_mutex_lock(&m_GmtxCout);
	MapCount::iterator itMapCount = m_GmapCout.find(iKey);
	if (itMapCount == m_GmapCout.end())
	{
		m_GmapCout[iKey] = lnCount;
	}
	else
	{
		m_GmapCout[iKey] += lnCount;
	}
	lnResultCount = m_GmapCout[iKey];
	pthread_mutex_unlock(&m_GmtxCout);
	return lnResultCount;
}

long DCExchGlobal::ReduceCount(int iKey, int lnCount)
{
	long lnResultCount = 0;
	pthread_mutex_lock(&m_GmtxCout);
	MapCount::iterator itMapCount = m_GmapCout.find(iKey);
	if (itMapCount == m_GmapCout.end())
	{
		m_GmapCout[iKey] = 0 - lnCount;
	}
	else
	{
		m_GmapCout[iKey] -= lnCount;
	}
	lnResultCount = m_GmapCout[iKey];
	pthread_mutex_unlock(&m_GmtxCout);
	return lnResultCount;
}

long DCExchGlobal::GetCount(int iKey)
{
	long lnCount = 0;
	pthread_mutex_lock(&m_GmtxCout);
	MapCount::iterator itMapCount = m_GmapCout.find(iKey);
	if (itMapCount != m_GmapCout.end())
	{
		lnCount = itMapCount->second;
	}
	pthread_mutex_unlock(&m_GmtxCout);
	return lnCount;
}



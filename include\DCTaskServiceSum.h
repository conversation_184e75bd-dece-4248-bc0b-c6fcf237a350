﻿#ifndef __DCTaskServiceSum_H__
#define __DCTaskServiceSum_H__

#include "DCTQueue.h"
#include "DCDBManer.h"
#include "DCFile.h"
#include "DCSeriaOp.h"
#include "UFmtMsg.h"
#include "DCLogMacro.h"
#include "DCPairList.h"
#include "DCDataDef.h"
#include "DCExchCfg.h"
#include "DCPerfStatistic.h"
#include <ace/Task.h>
#include <ace/Thread_Mutex.h>
#include <vector>
#include <stdio.h>
#include <list>
#include <map>
#include "DCTaskSum.h"
#include "UHead.h"
#include "DCAcctDiscMessage.h"
#include "DCTCompress.h"
#include "DCExchGlobal.h"


class DCTaskServiceSum : public ACE_Task_Base
{
public:
	DCTaskServiceSum();
	~DCTaskServiceSum();

	int init(DCDBManer* dmdb,dcf_new::DCFLocalClient *client,DCPairList *pairlist,int latnId, int iThreadNo);

	int run();

	virtual int svc();

	static void state(int done);

	static void queueState(int queueState);

	bool exit();

	int getSize()
	{
		return m_pairList->size();
	}

private:
	static int GetTask(DCDBManer *dmdb,SumTask& task,int latnId);

	int DoTask(const SumTask& task);

	int DealAnsMsg(const MsgObj& obj);
	void GetBuf(TAccRecord & recordBuf,string & line);
	void compress(string& buf);
	void SetRecord(ocs::RatingFee &record,TAccRecord &buf);
	int GetModGroupID(DCDBManer* dmdb, int latnId, long vi_nAcctID, long& vo_vo_ModGroupID);
	string GetBatchTime(string strBatchId);
	int GetBatchInterceptTime(DCDBManer *dmdb, int iLatnId, string &strInterceptTime);
	int UpdateTaskStatus(DCDBManer *dmdb, int iLatnId);
	int CheckIntercept(DCDBManer *dmdb, int iLatnId, const SumTask& task);
	int GetGrayServName(const int nLatnId,const long lnRouteAcctId,string &serviceBILL, string &cServiceNbr, dcf_new::DCFLocalClient *pClient=NULL);

private:
	static int               sm_nDone;
	static int               sm_nQueueState;
	static time_t            sm_checktime;
	bool                     m_bexit;
	SumTask*                 m_task;    
	std::string              m_szLocalPath;
	static DCTQueue<SumTask> sm_qTask;
	DCFLocalClient*          m_client;
	static ACE_Thread_Mutex  sm_aceMutex;
	STLogRating              m_lograte;
	DCTQueue<MsgObj>         m_queue;
	DCDBManer*               m_dmdb;
	DCExchDB*                m_exchdb;
	DCPairList*              m_pairList;
	DCSeriaEncoder           m_en;        
	DCSeriaPrinter           m_print;
	SExchCfg*                m_cfg;
	Compressor*              m_compress;
	int                      m_latnId;
	int						 m_iThreadNo;
	time_t                   m_checktime;
	static string            m_strHostIp;
	bool                     m_bInterceptFlag;
};

#endif // __DCTaskServiceSum_H__

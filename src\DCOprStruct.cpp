#include "DCExchDB.h"
#include "DCOprStruct.h"
#include <assert.h>
#include <stdlib.h>
#include "DCLogMacro.h"
#include "DCPerfStatistic.h"

DCOprStruct::DCOprStruct()
:m_iOperListId(0)
,m_iSize(0)
,m_acQuery(NULL)
{}

DCOprStruct::~DCOprStruct()
{}

int DCOprStruct::InitParam(DCDBManer* pdbm)
{
	m_dbm = pdbm;
    m_acQuery = m_dbm->GetSQL("PAROPRSTRUCT");
    if (m_acQuery == NULL)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","can't get m_acQuery[PAROPRSTRUCT]!");
        return -1;
    }

    return 0;
}

int DCOprStruct::SetOperListId(int vi_iOperListId)
{
    if (m_iOperListId != vi_iOperListId)
    {
        m_mapName2ColInfo.clear();

        try
        {
            m_acQuery->UnBindParam();
            m_acQuery->BindParam(1, vi_iOperListId);
            m_acQuery->BindParam(2, 1);//version is dead 1

            std::string fieldKey="";
            STOprColInfo stOprInfo;
            m_acQuery->Execute();
            char sStr[1024]={0};
            m_iSize = 0;
            while (m_acQuery->Next())
            {
                m_acQuery->GetValue(7, sStr);
                stOprInfo.col =  atoi(sStr);

                m_acQuery->GetValue(1, sStr);
                stOprInfo.name = sStr;

                m_acQuery->GetValue(2,sStr);
                stOprInfo.offset = atoi(sStr);

                m_acQuery->GetValue(3, sStr);
                stOprInfo.len = atoi(sStr);

                m_iSize += stOprInfo.len;
                m_mapName2ColInfo.insert(std::make_pair(stOprInfo.name, stOprInfo));
            }
        }
        catch (UDBException& e)
        {
            m_acQuery->UnBindParam();
            DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","UDBException error[%s]!\n", e.what());
			DCExchDB::CheckDBState(m_dbm, m_checktime);
            return -1;
        }

		m_iOperListId = vi_iOperListId;
    }

    m_buff = std::string(m_iSize,' ');

    return 0;
}

int DCOprStruct::SetBuff(std::string buff)
{
    m_buff = buff;
    return 0;
}



const char* DCOprStruct::GetCurrValue(const char *vi_sFieldCode)
{
    assert(vi_sFieldCode);

    STOprColInfo colInfo;
    int ret = GetColInfo(vi_sFieldCode, colInfo);
    if(-1 == ret)
    {
        return "\0";
    }

    if(colInfo.offset + colInfo.len > int(m_buff.size()))
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","invalid record buff[%s]", m_buff.c_str());
        return "\0";
    }

    m_curStr = m_buff.substr(colInfo.offset, colInfo.len);
    m_curStr.erase(0, m_curStr.find_first_not_of(' '));
    m_curStr.erase(m_curStr.find_last_not_of(' ') + 1);
    m_curStr += "\0";

    return (char*)m_curStr.c_str();
}

int DCOprStruct::SetCurrValue(const char *vi_sFieldCode, const char *vi_sValue)
{
    assert(vi_sFieldCode  && vi_sValue);
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","SetCurrValue[%s=%s]",vi_sFieldCode, vi_sValue);

    STOprColInfo colInfo;
    int ret = GetColInfo(vi_sFieldCode, colInfo);
    if(-1 == ret)
    {
        return -1;
    }

    std::string sSpace(colInfo.len,' ');
    m_buff.replace(colInfo.offset, colInfo.len, sSpace);

    //DC_OS::Trim(vi_sValue);
    int len = 0;
    if( colInfo.len < int(strlen(vi_sValue)))
    {
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","opr cfg len is too short,last value may not complete");
        len = colInfo.len;
    }
    else
    {
        len = strlen(vi_sValue);
    }
    m_buff.replace(colInfo.offset, len, vi_sValue, 0, len);

    return 0;
}

std::string DCOprStruct::GetBuff()
{
    return m_buff;
}

void DCOprStruct::ClearBuffer()
{
    m_buff = "";
}

int DCOprStruct::GetColInfo(const char *vi_sKey, STOprColInfo& colInfo)
{
    assert(vi_sKey);

    std::map<std::string, STOprColInfo>::iterator it;
    it = m_mapName2ColInfo.find(vi_sKey);
    if(it == m_mapName2ColInfo.end())
    {
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","invalid col name %s", vi_sKey);
        return -1;
    }
    colInfo = it->second;

    return 0;
}

void DCOprStruct::ShowAllCols()
{
    std::map<std::string, STOprColInfo>::iterator it;
    for(it = m_mapName2ColInfo.begin(); it != m_mapName2ColInfo.end(); ++it)
    {
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","[%s,%s]",it->first.c_str(), GetCurrValue(it->first.c_str()));
    }
}

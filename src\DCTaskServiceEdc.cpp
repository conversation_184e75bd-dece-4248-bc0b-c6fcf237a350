﻿#include "DCTaskServiceEdc.h"
#include "DCLogMacro.h"
#include "DCParseXml.h"
#include "UHead.h"

#include <ace/Guard_T.h>
#include <string>
#include <utility>
#include <string.h>
#include <unistd.h>
#include <time.h>
#include <sys/time.h>
#include <sched.h>
#include <iostream>
#include <fstream>
#include "DCPerfLogStatistic.h"

using namespace std;

int DCTaskServiceEdc::sm_nDone = 0;
int DCTaskServiceEdc::sm_nQueue = 0;
DCTQueue<STTask> DCTaskServiceEdc::sm_qTask;
map<string,int> DCTaskServiceEdc::sm_service;
time_t DCTaskServiceEdc::sm_checktime = time(NULL);

DCTaskServiceEdc::DCTaskServiceEdc()
{
	m_bexit = true;
	m_exchdb = NULL;
	m_client = NULL;
	m_file = NULL;
	//m_sTask = new DCTQueue<STTask>();
}

DCTaskServiceEdc::~DCTaskServiceEdc()
{
	//SAFE_DELETE_PTR(m_dmdb);
	if(m_file)
	{
		//m_file->Destroy();
		SAFE_DELETE_PTR(m_file);
	}

	SAFE_DELETE_PTR(m_exchdb);
}

void DCTaskServiceEdc::state(int done)
{
	sm_nDone = done;
}
void DCTaskServiceEdc::stateQueue(int done)
{
	sm_nQueue = done;
}
bool DCTaskServiceEdc::exit()
{
	return m_bexit;
}

int DCTaskServiceEdc::run()
{
	int ret = 0;
	ret = activate(THR_NEW_LWP | THR_JOINABLE | THR_INHERIT_SCHED | THR_SCOPE_SYSTEM, 1);
	if(ret < 0)
	{
		return -1;
	}
	return 0;
}

int DCTaskServiceEdc::init(DCDBManer* dmdb,dcf_new::DCFLocalClient *client, int threadNo)
{
	int ret = 0;

	m_dmdb = dmdb;
	m_exchdb = new DCExchDB(dmdb);
	if(!m_exchdb)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","new DCExchDB() faild.");
		return -1;
	}

	m_client = client;

	//分布式文件系统初始化
	
	SExchCfg* pcfg = DCExchCfg::instance()->GetConfig();
	m_cfg = pcfg;
	if(pcfg->iGetFileWay == 1)
	{
		m_file = new DCFile();
		if(!m_file)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","new DCFile() faild.");
			return -1;
		}	
	
		std::string sfileServ = pcfg->fileServ;
		size_t pos2 = sfileServ.rfind('/');
		if(std::string::npos==pos2)//没找到
		{
			char *szDFS =getenv("DFS_HOME");
			if(NULL==szDFS)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","read env DFS_HOME failed");
				return -1;
			}

			std::string subPath = szDFS;
			if(szDFS[strlen(szDFS)-1] != '/')
			{
				subPath += "/";
				subPath += pcfg->fileServ;
			}
			else
			{
				subPath += pcfg->fileServ;
			}
			sfileServ = subPath;

		}
		else
		{
			sfileServ = pcfg->fileServ;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","local file path %s",sfileServ.c_str());

		ret = m_file->Init(sfileServ.c_str());//, host, port);
		if(ret)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","FS client init failure,error info[%s]",m_file->GetErrorMsg());
			return -1;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Init DFS success!");
		
		char *szconfig =getenv("BILLING_CONFIG");
		if(NULL==szconfig)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","env BILLING_CONFIG not config");
			return -1;
		}
		std::string localfile =  pcfg->filepath;
		pos2 = localfile.rfind('/');
		if(std::string::npos==pos2)//没找到
		{
			std::string strLibPath=szconfig;
			int pos = strLibPath.rfind('/');
			std::string subPath=strLibPath.substr(0,pos+1);
			subPath += pcfg->filepath;
			m_szLocalPath = subPath;
		}
		else
		{
			m_szLocalPath = pcfg->filepath;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","local file path %s",m_szLocalPath.c_str());
	}
	else
	{
		m_szLocalPath = pcfg->filepath;
	}	

	m_nThreadNO = threadNo;
	return 0;
}

int DCTaskServiceEdc::svc()
{
	int ret = 0;
	STTask tasks;
	ACE_Time_Value expired;
	m_bexit = false;
	m_checktime = time(NULL);
	map<int, long>::iterator iter;
	int sendFailedTime = 0;
	int nPauseCnt(0);
	while(1)	// 可运行
	{
		SExchCfg* pcfg = DCExchCfg::instance()->GetConfig();
		if(m_cfg!=pcfg)
		{
			m_cfg = pcfg;
		}

		if (DCExchDB::CheckDBState(m_dmdb, m_checktime) < 0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "connected to database error!");
			continue;
		}

		DCPerfLogStatistic::instance()->output_timeing();

		if (m_cfg->nSysStateFlag == 1) // 暂停状态不读取任务不处理
		{
			sleep(1);
			if(sm_nDone == 2)
			{
				ret = ClearTask(m_dmdb);
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","getTask thread exit");
				break;
			}
			else
			{
				if ( 0 == (nPauseCnt++ % 600) )
				{
					DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "process state was set to 1, pausing");
					nPauseCnt = 0;
				}
				continue;
			}
		}

		if(0 == m_nThreadNO)
		{
			if(sm_nDone == 2)
			{
				ret = ClearTask(m_dmdb);
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","getTask thread exit");
				break;
			}
			if(sm_qTask.size() < 100)
			{
				DCPerfTimeVCollect collet(&m_tstat, true);
				collet.start();
				ret = GetTask(m_dmdb,tasks);	//从数据库获取
				collet.stop();

				// 输出统计信息
				if(DCLOG_GETLEVEL(DCLOG_CLASS_PERF) > 0 )
				{
					DCPERFLOG(DCLOG_CLASS_PERF, "msgexch.gettask=%ld",collet.m_usec);
				}
				if(sm_qTask.size() == 0)
				{
					sleep(1);
				}
			}
			else
			{
				sleep(1);
			}
			continue;
		}
		ret = sm_qTask.try_dequeue(tasks);	//从任务队列获取
		if(ret < 0)
		{
			expired.set(time(NULL)+5, 0);
			if(sm_qTask.dequeue(tasks, &expired) < 0)	//超时等待获取
			{
				if(sm_nDone == 2)		// 退出执行
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","thread exit");
					break;
				}

				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","task continue");
				continue;
			}
		}
		if(sm_nDone == 2)		// 退出执行
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","task exit");
			break;
		}
		ret = m_exchdb->MarkTaskDealingEdc(tasks.task_id);
		if(ret == 0)
		{
			DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "task_id[%d] is dealing,continue", tasks.task_id);
			continue;
		}
		DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "start deal task_id=[%d]", tasks.task_id);

		tasks.iFilewayMode = pcfg->iGetFileWay;

		DCPerfTimeVCollect collet1(&m_tstat, true);
		collet1.start();
		ret = DoTask(tasks);
		collet1.stop();

		// 输出统计信息
		if(DCLOG_GETLEVEL(DCLOG_CLASS_PERF) > 0 )
		{
			DCPERFLOG(DCLOG_CLASS_PERF, "msgexch.dotask=%ld",collet1.m_usec);
		}
		if(ret < 0)
		{
			// 处理失败，异常状态
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","task_id[%ld], deal failed, mark to exception,times[%d]",tasks.task_id,++sendFailedTime);
			ret = m_exchdb->MarkTaskToExceptionEdc(tasks.task_id);
			//记录文件稽核
			/*
			if(m_lograte.send_nr)
			{
			m_exchdb->RecordUidAll(tasks.latn_id,tasks.batch_id,tasks.lines,tasks.source_id);
			}
			*/
			if(0<m_cfg->nContinueFailExit && sendFailedTime>m_cfg->nContinueFailExit)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "%d continuous failures, ready to exit", m_cfg->nContinueFailExit);
				DCTaskServiceEdc::state(2);
			}
		}
		else
		{
			sendFailedTime = 0;
			// 发送成功，更新状态为M0A；接收到应答之后，接收到所有的应答消息，才将source_files状态更新为M0F，同时删除task_manager表
			DCBIZLOG(DCLOG_LEVEL_INFO,0,""," task_id[%ld], source_name[%s],deal success.",tasks.task_id, tasks.source_name);
			m_exchdb->MarkTaskToDoneEdc(tasks.task_id);
			//记录文件稽核
		}

		DCBIZLOG(DCLOG_LEVEL_INFO,0,""," task_id[%ld], deal done",tasks.task_id);
	}
	m_bexit = true;
	return 0;
}

int DCTaskServiceEdc::DoTask(const STTask& task)
{
	DCPerfTimeVCollect collet(DCPerfLogStatistic::instance()->GetPerfTimeStats("APP", "DoTask", m_dmdb), true);

	DCBIZLOG(DCLOG_LEVEL_INFO,0,"","start to deal file[%ld]",task.task_id);

	string filename = task.source_name;
	vector <string> vServiceName;
	int serviceNum = GetServiceName(task.fileType,vServiceName);
	if(serviceNum <= 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","can not get serviceNum:%d",serviceNum);
		return -1;
	}
	for(int i = 0; i < serviceNum; i++)
	{
		if(sm_service.find(vServiceName[i]) == sm_service.end())
		{
			if(m_client->subscribeService(vServiceName[i]) < 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","subscribeService:%s failed",vServiceName[i].c_str());
				return -1;
			}
			else
			{
				sm_service.insert(make_pair(vServiceName[i],1));
			}
		}
	}
	DCPerfTimeVCollect collet_dfs(DCPerfLogStatistic::instance()->GetPerfTimeStats("APP", "DoTask"), true);

	bool exist = false;

	std::size_t found = filename.find_last_of("/");
	std::string file = filename.substr(found + 1);

	std::string strLocalFile = m_szLocalPath + file;
	int ret = 0;
	if(task.iFilewayMode == 1)
	{	
		int ret = m_file->IsExistFile(filename.c_str(),exist);
		if(!exist || ret !=0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","file  %s is not exist",filename.c_str());
			return -1;
		}

	    if (access(strLocalFile.c_str(), F_OK) == 0)
	    {
	        remove(strLocalFile.c_str());
	    }
	
		ret = m_file->DownloadToFile(filename.c_str(), m_szLocalPath.c_str());
		if(ret != 0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","read file content fail,fileName[%s],error info[%s]",filename.c_str(),m_file->GetErrorMsg());
			return -1;
		}
	}
	else if (task.iFilewayMode == 2)
	{
		if (access(filename.c_str(), F_OK) != 0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","file  %s is not exist",filename.c_str());
			return -1;
		}

		CopyFile(filename.c_str(), strLocalFile.c_str());
	}

	collet_dfs.stop();

	DCBIZLOG(DCLOG_LEVEL_INFO,0,"","parse local file[%s]",strLocalFile.c_str());
	ifstream ifs(strLocalFile.c_str());
	string sline;
	int line = 0;
	if(!ifs)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","parse file error");
		return -1;
	}


	long lsendMsgCount = m_cfg->lsendMsgCount;
	int  sleeptime = m_cfg->sleeptime;
	string sendmsg="";
	int count = 0;
	ofstream fBreakPoint;
	char bakPath[256] = {0};
	int breakLine = 0;
	sprintf(bakPath,"%s%ld.bak",m_cfg->bakFilePath.c_str(),task.task_id);
	if(access(bakPath,F_OK) == 0)
	{
		ifstream ifBreakPoint(bakPath);
		if(ifBreakPoint.is_open())
		{
			ifBreakPoint>>breakLine;
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","read break file[%s],line:%d",bakPath,breakLine);
		}
		ifBreakPoint.close();
	}
	fBreakPoint.open(bakPath);
	while(ifs && getline(ifs, sline))
	{
		line++;
		if(breakLine > 0)
		{
			breakLine--;
			continue;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","read file[%s],line:%d",sline.c_str(),line);

		DCPerfTimeVCollect collet_cdr(DCPerfLogStatistic::instance()->GetPerfTimeStats("APP", "ToMsg"), true);

		sendmsg = "|" ;
		sendmsg = sendmsg + sline;
		if(line % 100 == 0)
		{
			while(sm_nQueue == 1)
			{
				usleep(100);
			}
		}
		for(int i = 0; i < serviceNum; i++)
		{
			ret = m_client->invokeAsync(vServiceName[i], "", sendmsg,"",1);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "send CCR ,service:%s", vServiceName[i].c_str());

			if(ret < 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "send failed\n");
				ifs.close();
				fBreakPoint.close();
				return -1;
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","send msg: %s",sendmsg.c_str());
		}
		count++;
		fBreakPoint.seekp(0, ios::beg);
		fBreakPoint<<line;
		fBreakPoint.flush();

		collet_cdr.stop();

		if(lsendMsgCount && line%lsendMsgCount==0)
		{
			usleep(sleeptime);
		}
	}
	ifs.close();
	fBreakPoint.close();
	remove(strLocalFile.c_str());
	remove(bakPath);
	DCBIZLOG(DCLOG_LEVEL_INFO,0,"","msgNum: %d,taskLines:%ld,task_id[%ld]", count, task.lines,task.task_id);

	collet.stop();

	return 0;
}

int DCTaskServiceEdc::GetTask(DCDBManer *dmdb,STTask& otask)
{
	//DCBIZLOG(DCLOG_LEVEL_DEBUG "", "before try_dequeue");
	int pid = getpid();
	int threadid =gettid();
	dmdb->FastReset();
	char buf[128]={0};
	sprintf(buf,"%d-%d",pid,threadid);
	DCPerfTimeStats  tstat;         //性能统计
	DCPerfTimeVCollect collet(&tstat, true);
	int row = 0;

	while (1)
	{
		UDBSQL* update = dmdb->GetSQL("UpdateTaskToDealEdc");
		try
		{
			update->UnBindParam();
			update->BindParam(1,buf);
			update->Execute();
			row = update->GetRowCount();
			update->Connection()->Commit();
		}
		catch(UDBException& e)
		{
			std::string sql;
			update->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "SQL[%s]", sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "update execption[%s]", e.ToString());
			int _ret = DCExchDB::CheckDBState(dmdb, sm_checktime);
			if (_ret != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", _ret);
				continue;
			}
			else
			{
				return -2;
			}
		}

		break;
	}

	collet.stop();

	// 输出统计信息
	DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "UpdateTask usec[%ld]", collet.m_usec);

	if(row>0)
	{
		DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "UpdateTask rownum[%d]", row);
	}

	collet.m_usec=0;
	collet.start();

	STTask task;
	while (1)
	{
		UDBSQL* query = dmdb->GetSQL("QueryTaskEdc");
		int num = 0;
		try
		{
			query->UnBindParam();
			query->BindParam(1,buf);
			query->Execute();
			while(query->Next())
			{
				query->GetValue(1, task.task_id);
				query->GetValue(2, task.source_name);
				query->GetValue(3, task.lines);
				query->GetValue(4, task.fileType);
				num++;
				sm_qTask.enqueue(task);
			}

			DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "getRecordNum[%d]", num);
		}
		catch(UDBException& e)
		{
			std::string sql;
			query->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "SQL[%s]", sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "query execption[%s]", e.ToString());
			int _ret = DCExchDB::CheckDBState(dmdb, sm_checktime);
			if (_ret != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", _ret);
				continue;
			}
			else
			{
				return -1;
			}
		}

		break;
	}
	
	collet.stop();
	DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "QueryTaskEdc usec[%ld]", collet.m_usec);
	return 0;
}
int DCTaskServiceEdc::ClearTask(DCDBManer *dmdb)
{
	int pid = getpid();
	int threadid =gettid();
	char buf[128]={0};
	sprintf(buf,"%d-%d",pid,threadid);
	int row = 0;

	while (1)
	{
		UDBSQL* pUpdate = dmdb->GetSQL("ClearTask");
		if(pUpdate == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "getSQL[ClearTask] error");
		}
		try
		{
			pUpdate->UnBindParam();
			pUpdate->BindParam(1, buf);
			pUpdate->Execute();
			row = pUpdate->GetRowCount();
			pUpdate->Connection()->Commit();
			DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "clear task,update row[%d]",row);
		}
		catch(UDBException& e)
		{
			std::string sql;
			pUpdate->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "SQL[%s]", sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "update execption[%s]", e.ToString());
			int _ret = DCExchDB::CheckDBState(dmdb, m_checktime);
			if (_ret != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", _ret);
				continue;
			}
			else
			{
				return -1;
			}
		}

		break;
	}
	
	return 0;
}

int DCTaskServiceEdc::GetServiceName(const char * fileType,std::vector<string> &v)
{
	int num = 0;
	string serviceName = "";
	int eventId = 0;

	while (1)
	{
		UDBSQL* pQuery = m_dmdb->GetSQL("GetEdcEventId");
		if(pQuery == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "getSQL[GetEdcEventId] error");
		}
		try
		{
			pQuery->UnBindParam();
			pQuery->BindParam(1,fileType);
			pQuery->Execute();
			while(pQuery->Next())
			{
				pQuery->GetValue(1,eventId);
				char buf[32] = {0};
				sprintf(buf,"service-%d",eventId);
				serviceName = buf;
				v.push_back(serviceName);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "get eventId [%d]",eventId);
				num++;
			}
			if(!num)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "can not get eventId by fileType[%s]",fileType);
			}
		}
		catch(UDBException& e)
		{
			std::string sql;
			pQuery->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "SQL[%s]", sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "execption[%s]", e.ToString());
			int _ret = DCExchDB::CheckDBState(m_dmdb, m_checktime);
			if (_ret != 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "reconnect db, ret=%d", _ret);
				continue;
			}
			else
			{
				num = -1;
			}
		}

		break;
	}
	
	return num;
}

int DCTaskServiceEdc::CopyFile(const char* pSrcPath, const char* pDesPath)
{
    FILE* fpSrcFile = fopen(pSrcPath, "r");
    if (!fpSrcFile)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "CopyFile Failed, Open SrcFilePath[%s] Failed.", pSrcPath);
        return -1;
    }

    FILE* fpDesFile = fopen(pDesPath, "w+");
    if (!fpDesFile)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "CopyFile Failed, Open DesFilePath[%s] Failed.", pDesPath);
		fclose(fpSrcFile);
        return -1;
    }

    char buff[4096] = { 0 };
    while (fgets(buff, sizeof(buff), fpSrcFile))
    {
        fputs(buff, fpDesFile);
    }

    fclose(fpSrcFile);
    fclose(fpDesFile);

    return 0;
}


#ifndef __GRAYSCALEROUTE_H__
#define __GRAYSCALEROUTE_H__
#include <queue>
#include <map>
#include <list>
#include <string>
#include <stdio.h>
#include <sys/time.h>
#include <pthread.h>
#include <stdlib.h>

#include "DCFLocalClientNew.h"
#include "DCFClientLogNew.h"

#include "DCDBManer.h"

using namespace std;
using std::map;
using std::multimap;
using std::vector;
using std::string;


//==========================错误码=================================
#define ERR_NOT_FIND_SERVICE_NAME   (1000)    //获取服务名称失败
#define ERR_NOT_FIND_RECORD         (1001)    //找不到数据
#define ERR_NOT_FIND_SQL            (-1002)    //找不到SQL
#define ERR_NOT_FIND_INMAPPARAM     (-1003)    //输入参数为空




//==========================路由码==================================
#define ROUTE_CODE_BILLING_NUM    "BillingNum"   //计费号码
#define ROUTE_CODE_SECTION_NUM    "SectionNum"   //号段
#define ROUTE_CODE_LATN_ID        "LatnID"       //本地网
#define ROUTE_CODE_OPER_TYPE      "OperType"     //业务类型
#define ROUTE_CODE_ACCT_ID        "ACCTID"       //账户ID
#define ROUTE_CODE_CUST_ID        "CUSTID"       //客户ID
#define ROUTE_CODE_OFFERINST_ID   "OFFERINSTID"  //销售品实例ID
#define ROUTE_CODE_OFFER_ID       "OFFERID"      //销售品ID
#define ROUTE_CODE_IMSI           "Imsi"         //Imsi号

enum EActiveData
{
	ACTIVE_DATA_NULL = 0,
	ACTIVE_DATA_MASTER,
	ACTIVE_DATA_SLAVE
};


typedef struct
{
	long        Id;             //序列号
 	long        ParentId;       //父序列号
 	long        Priority;       //优先级
 	string      RouteCode;      //路由号码类型
	string      RouteValueA;    //路由具体值，也可是路由范围的左值
	string      RouteValueB;    //路由范围的右值, 非必填 
	string      ServiceName;    //服务名称, 例如：RatingServie,ctg_service_551
	string      Subscriber;     //订购路由规则的模块
	string      RouteProcess;   //路由环节
	int         RouteStatus;    //状态

	
} St_route_service_cfg;

typedef struct
{
	long        GroupId;        //组序列号
 	string      Imsi;           //Imsi号  陕西版不用
 	string      Accnumber;      //用户号码     陕西版不用
 	long        Acctid;         //账户id 陕西版不用
 	string      sValue;         //陕西版: 对应Imsi号或用户号码或账户id
	int         RouteStatus;    //状态

} St_grey_user_group_cfg;


struct St_grayscale_route_node
{
	St_route_service_cfg tRoute;
	list <St_grayscale_route_node> listChild;  //按优先级顺序
	St_grayscale_route_node * pParent;           //父节点

	bool InsertNode(St_grayscale_route_node &v_Node);
};

typedef struct
{
    //存储的是中间节点对应的子节点,这个子节点不一定是叶子节点
    multimap <long , St_grayscale_route_node>              m_mapNode;  // long 是 Id;

    //存的是根节点打头的树,有可能是完整的树,有可能(根节点树+中间节点树, 这和数据查询出的先后有关,如果是按树型结构配置,则保存的是一个完整树)
    multimap <long , St_grayscale_route_node>              m_mmpRoot;  // long 是 ParentId;
/*
    //节点有: -1, 26, 27, 28,29,30,33,树型如下:
                        -1
                      /    \
                   26      27
                      \    /
                       28
                     /    \
                   29     30
                           |
                          33
    在m_mmpRoot中存储的就是多棵完整的树,每种业务只有一棵树
    m_mapNode中存储的是二个父子节点对,如下:
          26     27          28         30
           |     |          /  \         |
          28     28       29    30      33

*/
    void clear()
    {
        m_mapNode.clear();
        m_mmpRoot.clear();
    
    }
} St_grayscale_route_tree;

typedef struct
{
    int         scene_id;       // 场景ID
    string      scene_name;     // 场景名称
    string      version;        // 版本号
    int         center_id;      // 中心ID
    string      app_type;       // 应用类型
    int         status;         // 状态
    int         approval_status;// 审批状态
    string      create_time;    // 创建时间
} St_route_scene;

typedef struct
{
    int         rule_id;        // 规则ID
    string      rule_name;      // 规则名称
    string      version;        // 版本号
    int         scene_id;       // 场景ID
    string      env_code;       // 环境编码
    string      dispatch_path;  // 服务名
    string      client_code;    // 客户端程序名
    string      topology_name;  // topology图名
    int         priority;       // 优先级
} St_route_rule;

typedef struct
{
    int         cond_id;        // 条件ID
    int         rule_id;        // 规则ID
    string      version;        // 版本号
    int         group_id;       // 分组ID
    string      route_type_id;  // 路由类型ID
    string      operator_str;   // 操作符
    string      value;          // 条件值
} St_route_condition;

typedef struct
{
    int         route_type_id;  // 路由类型ID
    string      route_type_code;// 路由类型编码
    string      route_type_name;// 路由类型名称
} St_route_type;

typedef struct
{
    string      env_code;       // 环境编码
    string      env_addr;       // 环境地址
    string      env_path;       // 环境路径
    string      user_name;      // 用户名
    string      password;       // 密码
    dcf_new::DCFLocalClient* pDCFClient; // DCF客户端
    bool        need_init;      // 是否需要初始化的标识
} St_route_env;

/*灰度路由*/
class DCGrayRoute 
{
public:
	DCGrayRoute();

	virtual ~DCGrayRoute() = 0;

	/*加载数据*/
	virtual int init(DCDBManer* pdbm, const char * pSubscriber) = 0;

	/*加载属性*/
	virtual int Set(map<string,string> &inMapParam) = 0;

	/*获取路由规则增强的服务名称*/
	virtual int GetRouteServiceName(const char * pRouteProcess, map<string,string> &inMapParam, string &sServiceName) = 0;
	
	/*获取DCF客户端*/
	virtual int GetRouteServiceNameDCFClient(const char * pRouteProcess, map<string,string> &inMapParam, string &sServiceName, dcf_new::DCFLocalClient* &pDCFClient) = 0;

	/*业务模块初始化DCF客户端*/
    virtual int InitDCFClient(const char * pRouteProcess, const char * pEnvCode, dcf_new::DCFLocalClient* pDCFClient) = 0;
	
	/*获取路由环节对应的所有环境配置*/
    virtual int GetRouteEnvs(const char * pRouteProcess, vector<St_route_env> &routeEnvs) = 0;
};



/*灰度路由*/
class DCGrayscaleRoute : public DCGrayRoute
{
public:
	DCGrayscaleRoute();

	virtual ~DCGrayscaleRoute();

	static DCGrayscaleRoute* instance();

	/*加载数据*/
	virtual int init(DCDBManer* pdbm, const char * pSubscriber);

	/*加载属性*/
	virtual int Set(map<string,string> &inMapParam);

	/*获取路由规则增强的服务名称*/
    virtual int GetRouteServiceName(const char * pRouteProcess, map<string,string> &inMapParam, string &sServiceName);
    
    /*获取DCF客户端*/
    virtual int GetRouteServiceNameDCFClient(const char * pRouteProcess, map<string,string> &inMapParam, string &sServiceName, dcf_new::DCFLocalClient* &pDCFClient);
    
    /*业务模块初始化DCF客户端*/
    virtual int InitDCFClient(const char * pRouteProcess, const char * pEnvCode, dcf_new::DCFLocalClient* pDCFClient);
    
    /*获取路由环节对应的所有环境配置*/
    virtual int GetRouteEnvs(const char * pRouteProcess, vector<St_route_env> &routeEnvs);

private:
    int InitRouteServiceCfgTree(DCDBManer* pdbm, const char * pSubscriber, St_grayscale_route_tree *p_Data);
	int InitGreyUserGroupCfg(DCDBManer* pdbm, multimap<long, St_grey_user_group_cfg> *p_GreyUserGroupCfg);
    int AnalyzeRouteServiceCfgTree(St_grayscale_route_tree * pData, multimap<long, St_grey_user_group_cfg> *pGreyUserGroupCfg, string &sServiceName);
	int AnalyzeRouteServiceCfgTree(St_grayscale_route_tree * pData, multimap<long, St_grey_user_group_cfg> *pGreyUserGroupCfg, St_grayscale_route_node &v_Node, string &sServiceName);
    int LoadData(DCDBManer* pdbm, const char * pSubscriber);
	int ChangeActiveStatus();
	int clearData();
	int SetData(map<string,string> &inMapParam);
	
	//跨集群获取增强的服务名和dcf客户端
	int LoadCrossClusterData(DCDBManer* pdbm, const char * pSubscriber);
	int LoadSceneData(DCDBManer* pdbm, const char * pSubscriber);
	int LoadRuleData(DCDBManer* pdbm, const char * pSubscriber);
	int LoadConditionData(DCDBManer* pdbm);
	int LoadTypeData(DCDBManer* pdbm);
	int LoadEnvData(DCDBManer* pdbm);
	int LogRefreshResult(DCDBManer* pdbm, int result);
	int CompareSceneVersion(map<int, St_route_scene> &newScenes, map<int, St_route_scene> &oldScenes, string &changeInfo);
	int AnalyzeRouteRules(const char * pRouteProcess, map<string,string> &inMapParam, string &sServiceName, string &env_code);
	bool CompareCondition(const string &route_type_code, const string &operator_str, const string &cond_value, const string &input_value);

private:
    //类的指针
    static DCGrayscaleRoute *m_pGrayscaleRoute;	

	list<St_grayscale_route_node > m_TreeData;
	EActiveData m_activeData;
	St_grayscale_route_tree m_Master_Data;
	St_grayscale_route_tree m_Slave_Data;
	multimap<long, St_grey_user_group_cfg> m_Master_GreyUserGroupCfg;
	multimap<long, St_grey_user_group_cfg> m_Slave_GreyUserGroupCfg;

    //订购路由规则的模块
	std::string m_sSubscriber;
	//路由环节
	std::string m_sRouteProcess;
	
	//用户号码
	std::string m_sAccnumber;
	//号段
	std::string m_sSectionNum;
	//Imsi号
	std::string m_sImsi;
	//账户
	long m_lAcctid;
	//客户
	long m_lCustid;
	//销售品
	long m_lOfferid;
	//销售品实例
	long m_lOfferinstid;
	//本地网
	int m_nLatnid;
	//业务类型
	std::string m_sOperType;
	
	// 跨集群主数据
	map<int, St_route_scene> m_Master_Scenes;
	map<string, vector<St_route_rule> > m_Master_Rules;  // KEY = 客户端编码client_code+topology_name
	map<int, vector<St_route_condition> > m_Master_Conditions; // KEY = rule_id
	map<string, St_route_type> m_Master_Types; // KEY = route_type_id
	map<string, St_route_env> m_Master_Envs;   // KEY = env_code
	map<string, St_route_env> m_Master_DcfClients; // KEY = client_code|topology_name|env_code
	
	// 跨集群备数据
	map<int, St_route_scene> m_Slave_Scenes;
	map<string, vector<St_route_rule> > m_Slave_Rules;
	map<int, vector<St_route_condition> > m_Slave_Conditions;
	map<string, St_route_type> m_Slave_Types;
	map<string, St_route_env> m_Slave_Envs;
	map<string, St_route_env> m_Slave_DcfClients;
	
	// 跨集群场景变化信息
	string m_sSceneChangeInfo;

};



#endif // __GRAYSCALEROUTE_H__

